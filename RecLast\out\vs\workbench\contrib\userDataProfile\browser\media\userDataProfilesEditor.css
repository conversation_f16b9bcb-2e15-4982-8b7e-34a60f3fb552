/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.profiles-editor {
	height: 100%;
	overflow: hidden;
	max-width: 1200px;
	margin: 20px auto 0px auto;
}

.profiles-editor .sidebar-view,
.profiles-editor .contents-view {
	height: 100%;
}

.profiles-editor > .monaco-split-view2 > .sash-container,
.profiles-editor > .monaco-split-view2.separator-border.horizontal > .monaco-scrollable-element > .split-view-container > .split-view-view:not(:first-child)::before {
	top: 55px;
}

.profiles-editor .contents-container {
	height: 100%;
}

.profiles-editor .sidebar-container {
	padding-left: 20px;
	height: 100%;
}

.profiles-editor .sidebar-container .new-profile-button {
	padding: 0px 20px 0px 18px;
	display: flex;
	align-items: center;
	height: 40px;
}

.profiles-editor .sidebar-container .new-profile-button > .monaco-button-dropdown {
	flex-grow: 1;
}

.profiles-editor .monaco-button-dropdown > .monaco-dropdown-button {
	display: flex;
	align-items: center;
	padding: 0 4px;
}

.profiles-editor .monaco-list-row .profile-tree-item-actions-container {
	display: none;
	align-items: center;
}

.profiles-editor .monaco-list-row.focused .profile-tree-item-actions-container,
.profiles-editor .monaco-list-row.selected .profile-tree-item-actions-container,
.profiles-editor .monaco-list-row:hover .profile-tree-item-actions-container {
	display: flex;
}

.profiles-editor .sidebar-container .profiles-list {
	margin-top: 15px;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item {
	padding-left: 20px;
	display: flex;
	align-items: center;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item > * {
	margin-right: 5px;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item > .profile-list-item-label {
	overflow: hidden;
	text-overflow: ellipsis;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item > .profile-list-item-label.new-profile {
	font-style: italic;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item > .profile-list-item-description {
	margin-left: 2px;
	display: flex;
	align-items: center;
	font-size: 0.9em;
	opacity: 0.7;
}

.profiles-editor .sidebar-container .profiles-list .profile-list-item .profile-tree-item-actions-container {
	flex: 1;
	justify-content: flex-end;
	margin-right: 10px;
}

.profiles-editor .hide {
	display: none !important;
}

.profiles-editor .contents-container .profile-header {
	margin-left: 27px;
	display: flex;
	height: 40px;
	align-items: center;
}

.profiles-editor .contents-container .profile-header .profile-title-container {
	font-size: 26px;
	font-weight: 600;
}

.profiles-editor .contents-container .profile-title-container .monaco-inputbox {
	margin-right: 10px;
	flex: 1;
}

.profiles-editor .contents-container .profile-header .profile-actions-container .actions-container .action-label {
	padding: 6px;
}

.profiles-editor .contents-container .profile-body {
	margin: 15px 0px 0px 1px
}

.profiles-editor .contents-container .profile-body .profile-row-container {
	padding: 12px 0px;
}

.profiles-editor .contents-container .profile-body .profile-row-container.no-padding-bottom {
	padding-bottom: 0px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-label-element {
	font-weight: 600;
	padding-bottom: 5px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-description-element {
	color: var(--vscode-foreground);
	opacity: 0.9;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-description-element ul,
.profiles-editor .contents-container .profile-body .profile-row-container .profile-description-element p {
	margin: 0px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-description-element ul {
	padding-inline-start: 28px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-description-element ul li {
	padding-left: 2px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .monaco-inputbox {
	width: 400px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-icon-container {
	line-height: 22px;
	display: flex;
	align-items: center;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-icon-container.disabled .codicon {
	cursor: default;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-icon-container .codicon {
	cursor: pointer;
	margin-right: 4px;
	padding: 2px;
	border-radius: 5px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-icon-container:not(.disabled) .codicon:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
	outline: 1px dashed var(--vscode-toolbar-hoverOutline);
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-icon-container .profile-description-element {
	margin-top: -1px;
}

.profiles-editor .contents-container .profile-select-container {
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
}

.profiles-editor .contents-container .profile-select-container > .monaco-select-box {
	cursor: pointer;
	line-height: 18px;
	padding: 0px 23px 0px 8px;
	border-radius: 2px;
}

.profiles-editor .contents-container .profile-copy-from-container .profile-select-container {
	margin-top: 5px;
	width: 250px;
}

.profiles-editor .contents-container .profile-use-as-default-container,
.profiles-editor .contents-container .profile-use-for-current-container {
	display: flex;
	align-items: center;
	line-height: 22px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-use-as-default-container .profile-description-element,
.profiles-editor .contents-container .profile-body .profile-row-container .profile-use-for-current-container .profile-description-element {
	margin-top: -1px;
}

.profiles-editor .contents-container .profile-use-as-default-container .profile-use-as-default-label,
.profiles-editor .contents-container .profile-use-for-current-container .profile-use-as-default-label {
	margin-left: 2px;
}

.profiles-editor .contents-container .profile-contents-container {
	margin: 0px 0px 10px 20px;
}

.profiles-editor .contents-container .profile-content-tree-header {
	display: grid;
	grid-template-columns: 30px repeat(2, 1fr) 80px;
	line-height: 22px;
	height: 30px;
	align-items: center;
	margin: 5px 0px 2px 0px;
	font-weight: bold;
	border-bottom: 1px solid transparent;
}

.profiles-editor .profile-associations-table .monaco-table-th,
.profiles-editor .contents-container .profile-content-tree-header {
	background-color: var(--vscode-keybindingTable-headerBackground);
}

.profiles-editor .contents-container .profile-content-tree-header > .options-header {
	display: flex;
	align-items: center;
}

.profiles-editor .contents-container .profile-content-tree-header > .options-header .codicon {
	cursor: pointer;
	padding-left: 2px;
}

.profiles-editor .contents-container .profile-content-tree-header.default-profile {
	grid-template-columns: 30px repeat(1, 1fr) 80px;
}

.profiles-editor .contents-container .profile-content-tree-header.default-profile > .options-header {
	display: none;
}

.profiles-editor .contents-container .profile-tree-item-container {
	display: grid;
	align-items: center;
	grid-template-columns: repeat(2, 1fr) 80px;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree-header,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree {
	margin-right: 1px;
}

.profiles-editor .contents-container .profile-tree-item-container.default-profile,
.profiles-editor .contents-container .profile-tree-item-container.profile-resource-child-container {
	grid-template-columns: repeat(1, 1fr) 80px;
}

.profiles-editor .contents-container .profile-tree-item-container .profile-resource-type-description {
	margin-left: 10px;
	font-size: 0.9em;
	opacity: 0.7;
}

.profiles-editor .contents-container .profile-tree-item-container .profile-resource-options-container {
	height: fit-content;
	width: fit-content;
}

.profiles-editor .contents-container .profile-tree-item-container .monaco-custom-radio .monaco-button {
	outline-offset: -1px !important;
}

.profiles-editor .contents-container .profile-tree-item-container .profile-resource-actions-container {
	display: none;
	justify-self: end;
	padding-right: 10px;
}

.profiles-editor .contents-container .profile-content-tree .monaco-list-row.selected .profile-resource-actions-container,
.profiles-editor .contents-container .profile-content-tree .monaco-list-row.focused .profile-resource-actions-container,
.profiles-editor .contents-container .profile-content-tree .monaco-list-row:hover .profile-resource-actions-container {
	display: flex;
}

.profiles-editor .contents-container .profile-body .profile-row-container.profile-button-container {
	margin-top: 10px;
	margin-left: 30px;
	display: flex;
	align-items: center;
}

.profiles-editor .contents-container .profile-body .profile-row-container.profile-button-container .monaco-button {
	margin-right: 10px;
	width: inherit;
	padding: 4px 10px;
}

.profiles-editor .profile-associations-table {
	margin-top: 5px;
}

.profiles-editor .profile-associations-table .monaco-table-th,
.profiles-editor .profile-associations-table .monaco-table-td {
	padding: 0px 5px;
}

.profiles-editor .profile-associations-table .monaco-table-td {
	display: flex;
	align-items: center;
	overflow: hidden;
}

.profiles-editor .profile-associations-table .monaco-list-row .monaco-table-tr .monaco-table-td .profile-workspaces-actions-container {
	display: none;
	justify-content: flex-end;
	padding-right: 5px;
}

.profiles-editor .profile-associations-table .monaco-list-row.selected .monaco-table-tr .monaco-table-td .profile-workspaces-actions-container,
.profiles-editor .profile-associations-table .monaco-list-row.focused .monaco-table-tr .monaco-table-td .profile-workspaces-actions-container,
.profiles-editor .profile-associations-table .monaco-list-row:hover .monaco-table-tr .monaco-table-td .profile-workspaces-actions-container {
	display: flex;
}

.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .host,
.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .path {
	width: 100%;
}

.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .host .host-label,
.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .path .path-label {
	overflow: hidden;
	text-overflow: ellipsis;
}

.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .current-workspace .path-label,
.profiles-editor .profile-associations-table .monaco-table-tr .monaco-table-td .current-workspace .host-label {
	font-weight: bold;
	font-style: italic;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-workspaces-button-container {
	display: flex;
	margin: 8px 4px;
}

.profiles-editor .contents-container .profile-body .profile-row-container .profile-workspaces-button-container .monaco-button {
	width: inherit;
	padding: 2px 14px;
}

/* Profile Editor Tree Theming */

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row.focused {
	background-color: var(--vscode-settings-focusedRowBackground) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row[data-parity=odd] .monaco-table-tr {
	background-color: transparent !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row:not(.focused):hover {
	background-color: var(--vscode-settings-rowHoverBackground) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list:focus .monaco-list-row.focused {
	outline: 1px solid var(--vscode-settings-focusedRowBorder) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row {
	cursor: default;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row {
	cursor: default;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row.focused,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row.selected:not(:focus),
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row.selected:focus,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row.focused,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row.selected:not(:focus),
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row.selected:focus {
	background-color: inherit !important;
	color: inherit !important;
}

.monaco-workbench:not(.hc-black):not(.hc-light) .profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row:hover:not(.selected),
.monaco-workbench:not(.hc-black):not(.hc-light) .profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row:hover:not(.selected) {
	background-color: var(--vscode-list-hoverBackground) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row.selected:focus,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row.selected:focus {
	background-color: var(--vscode-list-activeSelectionBackground) !important;
	color: var(--vscode-list-activeSelectionForeground) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-associations-table .monaco-list-row.selected:not(:focus),
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row.selected:not(:focus) {
	background-color: var(--vscode-list-inactiveSelectionBackground) !important;
	color: var(--vscode-list-inactiveSelectionForeground) !important;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row .monaco-tl-twistie.collapsible {
	cursor: pointer;
}

.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .codicon,
.profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row .profile-content-tree .monaco-list-row .codicon {
	color: inherit !important;
}

.monaco-workbench:not(.reduce-motion) .profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row:hover .profile-content-tree-header,
.monaco-workbench:not(.reduce-motion) .profiles-editor .contents-container .profile-body .profile-tree .monaco-list-row:hover .profile-associations-table .monaco-table > .monaco-split-view2 {
	border-color: var(--vscode-tree-tableColumnsBorder) !important;
}
