{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,0DAAuD;AACvD,wDAAqD;AACrD,0DAAuD;AACvD,iDAA8C;AAC9C,0DAAuD;AAEvD,IAAI,eAAgC,CAAC;AACrC,IAAI,YAA0B,CAAC;AAC/B,IAAI,aAA4B,CAAC;AACjC,IAAI,UAAsB,CAAC;AAC3B,IAAI,aAA4B,CAAC;AAE1B,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC3D,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,sBAAsB;IACtB,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;IACpC,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;IACzC,aAAa,GAAG,IAAI,6BAAa,CAAC,OAAO,CAAC,CAAC;IAC3C,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,CAAC;IAErC,yBAAyB;IACzB,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IAEvG,4BAA4B;IAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CACrC,kBAAkB,EAClB,eAAe,EACf;QACI,cAAc,EAAE;YACZ,uBAAuB,EAAE,IAAI;SAChC;KACJ,CACJ,CACJ,CAAC;IAEF,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,sBAAsB;IACtB,IAAI;QACA,MAAM,YAAY,CAAC,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;KAC9E;IAAC,OAAO,KAAK,EAAE;QACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;KAC5E;AACL,CAAC;AAnCD,4BAmCC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC7E,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAChC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE;QACjF,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC/E,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACzF,aAAa,CAAC,cAAc,EAAE,CAAC;QAC/B,eAAe,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC/F,IAAI;YACA,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAClD,MAAM,EAAE,oBAAoB;gBAC5B,WAAW,EAAE,aAAa;aAC7B,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE;gBACd,MAAM,aAAa,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,aAAa,YAAY,gBAAgB,CAAC,CAAC;aACnF;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;SAC5E;IACL,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,qBAAqB,EACrB,qBAAqB,CACxB,CAAC;AACN,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;IACD,IAAI,UAAU,EAAE;QACZ,UAAU,CAAC,OAAO,EAAE,CAAC;KACxB;AACL,CAAC;AAVD,gCAUC"}