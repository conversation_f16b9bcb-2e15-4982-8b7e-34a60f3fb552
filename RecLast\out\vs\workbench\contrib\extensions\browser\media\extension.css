/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.extension-bookmark-container {
	position: relative;
}

.extension-bookmark-container > .extension-bookmark {
	position: absolute;
}

.extension-list-item {
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	padding: 0 0 0 16px;
	overflow: hidden;
	display: flex;
}

.extension-list-item > .icon-container {
	position: relative;
	display: flex;
	align-items: center;
}

.extension-list-item > .icon-container .extension-badge {
	position: absolute;
	bottom: 5px;
}

.extension-list-item > .icon-container .extension-badge.extension-remote-badge {
	width: 22px;
	height: 22px;
	line-height: 22px;
	border-radius: 20px;
	right: 5px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.extension-list-item > .icon-container .extension-remote-badge .codicon {
	color: currentColor;
}

.extension-list-item > .details {
	flex: 1;
	display: flex;
	overflow: hidden;
	flex-direction: column;
	justify-content: center;
}

.extension-list-item > .details > .header-container {
	height: 19px;
	display: flex;
	overflow: hidden;
	padding-right: 11px;
}

.extension-list-item > .details > .header-container > .header {
	display: flex;
	align-items: center;
	flex-wrap: nowrap;
	overflow: hidden;
	flex: 1;
	min-width: 0;
}

.extension-list-item > .details > .header-container > .header > .name {
	font-weight: bold;
	flex: 1;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.extension-list-item.deprecated > .details > .header-container > .header > .name {
	text-decoration: line-through;
}

.extension-list-item > .details > .header-container > .header > .activation-status,
.extension-list-item > .details > .header-container > .header > .install-count,
.extension-list-item > .details > .header-container > .header .extension-kind-indicator,
.extension-list-item > .details > .header-container > .header > .ratings {
	display: flex;
	align-items: center;
}

.extension-list-item > .details > .header-container > .header .extension-kind-indicator {
	font-size: 80%;
	margin-left: 4px;
}

.extension-list-item > .details > .header-container > .header > .install-count:not(:empty) {
	font-size: 80%;
	margin: 0 6px;
}

.extension-list-item > .details > .header-container > .header > .activation-status:not(:empty) {
	font-size: 80%;
	margin-left: 2px;
}

.extension-list-item > .details > .header-container > .header > .activation-status .activationTime,
.extension-list-item > .details > .header-container > .header > .activation-status:not(:empty) .codicon {
	margin-right: 2px;
}

.extension-list-item > .details > .header-container > .header .codicon {
	font-size: 120%;
	margin-right: 3px;
	-webkit-mask: inherit;
}

.extension-list-item > .details > .header-container > .header > .ratings {
	text-align: right;
}

.extension-list-item > .details > .header-container > .header .extension-remote-badge .codicon {
	margin-right: 0;
}

.extension-list-item > .details > .header-container > .header .extension-remote-badge {
	width: 14px;
	height: 14px;
	line-height: 14px;
	border-radius: 20px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

.extension-list-item > .details > .header-container > .header .extension-remote-badge > .codicon {
	font-size: 12px;
	color: currentColor;
}

.extension-list-item > .details > .header-container > .header .sync-ignored {
	display: flex;
}

.extension-list-item > .details > .header-container > .header .sync-ignored > .codicon {
	font-size: 100%;
}

.extension-list-item > .details > .description {
	padding-right: 11px;
	line-height: normal;
	color: var(--vscode-descriptionForeground);
}

.monaco-list-row.selected .extension-list-item > .details > .description{
	color: unset;
}

.hc-black .extension-list-item > .details > .description,
.hc-light .extension-list-item > .details > .description {
	color: unset;
}

.extension-list-item > .details > .footer {
	display: flex;
	justify-content: flex-end;
	padding-right: 7px;
	height: 24px;
	overflow: hidden;
	align-items: center;
}

.extension-list-item > .details > .footer > .publisher-container {
	flex: 1;
	line-height: 24px;
}

.extension-list-item > .details > .footer .publisher {
	display: flex;
	align-items: center;
}

.extension-list-item > .details > .footer .publisher > .publisher-name {
	font-size: 90%;
	color: var(--vscode-descriptionForeground);
	font-weight: 600;
}

.monaco-list-row.selected .extension-list-item > .details > .footer .publisher > .publisher-name{
	color: unset;
}

.extension-list-item > .details > .footer .publisher > .publisher-name:not(:first-child) {
	padding-left: 1px;
}

.extension-list-item .ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.extension-list-item > .details > .footer > .monaco-action-bar > .actions-container .action-label:not(.icon) {
	border-radius: 2px;
}

.extension-list-item > .details > .footer > .monaco-action-bar > .actions-container .extension-action.label {
	max-width: 150px;
	line-height: 16px;
}

.extension-list-item .footer .monaco-action-bar .action-item.action-dropdown-item.empty > .action-label {
	margin-right: 4px;
}

.extension-list-item .footer .monaco-action-bar .action-item.action-dropdown-item.empty > .action-dropdown-item-separator {
	display: none;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.disabled {
	min-width: 0;
}

.monaco-list-row.disabled:not(.selected) .extension-list-item > .details > .footer .publisher > .publisher-name {
	color: var(--vscode-disabledForeground);
}

.monaco-list-row.disabled:not(.selected) .extension-list-item {
	color: var(--vscode-disabledForeground);
}

.monaco-list-row.disabled:not(.selected) .extension-list-item .details .header .name,
.monaco-list-row.disabled:not(.selected) .extension-list-item .details .description {
	color: unset;
}

.extension-list-item .monaco-action-bar .action-label.icon {
	padding: 1px 2px;
}

.hc-black .extension-list-item .monaco-action-bar .action-label.icon,
.hc-light .extension-list-item .monaco-action-bar .action-label.icon {
	padding: 0px 2px;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item,
.extension-list-item .monaco-action-bar > .actions-container > .action-item:not(.action-dropdown-item) > .extension-action {
	margin-left: 6px;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item:not(.empty) > .extension-action.label {
	border-right-width: 0;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item > .action-dropdown-item-separator {
	height: 16px;
	margin-top: 2px;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item > .action-dropdown-item-separator > div {
	height: 10px;
}

.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item > .monaco-dropdown .extension-action.label {
	border-left-width: 0;
}

/* single install */
.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item.empty > .extension-action {
	border-radius: 2px;
}

/* split install */
.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item:not(.empty) > .extension-action.label:not(.dropdown) {
	border-radius: 2px 0 0 2px;
}
.extension-list-item .monaco-action-bar > .actions-container > .action-item.action-dropdown-item > .monaco-dropdown .extension-action {
	border-radius: 0 2px 2px 0;
}
