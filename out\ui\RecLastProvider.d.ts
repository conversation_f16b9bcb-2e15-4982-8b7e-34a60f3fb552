import * as vscode from 'vscode';
import { ModelManager } from '../models/ModelManager';
import { MemoryManager } from '../memory/MemoryManager';
import { RAGManager } from '../rag/RAGManager';
import { ConfigManager } from '../config/ConfigManager';
export declare class RecLastProvider implements vscode.WebviewViewProvider {
    private readonly context;
    private readonly modelManager;
    private readonly memoryManager;
    private readonly ragManager;
    private readonly configManager;
    static readonly viewType = "reclast.mainView";
    private _view?;
    private currentMode;
    constructor(context: vscode.ExtensionContext, modelManager: ModelManager, memoryManager: MemoryManager, ragManager: RAGManager, configManager: ConfigManager);
    resolveWebviewView(webviewView: vscode.WebviewView, context: vscode.WebviewViewResolveContext, _token: vscode.CancellationToken): void;
    private handleMessage;
    private handleSendMessage;
    private handleClearChat;
    private handleCreateSnapshot;
    private handleLoadSnapshot;
    private handleGetSnapshots;
    setMode(mode: 'chat' | 'manual' | 'agent'): void;
    updateLanguage(): void;
    private updateWebview;
    private getHtmlForWebview;
    private renderChatHistory;
}
//# sourceMappingURL=RecLastProvider.d.ts.map