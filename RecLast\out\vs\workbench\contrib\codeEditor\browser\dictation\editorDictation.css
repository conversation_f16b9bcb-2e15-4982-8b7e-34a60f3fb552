/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .editor-dictation-widget {
	background-color: var(--vscode-editor-background);
	padding: 2px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	box-shadow: 0 4px 8px var(--vscode-widget-shadow);
	z-index: 1000;
	min-height: var(--vscode-editor-dictation-widget-height);
	line-height: var(--vscode-editor-dictation-widget-height);
	max-width: var(--vscode-editor-dictation-widget-width);
}

.monaco-editor .editor-dictation-widget.recording .codicon.codicon-mic-filled {
	color: var(--vscode-activityBarBadge-background);
	animation: editor-dictation-animation 1s infinite;
}

@keyframes editor-dictation-animation {
	0% {
		color: var(--vscode-editorCursor-background);
	}

	50% {
		color: var(--vscode-activityBarBadge-background);
	}

	100% {
		color: var(--vscode-editorCursor-background);
	}
}
