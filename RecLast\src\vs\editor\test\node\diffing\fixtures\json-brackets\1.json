{"editor": [{"name": "vs/platform", "project": "vscode-editor"}, {"name": "vs/editor/contrib", "project": "vscode-editor"}, {"name": "vs/editor", "project": "vscode-editor"}, {"name": "vs/base", "project": "vscode-editor"}], "workbench": [{"name": "vs/code", "project": "vscode-workbench"}, {"name": "vs/workbench", "project": "vscode-workbench"}, {"name": "vs/workbench/api/common", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/bulkEdit", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/cli", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/codeEditor", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/callHierarchy", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/typeHierarchy", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/codeActions", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/comments", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/debug", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/dialogs", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/emmet", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/experiments", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/extensions", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/externalTerminal", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/feedback", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/files", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/html", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/issue", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/inlayHints", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/interactive", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/languageStatus", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/keybindings", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/markers", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/mergeEditor", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/localization", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/logs", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/output", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/performance", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/preferences", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/notebook", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/quickaccess", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/userData", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/remote", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/relauncher", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/sash", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/scm", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/search", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/searchEditor", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/snippets", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/format", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/tags", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/surveys", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/tasks", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/testing", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/terminal", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/themes", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/trust", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/update", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/url", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/watermark", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/webview", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/webviewPanel", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/workspace", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/workspaces", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/customEditor", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/externalUriO<PERSON>er", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/welcomeGettingStarted", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/welcomeOverlay", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/welcomePage", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/welcomeViews", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/welcomeWalkthrough", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/outline", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/userDataSync", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/editSessions", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/views", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/languageDetection", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/audioCues", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/deprecatedExtensionMigrator", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/offline", "project": "vscode-workbench"}, {"name": "vs/workbench/services/actions", "project": "vscode-workbench"}, {"name": "vs/workbench/services/authToken", "project": "vscode-workbench"}, {"name": "vs/workbench/services/backup", "project": "vscode-workbench"}, {"name": "vs/workbench/services/bulkEdit", "project": "vscode-workbench"}, {"name": "vs/workbench/services/clipboard", "project": "vscode-workbench"}, {"name": "vs/workbench/services/commands", "project": "vscode-workbench"}, {"name": "vs/workbench/services/configuration", "project": "vscode-workbench"}, {"name": "vs/workbench/services/configurationResolver", "project": "vscode-workbench"}, {"name": "vs/workbench/services/dialogs", "project": "vscode-workbench"}, {"name": "vs/workbench/services/editor", "project": "vscode-workbench"}, {"name": "vs/workbench/services/extensions", "project": "vscode-workbench"}, {"name": "vs/workbench/services/extensionManagement", "project": "vscode-workbench"}, {"name": "vs/workbench/services/files", "project": "vscode-workbench"}, {"name": "vs/workbench/services/history", "project": "vscode-workbench"}, {"name": "vs/workbench/services/log", "project": "vscode-workbench"}, {"name": "vs/workbench/services/integrity", "project": "vscode-workbench"}, {"name": "vs/workbench/services/keybinding", "project": "vscode-workbench"}, {"name": "vs/workbench/services/lifecycle", "project": "vscode-workbench"}, {"name": "vs/workbench/services/language", "project": "vscode-workbench"}, {"name": "vs/workbench/services/progress", "project": "vscode-workbench"}, {"name": "vs/workbench/services/remote", "project": "vscode-workbench"}, {"name": "vs/workbench/services/search", "project": "vscode-workbench"}, {"name": "vs/workbench/services/textfile", "project": "vscode-workbench"}, {"name": "vs/workbench/services/themes", "project": "vscode-workbench"}, {"name": "vs/workbench/services/textMate", "project": "vscode-workbench"}, {"name": "vs/workbench/services/workingCopy", "project": "vscode-workbench"}, {"name": "vs/workbench/services/workspaces", "project": "vscode-workbench"}, {"name": "vs/workbench/services/decorations", "project": "vscode-workbench"}, {"name": "vs/workbench/services/label", "project": "vscode-workbench"}, {"name": "vs/workbench/services/preferences", "project": "vscode-preferences"}, {"name": "vs/workbench/services/notification", "project": "vscode-workbench"}, {"name": "vs/workbench/services/userData", "project": "vscode-workbench"}, {"name": "vs/workbench/services/userDataSync", "project": "vscode-workbench"}, {"name": "vs/workbench/services/editSessions", "project": "vscode-workbench"}, {"name": "vs/workbench/services/views", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/timeline", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/localHistory", "project": "vscode-workbench"}, {"name": "vs/workbench/services/authentication", "project": "vscode-workbench"}, {"name": "vs/workbench/services/extensionRecommendations", "project": "vscode-workbench"}, {"name": "vs/workbench/services/gettingStarted", "project": "vscode-workbench"}, {"name": "vs/workbench/services/host", "project": "vscode-workbench"}, {"name": "vs/workbench/contrib/userDataProfile", "project": "vscode-profiles"}, {"name": "vs/workbench/services/userDataProfile", "project": "vscode-profiles"}]}