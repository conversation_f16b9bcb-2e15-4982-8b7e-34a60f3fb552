{"original": {"content": "\nconsole.log(1)\nconsole.log(2)\nconsole.log(3)\nconsole.log(4)\nconsole.log(5)\nconsole.log(6)\nconsole.log(7)\n", "fileName": "./1.txt"}, "modified": {"content": "console.log(1);\nconsole.log(2);\nconsole.log(3);\nconsole.log(4);\n\nconsole.log(5);\nconsole.log(6);\nconsole.log(7);\n\n", "fileName": "./2.txt"}, "diffs": [{"originalRange": "[1,9)", "modifiedRange": "[1,10)", "innerChanges": [{"originalRange": "[1,1 -> 2,1]", "modifiedRange": "[1,1 -> 1,1]"}, {"originalRange": "[2,15 -> 2,15 EOL]", "modifiedRange": "[1,15 -> 1,16 EOL]"}, {"originalRange": "[3,15 -> 3,15 EOL]", "modifiedRange": "[2,15 -> 2,16 EOL]"}, {"originalRange": "[4,15 -> 4,15 EOL]", "modifiedRange": "[3,15 -> 3,16 EOL]"}, {"originalRange": "[5,15 -> 5,15 EOL]", "modifiedRange": "[4,15 -> 5,1 EOL]"}, {"originalRange": "[6,15 -> 6,15 EOL]", "modifiedRange": "[6,15 -> 6,16 EOL]"}, {"originalRange": "[7,15 -> 7,15 EOL]", "modifiedRange": "[7,15 -> 7,16 EOL]"}, {"originalRange": "[8,15 -> 8,15 EOL]", "modifiedRange": "[8,15 -> 9,1 EOL]"}]}]}