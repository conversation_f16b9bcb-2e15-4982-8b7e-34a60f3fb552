{"original": {"content": "import * as path from 'path';\nimport { Command } from 'vscode';\nimport * as nls from 'vscode-nls';\n\n\"()()()()()()()()()()()()()\"", "fileName": "./1.tst"}, "modified": {"content": "import * as path from 'path';\nimport { Command, commands } from 'vscode';\nimport * as nls from 'vscode-nls';\n\n\"Gallicum()est()divisa()in()partres()tres()quarum()unam()est()()()()()()()()()()()()\"", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[2,3)", "modifiedRange": "[2,3)", "innerChanges": [{"originalRange": "[2,17 -> 2,17]", "modifiedRange": "[2,17 -> 2,27]"}]}, {"originalRange": "[5,6)", "modifiedRange": "[5,6)", "innerChanges": [{"originalRange": "[5,2 -> 5,4]", "modifiedRange": "[5,2 -> 5,61]"}]}]}