[{"c": "// from https://flutter.dev/", "t": "source.dart comment.line.double-slash.dart", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "import", "t": "source.dart meta.declaration.dart keyword.other.import.dart", "r": {"dark_plus": "keyword: #569CD6", "light_plus": "keyword: #0000FF", "dark_vs": "keyword: #569CD6", "light_vs": "keyword: #0000FF", "hc_black": "keyword: #569CD6", "dark_modern": "keyword: #569CD6", "hc_light": "keyword: #0F4A85", "light_modern": "keyword: #0000FF"}}, {"c": " ", "t": "source.dart meta.declaration.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "'package:flutter/material.dart'", "t": "source.dart meta.declaration.dart string.interpolated.single.dart", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": ";", "t": "source.dart meta.declaration.dart punctuation.terminator.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "void", "t": "source.dart storage.type.primitive.dart", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "main", "t": "source.dart entity.name.function.dart", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "() ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "async", "t": "source.dart keyword.control.dart", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": " {", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "  ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "runApp", "t": "source.dart entity.name.function.dart", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "    ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "MaterialApp", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": "(", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "      debugShowCheckedModeBanner", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ":", "t": "source.dart keyword.operator.ternary.dart", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "false", "t": "source.dart constant.language.dart", "r": {"dark_plus": "constant.language: #569CD6", "light_plus": "constant.language: #0000FF", "dark_vs": "constant.language: #569CD6", "light_vs": "constant.language: #0000FF", "hc_black": "constant.language: #569CD6", "dark_modern": "constant.language: #569CD6", "hc_light": "constant.language: #0F4A85", "light_modern": "constant.language: #0000FF"}}, {"c": ",", "t": "source.dart punctuation.comma.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "      home", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ":", "t": "source.dart keyword.operator.ternary.dart", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "Scaffold", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": "(", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "        body", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ":", "t": "source.dart keyword.operator.ternary.dart", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "MyApp", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": "()", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ",", "t": "source.dart punctuation.comma.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "      )", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ",", "t": "source.dart punctuation.comma.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "    )", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ",", "t": "source.dart punctuation.comma.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "  )", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ";", "t": "source.dart punctuation.terminator.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "}", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "class", "t": "source.dart keyword.declaration.dart", "r": {"dark_plus": "keyword: #569CD6", "light_plus": "keyword: #0000FF", "dark_vs": "keyword: #569CD6", "light_vs": "keyword: #0000FF", "hc_black": "keyword: #569CD6", "dark_modern": "keyword: #569CD6", "hc_light": "keyword: #0F4A85", "light_modern": "keyword: #0000FF"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "MyApp", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "extends", "t": "source.dart keyword.declaration.dart", "r": {"dark_plus": "keyword: #569CD6", "light_plus": "keyword: #0000FF", "dark_vs": "keyword: #569CD6", "light_vs": "keyword: #0000FF", "hc_black": "keyword: #569CD6", "dark_modern": "keyword: #569CD6", "hc_light": "keyword: #0F4A85", "light_modern": "keyword: #0000FF"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "StatefulWidget", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": " {", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "  ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "@override", "t": "source.dart storage.type.annotation.dart", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": "  ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "_MyAppState", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "createState", "t": "source.dart entity.name.function.dart", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "() ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=>", "t": "source.dart keyword.operator.closure.dart", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "_MyAppState", "t": "source.dart support.class.dart", "r": {"dark_plus": "support.class: #4EC9B0", "light_plus": "support.class: #267F99", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "support.class: #4EC9B0", "dark_modern": "support.class: #4EC9B0", "hc_light": "support.class: #185E73", "light_modern": "support.class: #267F99"}}, {"c": "()", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ";", "t": "source.dart punctuation.terminator.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "}", "t": "source.dart", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}]