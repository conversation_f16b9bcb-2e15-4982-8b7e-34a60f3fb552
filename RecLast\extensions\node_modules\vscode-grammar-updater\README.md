# vscode-grammar-updater
Utility to update the TextMate grammars in the VS Code language extension grammars

[![npm Package](https://img.shields.io/npm/v/vscode-grammar-updater.svg?style=flat-square)](https://www.npmjs.org/package/vscode-grammar-updater)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-grammar-updater.svg)](https://npmjs.org/package/vscode-grammar-updater)

Why?
----
The VSCode language extension such as https://github.com/microsoft/vscode-json contain TextMate grammars. These grammars are imported from the repositories where they are maintained.

This node module contains the import utility used to do that.

Issues
-------

Issues can be created and tracked in [VS Code's Issues page](https://github.com/microsoft/vscode/issues).

License
-------

(MIT License)

Copyright 2021, Microsoft
