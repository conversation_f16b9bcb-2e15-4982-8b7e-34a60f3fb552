/users/foo/src
/users/foo/src/vs
/users/foo/src/vs/monaco.d.ts
/users/foo/src/vs/nls.d.ts
/users/foo/src/vs/nls.js
/users/foo/src/vs/platform
/users/foo/src/vs/platform/keybinding
/users/foo/src/vs/platform/keybinding/test
/users/foo/src/vs/platform/keybinding/test/common
/users/foo/src/vs/platform/keybinding/test/common/keybindingLabels.test.ts
/users/foo/src/vs/platform/keybinding/test/common/abstractKeybindingService.test.ts
/users/foo/src/vs/platform/keybinding/test/common/mockKeybindingService.ts
/users/foo/src/vs/platform/keybinding/test/common/keybindingResolver.test.ts
/users/foo/src/vs/platform/keybinding/common
/users/foo/src/vs/platform/keybinding/common/keybinding.ts
/users/foo/src/vs/platform/keybinding/common/usLayoutResolvedKeybinding.ts
/users/foo/src/vs/platform/keybinding/common/abstractKeybindingService.ts
/users/foo/src/vs/platform/keybinding/common/resolvedKeybindingItem.ts
/users/foo/src/vs/platform/keybinding/common/keybindingsRegistry.ts
/users/foo/src/vs/platform/keybinding/common/keybindingResolver.ts
/users/foo/src/vs/platform/configuration
/users/foo/src/vs/platform/configuration/test
/users/foo/src/vs/platform/configuration/test/common
/users/foo/src/vs/platform/configuration/test/common/configurationModels.test.ts
/users/foo/src/vs/platform/configuration/test/common/testConfigurationService.ts
/users/foo/src/vs/platform/configuration/test/common/configuration.test.ts
/users/foo/src/vs/platform/configuration/test/node
/users/foo/src/vs/platform/configuration/test/node/configurationService.test.ts
/users/foo/src/vs/platform/configuration/common
/users/foo/src/vs/platform/configuration/common/configurationModels.ts
/users/foo/src/vs/platform/configuration/common/configuration.ts
/users/foo/src/vs/platform/configuration/common/configurationRegistry.ts
/users/foo/src/vs/platform/configuration/node
/users/foo/src/vs/platform/configuration/node/configurationService.ts
/users/foo/src/vs/platform/configuration/node/configuration.ts
/users/foo/src/vs/platform/progress
/users/foo/src/vs/platform/progress/common
/users/foo/src/vs/platform/progress/common/progress.ts
/users/foo/src/vs/platform/update
/users/foo/src/vs/platform/update/common
/users/foo/src/vs/platform/update/common/updateIpc.ts
/users/foo/src/vs/platform/update/common/update.ts
/users/foo/src/vs/platform/update/node
/users/foo/src/vs/platform/update/node/update.config.contribution.ts
/users/foo/src/vs/platform/update/electron-main
/users/foo/src/vs/platform/update/electron-main/abstractUpdateService.ts
/users/foo/src/vs/platform/update/electron-main/updateService.linux.ts
/users/foo/src/vs/platform/update/electron-main/updateService.darwin.ts
/users/foo/src/vs/platform/update/electron-main/updateService.win32.ts
/users/foo/src/vs/platform/markers
/users/foo/src/vs/platform/markers/test
/users/foo/src/vs/platform/markers/test/common
/users/foo/src/vs/platform/markers/test/common/markerService.test.ts
/users/foo/src/vs/platform/markers/common
/users/foo/src/vs/platform/markers/common/markerService.ts
/users/foo/src/vs/platform/markers/common/markers.ts
/users/foo/src/vs/platform/quickOpen
/users/foo/src/vs/platform/quickOpen/common
/users/foo/src/vs/platform/quickOpen/common/quickOpen.ts
/users/foo/src/vs/platform/instantiation
/users/foo/src/vs/platform/instantiation/test
/users/foo/src/vs/platform/instantiation/test/common
/users/foo/src/vs/platform/instantiation/test/common/instantiationService.test.ts
/users/foo/src/vs/platform/instantiation/test/common/instantiationServiceMock.ts
/users/foo/src/vs/platform/instantiation/common
/users/foo/src/vs/platform/instantiation/common/instantiation.ts
/users/foo/src/vs/platform/instantiation/common/extensions.ts
/users/foo/src/vs/platform/instantiation/common/descriptors.ts
/users/foo/src/vs/platform/instantiation/common/instantiationService.ts
/users/foo/src/vs/platform/instantiation/common/serviceCollection.ts
/users/foo/src/vs/platform/driver
/users/foo/src/vs/platform/driver/common
/users/foo/src/vs/platform/driver/common/driver.ts
/users/foo/src/vs/platform/driver/electron-browser
/users/foo/src/vs/platform/driver/electron-browser/driver.ts
/users/foo/src/vs/platform/driver/node
/users/foo/src/vs/platform/driver/node/driver.ts
/users/foo/src/vs/platform/driver/electron-main
/users/foo/src/vs/platform/driver/electron-main/driver.ts
/users/foo/src/vs/platform/notification
/users/foo/src/vs/platform/notification/test
/users/foo/src/vs/platform/notification/test/common
/users/foo/src/vs/platform/notification/test/common/testNotificationService.ts
/users/foo/src/vs/platform/notification/common
/users/foo/src/vs/platform/notification/common/notification.ts
/users/foo/src/vs/platform/statusbar
/users/foo/src/vs/platform/statusbar/common
/users/foo/src/vs/platform/statusbar/common/statusbar.ts
/users/foo/src/vs/platform/localizations
/users/foo/src/vs/platform/localizations/common
/users/foo/src/vs/platform/localizations/common/localizations.ts
/users/foo/src/vs/platform/localizations/common/localizationsIpc.ts
/users/foo/src/vs/platform/localizations/node
/users/foo/src/vs/platform/localizations/node/localizations.ts
/users/foo/src/vs/platform/workspace
/users/foo/src/vs/platform/workspace/test
/users/foo/src/vs/platform/workspace/test/common
/users/foo/src/vs/platform/workspace/test/common/testWorkspace.ts
/users/foo/src/vs/platform/workspace/test/common/workspace.test.ts
/users/foo/src/vs/platform/workspace/common
/users/foo/src/vs/platform/workspace/common/workspace.ts
/users/foo/src/vs/platform/quickinput
/users/foo/src/vs/platform/quickinput/common
/users/foo/src/vs/platform/quickinput/common/quickInput.ts
/users/foo/src/vs/platform/lifecycle
/users/foo/src/vs/platform/lifecycle/common
/users/foo/src/vs/platform/lifecycle/common/lifecycle.ts
/users/foo/src/vs/platform/lifecycle/electron-browser
/users/foo/src/vs/platform/lifecycle/electron-browser/lifecycleService.ts
/users/foo/src/vs/platform/lifecycle/electron-main
/users/foo/src/vs/platform/lifecycle/electron-main/lifecycleMain.ts
/users/foo/src/vs/platform/jsonschemas
/users/foo/src/vs/platform/jsonschemas/common
/users/foo/src/vs/platform/jsonschemas/common/jsonContributionRegistry.ts
/users/foo/src/vs/platform/state
/users/foo/src/vs/platform/state/test
/users/foo/src/vs/platform/state/test/node
/users/foo/src/vs/platform/state/test/node/state.test.ts
/users/foo/src/vs/platform/state/common
/users/foo/src/vs/platform/state/common/state.ts
/users/foo/src/vs/platform/state/node
/users/foo/src/vs/platform/state/node/stateService.ts
/users/foo/src/vs/platform/workspaces
/users/foo/src/vs/platform/workspaces/test
/users/foo/src/vs/platform/workspaces/test/electron-main
/users/foo/src/vs/platform/workspaces/test/electron-main/workspacesMainService.test.ts
/users/foo/src/vs/platform/workspaces/common
/users/foo/src/vs/platform/workspaces/common/workspaces.ts
/users/foo/src/vs/platform/workspaces/common/workspacesIpc.ts
/users/foo/src/vs/platform/workspaces/node
/users/foo/src/vs/platform/workspaces/node/workspaces.ts
/users/foo/src/vs/platform/workspaces/electron-main
/users/foo/src/vs/platform/workspaces/electron-main/workspacesMainService.ts
/users/foo/src/vs/platform/url
/users/foo/src/vs/platform/url/common
/users/foo/src/vs/platform/url/common/urlIpc.ts
/users/foo/src/vs/platform/url/common/url.ts
/users/foo/src/vs/platform/url/common/urlService.ts
/users/foo/src/vs/platform/url/electron-browser
/users/foo/src/vs/platform/url/electron-browser/inactiveExtensionUrlHandler.ts
/users/foo/src/vs/platform/url/electron-main
/users/foo/src/vs/platform/url/electron-main/electronUrlListener.ts
/users/foo/src/vs/platform/contextview
/users/foo/src/vs/platform/contextview/browser
/users/foo/src/vs/platform/contextview/browser/contextMenuHandler.ts
/users/foo/src/vs/platform/contextview/browser/contextView.ts
/users/foo/src/vs/platform/contextview/browser/contextMenuService.ts
/users/foo/src/vs/platform/contextview/browser/contextViewService.ts
/users/foo/src/vs/platform/contextview/browser/contextMenuHandler.css
/users/foo/src/vs/platform/storage
/users/foo/src/vs/platform/storage/test
/users/foo/src/vs/platform/storage/test/browser
/users/foo/src/vs/platform/storage/test/browser/migration.test.ts
/users/foo/src/vs/platform/storage/test/common
/users/foo/src/vs/platform/storage/test/common/storageService.test.ts
/users/foo/src/vs/platform/storage/common
/users/foo/src/vs/platform/storage/common/migration.ts
/users/foo/src/vs/platform/storage/common/storage.ts
/users/foo/src/vs/platform/storage/common/storageService.ts
/users/foo/src/vs/platform/contextkey
/users/foo/src/vs/platform/contextkey/test
/users/foo/src/vs/platform/contextkey/test/common
/users/foo/src/vs/platform/contextkey/test/common/contextkey.test.ts
/users/foo/src/vs/platform/contextkey/browser
/users/foo/src/vs/platform/contextkey/browser/contextKeyService.ts
/users/foo/src/vs/platform/contextkey/common
/users/foo/src/vs/platform/contextkey/common/contextkey.ts
/users/foo/src/vs/platform/extensions
/users/foo/src/vs/platform/extensions/test
/users/foo/src/vs/platform/extensions/test/node
/users/foo/src/vs/platform/extensions/test/node/extensionValidator.test.ts
/users/foo/src/vs/platform/extensions/common
/users/foo/src/vs/platform/extensions/common/extensions.ts
/users/foo/src/vs/platform/extensions/common/extensionHost.ts
/users/foo/src/vs/platform/extensions/node
/users/foo/src/vs/platform/extensions/node/extensionValidator.ts
/users/foo/src/vs/platform/search
/users/foo/src/vs/platform/search/test
/users/foo/src/vs/platform/search/test/common
/users/foo/src/vs/platform/search/test/common/replace.test.ts
/users/foo/src/vs/platform/search/common
/users/foo/src/vs/platform/search/common/replace.ts
/users/foo/src/vs/platform/search/common/search.ts
/users/foo/src/vs/platform/opener
/users/foo/src/vs/platform/opener/test
/users/foo/src/vs/platform/opener/test/browser
/users/foo/src/vs/platform/opener/test/browser/openerService.test.ts
/users/foo/src/vs/platform/opener/browser
/users/foo/src/vs/platform/opener/browser/opener.contribution.ts
/users/foo/src/vs/platform/opener/browser/openerService.ts
/users/foo/src/vs/platform/opener/common
/users/foo/src/vs/platform/opener/common/opener.ts
/users/foo/src/vs/platform/theme
/users/foo/src/vs/platform/theme/test
/users/foo/src/vs/platform/theme/test/common
/users/foo/src/vs/platform/theme/test/common/testThemeService.ts
/users/foo/src/vs/platform/theme/common
/users/foo/src/vs/platform/theme/common/styler.ts
/users/foo/src/vs/platform/theme/common/themeService.ts
/users/foo/src/vs/platform/theme/common/colorRegistry.ts
/users/foo/src/vs/platform/workbench
/users/foo/src/vs/platform/workbench/common
/users/foo/src/vs/platform/workbench/common/contextkeys.ts
/users/foo/src/vs/platform/issue
/users/foo/src/vs/platform/issue/common
/users/foo/src/vs/platform/issue/common/issue.ts
/users/foo/src/vs/platform/issue/common/issueIpc.ts
/users/foo/src/vs/platform/issue/electron-main
/users/foo/src/vs/platform/issue/electron-main/issueService.ts
/users/foo/src/vs/platform/list
/users/foo/src/vs/platform/list/browser
/users/foo/src/vs/platform/list/browser/listService.ts
/users/foo/src/vs/platform/history
/users/foo/src/vs/platform/history/common
/users/foo/src/vs/platform/history/common/history.ts
/users/foo/src/vs/platform/history/electron-main
/users/foo/src/vs/platform/history/electron-main/historyMainService.ts
/users/foo/src/vs/platform/actions
/users/foo/src/vs/platform/actions/browser
/users/foo/src/vs/platform/actions/browser/menuItemActionItem.ts
/users/foo/src/vs/platform/actions/common
/users/foo/src/vs/platform/actions/common/menu.ts
/users/foo/src/vs/platform/actions/common/actions.ts
/users/foo/src/vs/platform/registry
/users/foo/src/vs/platform/registry/test
/users/foo/src/vs/platform/registry/test/common
/users/foo/src/vs/platform/registry/test/common/platform.test.ts
/users/foo/src/vs/platform/registry/common
/users/foo/src/vs/platform/registry/common/platform.ts
/users/foo/src/vs/platform/files
/users/foo/src/vs/platform/files/test
/users/foo/src/vs/platform/files/test/files.test.ts
/users/foo/src/vs/platform/files/common
/users/foo/src/vs/platform/files/common/files.ts
/users/foo/src/vs/platform/files/node
/users/foo/src/vs/platform/files/node/files.ts
/users/foo/src/vs/platform/environment
/users/foo/src/vs/platform/environment/test
/users/foo/src/vs/platform/environment/test/node
/users/foo/src/vs/platform/environment/test/node/environmentService.test.ts
/users/foo/src/vs/platform/environment/common
/users/foo/src/vs/platform/environment/common/environment.ts
/users/foo/src/vs/platform/environment/node
/users/foo/src/vs/platform/environment/node/argv.ts
/users/foo/src/vs/platform/environment/node/environmentService.ts
/users/foo/src/vs/platform/dialogs
/users/foo/src/vs/platform/dialogs/common
/users/foo/src/vs/platform/dialogs/common/dialogs.ts
/users/foo/src/vs/platform/dialogs/common/dialogIpc.ts
/users/foo/src/vs/platform/dialogs/node
/users/foo/src/vs/platform/dialogs/node/dialogService.ts
/users/foo/src/vs/platform/log
/users/foo/src/vs/platform/log/common
/users/foo/src/vs/platform/log/common/bufferLog.ts
/users/foo/src/vs/platform/log/common/log.ts
/users/foo/src/vs/platform/log/common/logIpc.ts
/users/foo/src/vs/platform/log/node
/users/foo/src/vs/platform/log/node/spdlogService.ts
/users/foo/src/vs/platform/request
/users/foo/src/vs/platform/request/electron-browser
/users/foo/src/vs/platform/request/electron-browser/requestService.ts
/users/foo/src/vs/platform/request/node
/users/foo/src/vs/platform/request/node/requestService.ts
/users/foo/src/vs/platform/request/node/request.ts
/users/foo/src/vs/platform/request/electron-main
/users/foo/src/vs/platform/request/electron-main/requestService.ts
/users/foo/src/vs/platform/integrity
/users/foo/src/vs/platform/integrity/common
/users/foo/src/vs/platform/integrity/common/integrity.ts
/users/foo/src/vs/platform/integrity/node
/users/foo/src/vs/platform/integrity/node/integrityServiceImpl.ts
/users/foo/src/vs/platform/commands
/users/foo/src/vs/platform/commands/test
/users/foo/src/vs/platform/commands/test/commands.test.ts
/users/foo/src/vs/platform/commands/common
/users/foo/src/vs/platform/commands/common/commands.ts
/users/foo/src/vs/platform/telemetry
/users/foo/src/vs/platform/telemetry/test
/users/foo/src/vs/platform/telemetry/test/electron-browser
/users/foo/src/vs/platform/telemetry/test/electron-browser/telemetryService.test.ts
/users/foo/src/vs/platform/telemetry/test/electron-browser/appInsightsAppender.test.ts
/users/foo/src/vs/platform/telemetry/test/electron-browser/commonProperties.test.ts
/users/foo/src/vs/platform/telemetry/browser
/users/foo/src/vs/platform/telemetry/browser/errorTelemetry.ts
/users/foo/src/vs/platform/telemetry/common
/users/foo/src/vs/platform/telemetry/common/telemetryIpc.ts
/users/foo/src/vs/platform/telemetry/common/experiments.ts
/users/foo/src/vs/platform/telemetry/common/telemetryService.ts
/users/foo/src/vs/platform/telemetry/common/telemetry.ts
/users/foo/src/vs/platform/telemetry/common/telemetryUtils.ts
/users/foo/src/vs/platform/telemetry/node
/users/foo/src/vs/platform/telemetry/node/appInsightsAppender.ts
/users/foo/src/vs/platform/telemetry/node/commonProperties.ts
/users/foo/src/vs/platform/telemetry/node/telemetryNodeUtils.ts
/users/foo/src/vs/platform/telemetry/node/workbenchCommonProperties.ts
/users/foo/src/vs/platform/node
/users/foo/src/vs/platform/node/minimalTranslations.ts
/users/foo/src/vs/platform/node/product.ts
/users/foo/src/vs/platform/node/package.ts
/users/foo/src/vs/platform/windows
/users/foo/src/vs/platform/windows/common
/users/foo/src/vs/platform/windows/common/windowsIpc.ts
/users/foo/src/vs/platform/windows/common/windows.ts
/users/foo/src/vs/platform/windows/electron-browser
/users/foo/src/vs/platform/windows/electron-browser/windowService.ts
/users/foo/src/vs/platform/windows/electron-main
/users/foo/src/vs/platform/windows/electron-main/windows.ts
/users/foo/src/vs/platform/windows/electron-main/windowsService.ts
/users/foo/src/vs/platform/backup
/users/foo/src/vs/platform/backup/test
/users/foo/src/vs/platform/backup/test/electron-main
/users/foo/src/vs/platform/backup/test/electron-main/backupMainService.test.ts
/users/foo/src/vs/platform/backup/common
/users/foo/src/vs/platform/backup/common/backup.ts
/users/foo/src/vs/platform/backup/electron-main
/users/foo/src/vs/platform/backup/electron-main/backupMainService.ts
/users/foo/src/vs/platform/extensionManagement
/users/foo/src/vs/platform/extensionManagement/test
/users/foo/src/vs/platform/extensionManagement/test/common
/users/foo/src/vs/platform/extensionManagement/test/common/extensionEnablementService.test.ts
/users/foo/src/vs/platform/extensionManagement/test/common/extensionManagement.test.ts
/users/foo/src/vs/platform/extensionManagement/test/node
/users/foo/src/vs/platform/extensionManagement/test/node/extensionGalleryService.test.ts
/users/foo/src/vs/platform/extensionManagement/common
/users/foo/src/vs/platform/extensionManagement/common/extensionManagement.ts
/users/foo/src/vs/platform/extensionManagement/common/extensionManagementUtil.ts
/users/foo/src/vs/platform/extensionManagement/common/extensionEnablementService.ts
/users/foo/src/vs/platform/extensionManagement/common/extensionManagementIpc.ts
/users/foo/src/vs/platform/extensionManagement/common/extensionNls.ts
/users/foo/src/vs/platform/extensionManagement/node
/users/foo/src/vs/platform/extensionManagement/node/extensionGalleryService.ts
/users/foo/src/vs/platform/extensionManagement/node/extensionLifecycle.ts
/users/foo/src/vs/platform/extensionManagement/node/extensionsManifestCache.ts
/users/foo/src/vs/platform/extensionManagement/node/extensionManagementUtil.ts
/users/foo/src/vs/platform/extensionManagement/node/extensionManagementService.ts
/users/foo/src/vs/platform/extensionManagement/node/media
/users/foo/src/vs/platform/extensionManagement/node/media/defaultIcon.png
/users/foo/src/vs/platform/broadcast
/users/foo/src/vs/platform/broadcast/electron-browser
/users/foo/src/vs/platform/broadcast/electron-browser/broadcastService.ts
/users/foo/src/vs/platform/editor
/users/foo/src/vs/platform/editor/common
/users/foo/src/vs/platform/editor/common/editor.ts
/users/foo/src/vs/platform/credentials
/users/foo/src/vs/platform/credentials/test
/users/foo/src/vs/platform/credentials/test/node
/users/foo/src/vs/platform/credentials/test/node/keytar.test.ts
/users/foo/src/vs/platform/clipboard
/users/foo/src/vs/platform/clipboard/common
/users/foo/src/vs/platform/clipboard/common/clipboardService.ts
/users/foo/src/vs/platform/clipboard/electron-browser
/users/foo/src/vs/platform/clipboard/electron-browser/clipboardService.ts
/users/foo/src/vs/code
/users/foo/src/vs/code/test
/users/foo/src/vs/code/test/node
/users/foo/src/vs/code/test/node/windowsFinder.test.ts
/users/foo/src/vs/code/test/node/argv.test.ts
/users/foo/src/vs/code/test/node/fixtures
/users/foo/src/vs/code/test/node/fixtures/no_vscode_folder
/users/foo/src/vs/code/test/node/fixtures/no_vscode_folder/file.txt
/users/foo/src/vs/code/test/node/fixtures/vscode_folder
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/file.txt
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/_vscode
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/_vscode/settings.json
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/nested_vscode_folder
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/nested_vscode_folder/_vscode
/users/foo/src/vs/code/test/node/fixtures/vscode_folder/nested_vscode_folder/_vscode/settings.json
/users/foo/src/vs/code/test/node/fixtures/vscode_home_folder
/users/foo/src/vs/code/test/node/fixtures/vscode_home_folder/file.txt
/users/foo/src/vs/code/test/node/fixtures/vscode_home_folder/_vscode
/users/foo/src/vs/code/test/node/fixtures/vscode_home_folder/_vscode/settings.json
/users/foo/src/vs/code/electron-browser
/users/foo/src/vs/code/electron-browser/proxy
/users/foo/src/vs/code/electron-browser/proxy/auth.html
/users/foo/src/vs/code/electron-browser/issue
/users/foo/src/vs/code/electron-browser/issue/issueReporterPage.ts
/users/foo/src/vs/code/electron-browser/issue/test
/users/foo/src/vs/code/electron-browser/issue/test/testReporterModel.test.ts
/users/foo/src/vs/code/electron-browser/issue/issueReporterUtil.ts
/users/foo/src/vs/code/electron-browser/issue/issueReporterModel.ts
/users/foo/src/vs/code/electron-browser/issue/issueReporterMain.ts
/users/foo/src/vs/code/electron-browser/issue/issueReporter.js
/users/foo/src/vs/code/electron-browser/issue/issueReporter.html
/users/foo/src/vs/code/electron-browser/issue/media
/users/foo/src/vs/code/electron-browser/issue/media/issueReporter.css
/users/foo/src/vs/code/electron-browser/processExplorer
/users/foo/src/vs/code/electron-browser/processExplorer/processExplorerMain.ts
/users/foo/src/vs/code/electron-browser/processExplorer/processExplorer.js
/users/foo/src/vs/code/electron-browser/processExplorer/processExplorer.html
/users/foo/src/vs/code/electron-browser/processExplorer/media
/users/foo/src/vs/code/electron-browser/processExplorer/media/processExplorer.css
/users/foo/src/vs/code/electron-browser/sharedProcess
/users/foo/src/vs/code/electron-browser/sharedProcess/sharedProcess.js
/users/foo/src/vs/code/electron-browser/sharedProcess/sharedProcessMain.ts
/users/foo/src/vs/code/electron-browser/sharedProcess/contrib
/users/foo/src/vs/code/electron-browser/sharedProcess/contrib/nodeCachedDataCleaner.ts
/users/foo/src/vs/code/electron-browser/sharedProcess/contrib/contributions.ts
/users/foo/src/vs/code/electron-browser/sharedProcess/contrib/languagePackCachedDataCleaner.ts
/users/foo/src/vs/code/electron-browser/sharedProcess/sharedProcess.html
/users/foo/src/vs/code/buildfile.js
/users/foo/src/vs/code/node
/users/foo/src/vs/code/node/shellEnv.ts
/users/foo/src/vs/code/node/cliProcessMain.ts
/users/foo/src/vs/code/node/cli.ts
/users/foo/src/vs/code/node/windowsFinder.ts
/users/foo/src/vs/code/node/paths.ts
/users/foo/src/vs/code/electron-main
/users/foo/src/vs/code/electron-main/keyboard.ts
/users/foo/src/vs/code/electron-main/logUploader.ts
/users/foo/src/vs/code/electron-main/main.ts
/users/foo/src/vs/code/electron-main/menus.ts
/users/foo/src/vs/code/electron-main/windows.ts
/users/foo/src/vs/code/electron-main/contributions.ts
/users/foo/src/vs/code/electron-main/app.ts
/users/foo/src/vs/code/electron-main/launch.ts
/users/foo/src/vs/code/electron-main/sharedProcess.ts
/users/foo/src/vs/code/electron-main/diagnostics.ts
/users/foo/src/vs/code/electron-main/window.ts
/users/foo/src/vs/code/electron-main/auth.ts
/users/foo/src/vs/vscode.d.ts
/users/foo/src/vs/vscode.proposed.d.ts
/users/foo/src/vs/css.build.js
/users/foo/src/vs/buildunit.json
/users/foo/src/vs/workbench
/users/foo/src/vs/workbench/.DS_Store
/users/foo/src/vs/workbench/test
/users/foo/src/vs/workbench/test/browser
/users/foo/src/vs/workbench/test/browser/actionRegistry.test.ts
/users/foo/src/vs/workbench/test/browser/part.test.ts
/users/foo/src/vs/workbench/test/browser/parts
/users/foo/src/vs/workbench/test/browser/parts/views
/users/foo/src/vs/workbench/test/browser/parts/views/contributableViews.test.ts
/users/foo/src/vs/workbench/test/browser/parts/editor
/users/foo/src/vs/workbench/test/browser/parts/editor/editorStacksModel.test.ts
/users/foo/src/vs/workbench/test/browser/parts/editor/rangeDecorations.test.ts
/users/foo/src/vs/workbench/test/browser/parts/editor/baseEditor.test.ts
/users/foo/src/vs/workbench/test/browser/viewlet.test.ts
/users/foo/src/vs/workbench/test/browser/quickopen.test.ts
/users/foo/src/vs/workbench/test/common
/users/foo/src/vs/workbench/test/common/notifications.test.ts
/users/foo/src/vs/workbench/test/common/memento.test.ts
/users/foo/src/vs/workbench/test/common/editor
/users/foo/src/vs/workbench/test/common/editor/editorModel.test.ts
/users/foo/src/vs/workbench/test/common/editor/editorInput.test.ts
/users/foo/src/vs/workbench/test/common/editor/editorOptions.test.ts
/users/foo/src/vs/workbench/test/common/editor/editor.test.ts
/users/foo/src/vs/workbench/test/common/editor/editorDiffModel.test.ts
/users/foo/src/vs/workbench/test/common/editor/resourceEditorInput.test.ts
/users/foo/src/vs/workbench/test/common/editor/untitledEditor.test.ts
/users/foo/src/vs/workbench/test/common/editor/dataUriEditorInput.test.ts
/users/foo/src/vs/workbench/test/electron-browser
/users/foo/src/vs/workbench/test/electron-browser/quickopen.perf.integrationTest.ts
/users/foo/src/vs/workbench/test/electron-browser/textsearch.perf.integrationTest.ts
/users/foo/src/vs/workbench/test/electron-browser/api
/users/foo/src/vs/workbench/test/electron-browser/api/mock.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostDocumentData.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostTextEditor.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostDiagnostics.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostTypes.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadSaveParticipant.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/testRPCProtocol.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostDocumentSaveParticipant.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostCommands.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostWorkspace.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostTextEditors.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostLanguageFeatures.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostConfiguration.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostMessagerService.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadDocumentsAndEditors.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadDiagnostics.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadConfiguration.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadCommands.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostApiCommands.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostTreeViews.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadEditors.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/mainThreadDocuments.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostSearch.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostWebview.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostFileSystemEventService.test.ts
/users/foo/src/vs/workbench/test/electron-browser/api/extHostDocumentsAndEditors.test.ts
/users/foo/src/vs/workbench/test/workbenchTestServices.ts
/users/foo/src/vs/workbench/browser
/users/foo/src/vs/workbench/browser/viewlet.ts
/users/foo/src/vs/workbench/browser/panel.ts
/users/foo/src/vs/workbench/browser/part.ts
/users/foo/src/vs/workbench/browser/editor.ts
/users/foo/src/vs/workbench/browser/composite.ts
/users/foo/src/vs/workbench/browser/quickopen.ts
/users/foo/src/vs/workbench/browser/actions.ts
/users/foo/src/vs/workbench/browser/dnd.ts
/users/foo/src/vs/workbench/browser/actions
/users/foo/src/vs/workbench/browser/actions/workspaceActions.ts
/users/foo/src/vs/workbench/browser/actions/toggleZenMode.ts
/users/foo/src/vs/workbench/browser/actions/toggleCenteredLayout.ts
/users/foo/src/vs/workbench/browser/actions/toggleSidebarPosition.ts
/users/foo/src/vs/workbench/browser/actions/toggleStatusbarVisibility.ts
/users/foo/src/vs/workbench/browser/actions/workspaceCommands.ts
/users/foo/src/vs/workbench/browser/actions/toggleTabsVisibility.ts
/users/foo/src/vs/workbench/browser/actions/toggleSidebarVisibility.ts
/users/foo/src/vs/workbench/browser/actions/toggleEditorLayout.ts
/users/foo/src/vs/workbench/browser/actions/toggleActivityBarVisibility.ts
/users/foo/src/vs/workbench/browser/actions/media
/users/foo/src/vs/workbench/browser/actions/media/actions.css
/users/foo/src/vs/workbench/browser/actions/media/editor-layout-inverse.svg
/users/foo/src/vs/workbench/browser/actions/media/editor-layout.svg
/users/foo/src/vs/workbench/browser/layout.ts
/users/foo/src/vs/workbench/browser/parts
/users/foo/src/vs/workbench/browser/parts/compositebar
/users/foo/src/vs/workbench/browser/parts/compositebar/compositeBar.ts
/users/foo/src/vs/workbench/browser/parts/compositebar/compositeBarActions.ts
/users/foo/src/vs/workbench/browser/parts/sidebar
/users/foo/src/vs/workbench/browser/parts/sidebar/sidebarPart.ts
/users/foo/src/vs/workbench/browser/parts/sidebar/media
/users/foo/src/vs/workbench/browser/parts/sidebar/media/sidebarpart.css
/users/foo/src/vs/workbench/browser/parts/panel
/users/foo/src/vs/workbench/browser/parts/panel/panelActions.ts
/users/foo/src/vs/workbench/browser/parts/panel/panelPart.ts
/users/foo/src/vs/workbench/browser/parts/panel/media
/users/foo/src/vs/workbench/browser/parts/panel/media/panel-bottom-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/ellipsis-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/right-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/down.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/up.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/close-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/panelpart.css
/users/foo/src/vs/workbench/browser/parts/panel/media/panel-right-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/panel-bottom.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/left-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/close.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/right.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/down-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/panel-right.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/up-inverse.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/ellipsis.svg
/users/foo/src/vs/workbench/browser/parts/panel/media/left.svg
/users/foo/src/vs/workbench/browser/parts/quickopen
/users/foo/src/vs/workbench/browser/parts/quickopen/quickopen.ts
/users/foo/src/vs/workbench/browser/parts/quickopen/quickOpenController.ts
/users/foo/src/vs/workbench/browser/parts/quickopen/quickopen.contribution.ts
/users/foo/src/vs/workbench/browser/parts/quickopen/media
/users/foo/src/vs/workbench/browser/parts/quickopen/media/quickopen.css
/users/foo/src/vs/workbench/browser/parts/quickopen/media/dirty.svg
/users/foo/src/vs/workbench/browser/parts/quickopen/media/dirty-inverse.svg
/users/foo/src/vs/workbench/browser/parts/titlebar
/users/foo/src/vs/workbench/browser/parts/titlebar/titlebarPart.ts
/users/foo/src/vs/workbench/browser/parts/titlebar/media
/users/foo/src/vs/workbench/browser/parts/titlebar/media/titlebarpart.css
/users/foo/src/vs/workbench/browser/parts/compositePart.ts
/users/foo/src/vs/workbench/browser/parts/statusbar
/users/foo/src/vs/workbench/browser/parts/statusbar/statusbarPart.ts
/users/foo/src/vs/workbench/browser/parts/statusbar/statusbar.ts
/users/foo/src/vs/workbench/browser/parts/statusbar/media
/users/foo/src/vs/workbench/browser/parts/statusbar/media/statusbarpart.css
/users/foo/src/vs/workbench/browser/parts/quickinput
/users/foo/src/vs/workbench/browser/parts/quickinput/quickInputList.ts
/users/foo/src/vs/workbench/browser/parts/quickinput/quickInputBox.ts
/users/foo/src/vs/workbench/browser/parts/quickinput/quickInput.contribution.ts
/users/foo/src/vs/workbench/browser/parts/quickinput/quickInput.css
/users/foo/src/vs/workbench/browser/parts/quickinput/quickInput.ts
/users/foo/src/vs/workbench/browser/parts/activitybar
/users/foo/src/vs/workbench/browser/parts/activitybar/activitybarPart.ts
/users/foo/src/vs/workbench/browser/parts/activitybar/activitybarActions.ts
/users/foo/src/vs/workbench/browser/parts/activitybar/media
/users/foo/src/vs/workbench/browser/parts/activitybar/media/activityaction.css
/users/foo/src/vs/workbench/browser/parts/activitybar/media/activitybarpart.css
/users/foo/src/vs/workbench/browser/parts/activitybar/media/ellipsis-global.svg
/users/foo/src/vs/workbench/browser/parts/views
/users/foo/src/vs/workbench/browser/parts/views/views.ts
/users/foo/src/vs/workbench/browser/parts/views/contributableViews.ts
/users/foo/src/vs/workbench/browser/parts/views/panelViewlet.ts
/users/foo/src/vs/workbench/browser/parts/views/customViewPanel.ts
/users/foo/src/vs/workbench/browser/parts/views/viewsViewlet.ts
/users/foo/src/vs/workbench/browser/parts/views/media
/users/foo/src/vs/workbench/browser/parts/views/media/collapsed.svg
/users/foo/src/vs/workbench/browser/parts/views/media/expanded-dark.svg
/users/foo/src/vs/workbench/browser/parts/views/media/views.css
/users/foo/src/vs/workbench/browser/parts/views/media/panelviewlet.css
/users/foo/src/vs/workbench/browser/parts/views/media/collapsed-hc.svg
/users/foo/src/vs/workbench/browser/parts/views/media/collapsed-dark.svg
/users/foo/src/vs/workbench/browser/parts/views/media/expanded.svg
/users/foo/src/vs/workbench/browser/parts/views/media/expanded-hc.svg
/users/foo/src/vs/workbench/browser/parts/notifications
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsList.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsToasts.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsActions.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsStatus.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsAlerts.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsViewer.ts
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsCenter.ts
/users/foo/src/vs/workbench/browser/parts/notifications/media
/users/foo/src/vs/workbench/browser/parts/notifications/media/info-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/down.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/notificationsCenter.css
/users/foo/src/vs/workbench/browser/parts/notifications/media/up.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/close-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/error-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/closeall.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/warning.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/info.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/close.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/notificationsActions.css
/users/foo/src/vs/workbench/browser/parts/notifications/media/down-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/closeall-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/notificationsToasts.css
/users/foo/src/vs/workbench/browser/parts/notifications/media/notificationsList.css
/users/foo/src/vs/workbench/browser/parts/notifications/media/warning-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/up-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/configure-inverse.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/error.svg
/users/foo/src/vs/workbench/browser/parts/notifications/media/configure.svg
/users/foo/src/vs/workbench/browser/parts/notifications/notificationsCommands.ts
/users/foo/src/vs/workbench/browser/parts/editor
/users/foo/src/vs/workbench/browser/parts/editor/textDiffEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorCommands.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorActions.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorPicker.ts
/users/foo/src/vs/workbench/browser/parts/editor/tabsTitleControl.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorGroupsControl.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorPart.ts
/users/foo/src/vs/workbench/browser/parts/editor/editor.contribution.ts
/users/foo/src/vs/workbench/browser/parts/editor/binaryEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/resourceViewer.ts
/users/foo/src/vs/workbench/browser/parts/editor/noTabsTitleControl.ts
/users/foo/src/vs/workbench/browser/parts/editor/baseEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/binaryDiffEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/titleControl.ts
/users/foo/src/vs/workbench/browser/parts/editor/textResourceEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/rangeDecorations.ts
/users/foo/src/vs/workbench/browser/parts/editor/editorStatus.ts
/users/foo/src/vs/workbench/browser/parts/editor/textEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/sideBySideEditor.ts
/users/foo/src/vs/workbench/browser/parts/editor/media
/users/foo/src/vs/workbench/browser/parts/editor/media/previous-diff-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/close-dirty-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/close-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/split-editor-vertical.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/next-diff.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/editorpicker.css
/users/foo/src/vs/workbench/browser/parts/editor/media/editorpart.css
/users/foo/src/vs/workbench/browser/parts/editor/media/split-editor-horizontal-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/split-editor-horizontal.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/Paragraph_16x_nohalo_inversep.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/notabstitle.css
/users/foo/src/vs/workbench/browser/parts/editor/media/editorstatus.css
/users/foo/src/vs/workbench/browser/parts/editor/media/Paragraph_16x_nohalo.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/resourceviewer.css
/users/foo/src/vs/workbench/browser/parts/editor/media/split-editor-vertical-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/close.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/close-big-dark.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/previous-diff.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/forward-tb.png
/users/foo/src/vs/workbench/browser/parts/editor/media/close-dirty.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/editorGroupsControl.css
/users/foo/src/vs/workbench/browser/parts/editor/media/close-big.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/textdiffeditor.css
/users/foo/src/vs/workbench/browser/parts/editor/media/next-diff-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/stackview.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/titlecontrol.css
/users/foo/src/vs/workbench/browser/parts/editor/media/stackview-inverse.svg
/users/foo/src/vs/workbench/browser/parts/editor/media/tabstitle.css
/users/foo/src/vs/workbench/browser/parts/editor/media/back-tb.png
/users/foo/src/vs/workbench/browser/parts/media
/users/foo/src/vs/workbench/browser/parts/media/compositepart.css
/users/foo/src/vs/workbench/browser/labels.ts
/users/foo/src/vs/workbench/browser/media
/users/foo/src/vs/workbench/browser/media/part.css
/users/foo/src/vs/workbench/common
/users/foo/src/vs/workbench/common/viewlet.ts
/users/foo/src/vs/workbench/common/views.ts
/users/foo/src/vs/workbench/common/component.ts
/users/foo/src/vs/workbench/common/panel.ts
/users/foo/src/vs/workbench/common/editor.ts
/users/foo/src/vs/workbench/common/composite.ts
/users/foo/src/vs/workbench/common/contributions.ts
/users/foo/src/vs/workbench/common/resources.ts
/users/foo/src/vs/workbench/common/notifications.ts
/users/foo/src/vs/workbench/common/actions.ts
/users/foo/src/vs/workbench/common/activity.ts
/users/foo/src/vs/workbench/common/theme.ts
/users/foo/src/vs/workbench/common/editor
/users/foo/src/vs/workbench/common/editor/untitledEditorModel.ts
/users/foo/src/vs/workbench/common/editor/resourceEditorModel.ts
/users/foo/src/vs/workbench/common/editor/resourceEditorInput.ts
/users/foo/src/vs/workbench/common/editor/untitledEditorInput.ts
/users/foo/src/vs/workbench/common/editor/diffEditorModel.ts
/users/foo/src/vs/workbench/common/editor/diffEditorInput.ts
/users/foo/src/vs/workbench/common/editor/textEditorModel.ts
/users/foo/src/vs/workbench/common/editor/editorStacksModel.ts
/users/foo/src/vs/workbench/common/editor/textDiffEditorModel.ts
/users/foo/src/vs/workbench/common/editor/dataUriEditorInput.ts
/users/foo/src/vs/workbench/common/editor/binaryEditorModel.ts
/users/foo/src/vs/workbench/common/memento.ts
/users/foo/src/vs/workbench/workbench.main.css
/users/foo/src/vs/workbench/workbench.main.ts
/users/foo/src/vs/workbench/parts
/users/foo/src/vs/workbench/parts/snippets
/users/foo/src/vs/workbench/parts/snippets/test
/users/foo/src/vs/workbench/parts/snippets/test/electron-browser
/users/foo/src/vs/workbench/parts/snippets/test/electron-browser/snippetsRewrite.test.ts
/users/foo/src/vs/workbench/parts/snippets/test/electron-browser/snippetFile.test.ts
/users/foo/src/vs/workbench/parts/snippets/test/electron-browser/snippetsRegistry.test.ts
/users/foo/src/vs/workbench/parts/snippets/test/electron-browser/snippetsService.test.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser
/users/foo/src/vs/workbench/parts/snippets/electron-browser/tabCompletion.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser/snippets.contribution.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser/snippetsService.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser/insertSnippet.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser/snippetsFile.ts
/users/foo/src/vs/workbench/parts/snippets/electron-browser/configureSnippets.ts
/users/foo/src/vs/workbench/parts/preferences
/users/foo/src/vs/workbench/parts/preferences/test
/users/foo/src/vs/workbench/parts/preferences/test/browser
/users/foo/src/vs/workbench/parts/preferences/test/browser/keybindingsEditorContribution.test.ts
/users/foo/src/vs/workbench/parts/preferences/test/browser/settingsTree.test.ts
/users/foo/src/vs/workbench/parts/preferences/test/common
/users/foo/src/vs/workbench/parts/preferences/test/common/smartSnippetInserter.test.ts
/users/foo/src/vs/workbench/parts/preferences/browser
/users/foo/src/vs/workbench/parts/preferences/browser/keybindingWidgets.ts
/users/foo/src/vs/workbench/parts/preferences/browser/preferencesWidgets.ts
/users/foo/src/vs/workbench/parts/preferences/browser/settingsTree.ts
/users/foo/src/vs/workbench/parts/preferences/browser/settingsEditor2.ts
/users/foo/src/vs/workbench/parts/preferences/browser/keybindingsEditor.ts
/users/foo/src/vs/workbench/parts/preferences/browser/preferencesEditor.ts
/users/foo/src/vs/workbench/parts/preferences/browser/preferencesActions.ts
/users/foo/src/vs/workbench/parts/preferences/browser/keybindingsEditorContribution.ts
/users/foo/src/vs/workbench/parts/preferences/browser/preferencesRenderers.ts
/users/foo/src/vs/workbench/parts/preferences/browser/media
/users/foo/src/vs/workbench/parts/preferences/browser/media/collapsed.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/expanded-dark.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/add_inverse.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/keybindingsEditor.css
/users/foo/src/vs/workbench/parts/preferences/browser/media/clean-dark.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/collapseAll.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/collapsed-dark.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/regex.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/collapseAll_inverse.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/info.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/add.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/expanded.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/settingsEditor2.css
/users/foo/src/vs/workbench/parts/preferences/browser/media/preferences.css
/users/foo/src/vs/workbench/parts/preferences/browser/media/status-error.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/edit.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/edit_inverse.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/sort_precedence_inverse.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/sort_precedence.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/clean.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/regex-dark.svg
/users/foo/src/vs/workbench/parts/preferences/browser/media/keybindings.css
/users/foo/src/vs/workbench/parts/preferences/common
/users/foo/src/vs/workbench/parts/preferences/common/smartSnippetInserter.ts
/users/foo/src/vs/workbench/parts/preferences/common/preferences.ts
/users/foo/src/vs/workbench/parts/preferences/common/preferencesContribution.ts
/users/foo/src/vs/workbench/parts/preferences/electron-browser
/users/foo/src/vs/workbench/parts/preferences/electron-browser/preferences.contribution.ts
/users/foo/src/vs/workbench/parts/preferences/electron-browser/preferencesSearch.ts
/users/foo/src/vs/workbench/parts/codeEditor
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/languageConfiguration
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/languageConfiguration/languageConfigurationExtensionPoint.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/toggleMinimap.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/toggleWordWrap.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/accessibility.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/selectionClipboard.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/textMate
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/textMate/inspectTMScopes.css
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/textMate/inspectTMScopes.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/toggleMultiCursorModifier.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/largeFileOptimizations.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/inspectKeybindings.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/workbenchReferenceSearch.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/toggleRenderControlCharacter.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/menuPreventer.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/accessibility.css
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/toggleRenderWhitespace.ts
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/media
/users/foo/src/vs/workbench/parts/codeEditor/electron-browser/media/WordWrap_16x.svg
/users/foo/src/vs/workbench/parts/codeEditor/codeEditor.contribution.ts
/users/foo/src/vs/workbench/parts/tasks
/users/foo/src/vs/workbench/parts/tasks/test
/users/foo/src/vs/workbench/parts/tasks/test/common
/users/foo/src/vs/workbench/parts/tasks/test/common/problemMatcher.test.ts
/users/foo/src/vs/workbench/parts/tasks/test/electron-browser
/users/foo/src/vs/workbench/parts/tasks/test/electron-browser/configuration.test.ts
/users/foo/src/vs/workbench/parts/tasks/browser
/users/foo/src/vs/workbench/parts/tasks/browser/taskQuickOpen.ts
/users/foo/src/vs/workbench/parts/tasks/browser/quickOpen.ts
/users/foo/src/vs/workbench/parts/tasks/common
/users/foo/src/vs/workbench/parts/tasks/common/taskTemplates.ts
/users/foo/src/vs/workbench/parts/tasks/common/problemMatcher.ts
/users/foo/src/vs/workbench/parts/tasks/common/taskDefinitionRegistry.ts
/users/foo/src/vs/workbench/parts/tasks/common/taskService.ts
/users/foo/src/vs/workbench/parts/tasks/common/problemCollectors.ts
/users/foo/src/vs/workbench/parts/tasks/common/taskSystem.ts
/users/foo/src/vs/workbench/parts/tasks/common/tasks.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser
/users/foo/src/vs/workbench/parts/tasks/electron-browser/jsonSchema_v1.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser/terminalTaskSystem.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser/task.contribution.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser/jsonSchema_v2.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser/jsonSchemaCommon.ts
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/task.svg
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/status-error.svg
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/task.contribution.css
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/status-info.svg
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/status-warning.svg
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/configure-inverse.svg
/users/foo/src/vs/workbench/parts/tasks/electron-browser/media/configure.svg
/users/foo/src/vs/workbench/parts/tasks/node
/users/foo/src/vs/workbench/parts/tasks/node/taskConfiguration.ts
/users/foo/src/vs/workbench/parts/tasks/node/processTaskSystem.ts
/users/foo/src/vs/workbench/parts/tasks/node/processRunnerDetector.ts
/users/foo/src/vs/workbench/parts/.DS_Store
/users/foo/src/vs/workbench/parts/update
/users/foo/src/vs/workbench/parts/update/electron-browser
/users/foo/src/vs/workbench/parts/update/electron-browser/update.contribution.ts
/users/foo/src/vs/workbench/parts/update/electron-browser/releaseNotesEditor.ts
/users/foo/src/vs/workbench/parts/update/electron-browser/update.ts
/users/foo/src/vs/workbench/parts/update/electron-browser/media
/users/foo/src/vs/workbench/parts/update/electron-browser/media/markdown.css
/users/foo/src/vs/workbench/parts/update/electron-browser/media/update.svg
/users/foo/src/vs/workbench/parts/update/electron-browser/media/code-icon.svg
/users/foo/src/vs/workbench/parts/update/electron-browser/media/update.contribution.css
/users/foo/src/vs/workbench/parts/markers
/users/foo/src/vs/workbench/parts/markers/test
/users/foo/src/vs/workbench/parts/markers/test/electron-browser
/users/foo/src/vs/workbench/parts/markers/test/electron-browser/markersModel.test.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser
/users/foo/src/vs/workbench/parts/markers/electron-browser/markers.contribution.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersFileDecorations.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersTreeViewer.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/messages.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markers.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersPanel.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersTreeController.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/constants.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersModel.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/markersPanelActions.ts
/users/foo/src/vs/workbench/parts/markers/electron-browser/media
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/excludeSettings.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/markers.css
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-info-inverse.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-error-inverse.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-error.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/excludeSettings-dark.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-info.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-warning.svg
/users/foo/src/vs/workbench/parts/markers/electron-browser/media/status-warning-inverse.svg
/users/foo/src/vs/workbench/parts/quickopen
/users/foo/src/vs/workbench/parts/quickopen/.DS_Store
/users/foo/src/vs/workbench/parts/quickopen/browser
/users/foo/src/vs/workbench/parts/quickopen/browser/gotoLineHandler.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/commandsHandler.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/gotoSymbolHandler.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/helpHandler.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/viewPickerHandler.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/quickopen.contribution.ts
/users/foo/src/vs/workbench/parts/quickopen/browser/media
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Constant_16x.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/symbol-sprite.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/EnumItem_inverse_16x.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Template_16x_vscode.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Operator_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Structure_16x_vscode.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Event_16x_vscode.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/gotoSymbolHandler.css
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Structure_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Event_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Template_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/EnumItem_16x.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Operator_16x_vscode.svg
/users/foo/src/vs/workbench/parts/quickopen/browser/media/Constant_16x_inverse.svg
/users/foo/src/vs/workbench/parts/localizations
/users/foo/src/vs/workbench/parts/localizations/electron-browser
/users/foo/src/vs/workbench/parts/localizations/electron-browser/localizationsActions.ts
/users/foo/src/vs/workbench/parts/localizations/electron-browser/localizations.contribution.ts
/users/foo/src/vs/workbench/parts/webview
/users/foo/src/vs/workbench/parts/webview/electron-browser
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewEditorInput.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewElement.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webview-pre.js
/users/foo/src/vs/workbench/parts/webview/electron-browser/baseWebviewEditor.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewEditor.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webview.html
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewEditorService.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewFindWidget.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewEditorInputFactory.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webviewCommands.ts
/users/foo/src/vs/workbench/parts/webview/electron-browser/webview.contribution.ts
/users/foo/src/vs/workbench/parts/output
/users/foo/src/vs/workbench/parts/output/test
/users/foo/src/vs/workbench/parts/output/test/outputLinkProvider.test.ts
/users/foo/src/vs/workbench/parts/output/browser
/users/foo/src/vs/workbench/parts/output/browser/outputPanel.ts
/users/foo/src/vs/workbench/parts/output/browser/logViewer.ts
/users/foo/src/vs/workbench/parts/output/browser/outputActions.ts
/users/foo/src/vs/workbench/parts/output/browser/media
/users/foo/src/vs/workbench/parts/output/browser/media/clear_output_inverse.svg
/users/foo/src/vs/workbench/parts/output/browser/media/output_lock_inverse.svg
/users/foo/src/vs/workbench/parts/output/browser/media/open_log_file.svg
/users/foo/src/vs/workbench/parts/output/browser/media/output_unlock.svg
/users/foo/src/vs/workbench/parts/output/browser/media/open_log_file_inverse.svg
/users/foo/src/vs/workbench/parts/output/browser/media/output_lock.svg
/users/foo/src/vs/workbench/parts/output/browser/media/output_unlock_inverse.svg
/users/foo/src/vs/workbench/parts/output/browser/media/output.css
/users/foo/src/vs/workbench/parts/output/browser/media/clear_output.svg
/users/foo/src/vs/workbench/parts/output/common
/users/foo/src/vs/workbench/parts/output/common/output.ts
/users/foo/src/vs/workbench/parts/output/common/outputLinkProvider.ts
/users/foo/src/vs/workbench/parts/output/common/outputLinkComputer.ts
/users/foo/src/vs/workbench/parts/output/electron-browser
/users/foo/src/vs/workbench/parts/output/electron-browser/outputServices.ts
/users/foo/src/vs/workbench/parts/output/electron-browser/output.contribution.ts
/users/foo/src/vs/workbench/parts/terminal
/users/foo/src/vs/workbench/parts/terminal/test
/users/foo/src/vs/workbench/parts/terminal/test/electron-browser
/users/foo/src/vs/workbench/parts/terminal/test/electron-browser/terminalConfigHelper.test.ts
/users/foo/src/vs/workbench/parts/terminal/test/electron-browser/terminalColorRegistry.test.ts
/users/foo/src/vs/workbench/parts/terminal/test/electron-browser/terminalLinkHandler.test.ts
/users/foo/src/vs/workbench/parts/terminal/test/node
/users/foo/src/vs/workbench/parts/terminal/test/node/terminalCommandTracker.test.ts
/users/foo/src/vs/workbench/parts/terminal/test/node/terminalEnvironment.test.ts
/users/foo/src/vs/workbench/parts/terminal/browser
/users/foo/src/vs/workbench/parts/terminal/browser/terminalTab.ts
/users/foo/src/vs/workbench/parts/terminal/browser/terminalQuickOpen.ts
/users/foo/src/vs/workbench/parts/terminal/browser/terminalWidgetManager.ts
/users/foo/src/vs/workbench/parts/terminal/browser/terminalFindWidget.ts
/users/foo/src/vs/workbench/parts/terminal/common
/users/foo/src/vs/workbench/parts/terminal/common/terminalService.ts
/users/foo/src/vs/workbench/parts/terminal/common/terminalCommands.ts
/users/foo/src/vs/workbench/parts/terminal/common/terminalColorRegistry.ts
/users/foo/src/vs/workbench/parts/terminal/common/terminal.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalService.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalActions.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalConfigHelper.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalLinkHandler.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminal.contribution.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalPanel.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalInstance.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/terminalProcessManager.ts
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/terminal.css
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/xterm.css
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/kill-inverse.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/widgets.css
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/new-inverse.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/split-horizontal.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/split-inverse.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/kill.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/split.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/scrollbar.css
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/split-horizontal-inverse.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/new.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/configure-inverse.svg
/users/foo/src/vs/workbench/parts/terminal/electron-browser/media/configure.svg
/users/foo/src/vs/workbench/parts/terminal/node
/users/foo/src/vs/workbench/parts/terminal/node/terminalEnvironment.ts
/users/foo/src/vs/workbench/parts/terminal/node/terminalProcessExtHostProxy.ts
/users/foo/src/vs/workbench/parts/terminal/node/terminalCommandTracker.ts
/users/foo/src/vs/workbench/parts/terminal/node/terminalProcess.ts
/users/foo/src/vs/workbench/parts/terminal/node/terminal.ts
/users/foo/src/vs/workbench/parts/terminal/node/windowsShellHelper.ts
/users/foo/src/vs/workbench/parts/html
/users/foo/src/vs/workbench/parts/html/common
/users/foo/src/vs/workbench/parts/html/common/htmlInput.ts
/users/foo/src/vs/workbench/parts/html/electron-browser
/users/foo/src/vs/workbench/parts/html/electron-browser/html.contribution.ts
/users/foo/src/vs/workbench/parts/html/electron-browser/htmlPreviewPart.ts
/users/foo/src/vs/workbench/parts/cli
/users/foo/src/vs/workbench/parts/cli/electron-browser
/users/foo/src/vs/workbench/parts/cli/electron-browser/cli.contribution.ts
/users/foo/src/vs/workbench/parts/welcome
/users/foo/src/vs/workbench/parts/welcome/page
/users/foo/src/vs/workbench/parts/welcome/page/electron-browser
/users/foo/src/vs/workbench/parts/welcome/page/electron-browser/welcomePage.contribution.ts
/users/foo/src/vs/workbench/parts/welcome/page/electron-browser/vs_code_welcome_page.ts
/users/foo/src/vs/workbench/parts/welcome/page/electron-browser/welcomePage.ts
/users/foo/src/vs/workbench/parts/welcome/page/electron-browser/welcomePage.css
/users/foo/src/vs/workbench/parts/welcome/walkThrough
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/walkThroughActions.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/walkThroughPart.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/walkThrough.contribution.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/walkThroughPart.css
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/editor
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/editor/editorWalkThrough.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/electron-browser/editor/vs_code_editor_walkthrough.md
/users/foo/src/vs/workbench/parts/welcome/walkThrough/node
/users/foo/src/vs/workbench/parts/welcome/walkThrough/node/walkThroughUtils.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/node/walkThroughInput.ts
/users/foo/src/vs/workbench/parts/welcome/walkThrough/node/walkThroughContentProvider.ts
/users/foo/src/vs/workbench/parts/welcome/gettingStarted
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/test
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/test/common
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/test/common/gettingStarted.test.ts
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/electron-browser
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/electron-browser/gettingStarted.contribution.ts
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/electron-browser/telemetryOptOut.ts
/users/foo/src/vs/workbench/parts/welcome/gettingStarted/electron-browser/gettingStarted.ts
/users/foo/src/vs/workbench/parts/welcome/code-icon.svg
/users/foo/src/vs/workbench/parts/welcome/overlay
/users/foo/src/vs/workbench/parts/welcome/overlay/browser
/users/foo/src/vs/workbench/parts/welcome/overlay/browser/welcomeOverlay.css
/users/foo/src/vs/workbench/parts/welcome/overlay/browser/welcomeOverlay.ts
/users/foo/src/vs/workbench/parts/welcome/overlay/browser/media
/users/foo/src/vs/workbench/parts/welcome/overlay/browser/media/commandpalette-dark.svg
/users/foo/src/vs/workbench/parts/welcome/overlay/browser/media/commandpalette.svg
/users/foo/src/vs/workbench/parts/url
/users/foo/src/vs/workbench/parts/url/electron-browser
/users/foo/src/vs/workbench/parts/url/electron-browser/url.contribution.ts
/users/foo/src/vs/workbench/parts/emmet
/users/foo/src/vs/workbench/parts/emmet/test
/users/foo/src/vs/workbench/parts/emmet/test/electron-browser
/users/foo/src/vs/workbench/parts/emmet/test/electron-browser/emmetAction.test.ts
/users/foo/src/vs/workbench/parts/emmet/browser
/users/foo/src/vs/workbench/parts/emmet/browser/emmet.browser.contribution.ts
/users/foo/src/vs/workbench/parts/emmet/browser/actions
/users/foo/src/vs/workbench/parts/emmet/browser/actions/showEmmetCommands.ts
/users/foo/src/vs/workbench/parts/emmet/electron-browser
/users/foo/src/vs/workbench/parts/emmet/electron-browser/emmetActions.ts
/users/foo/src/vs/workbench/parts/emmet/electron-browser/actions
/users/foo/src/vs/workbench/parts/emmet/electron-browser/actions/expandAbbreviation.ts
/users/foo/src/vs/workbench/parts/emmet/electron-browser/emmet.contribution.ts
/users/foo/src/vs/workbench/parts/feedback
/users/foo/src/vs/workbench/parts/feedback/electron-browser
/users/foo/src/vs/workbench/parts/feedback/electron-browser/feedback.contribution.ts
/users/foo/src/vs/workbench/parts/feedback/electron-browser/feedback.ts
/users/foo/src/vs/workbench/parts/feedback/electron-browser/feedbackStatusbarItem.ts
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/close-dark.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/sad.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/smiley.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/happy.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/info.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/close.svg
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/feedback.css
/users/foo/src/vs/workbench/parts/feedback/electron-browser/media/twitter.svg
/users/foo/src/vs/workbench/parts/extensions
/users/foo/src/vs/workbench/parts/extensions/test
/users/foo/src/vs/workbench/parts/extensions/test/common
/users/foo/src/vs/workbench/parts/extensions/test/common/extensionQuery.test.ts
/users/foo/src/vs/workbench/parts/extensions/test/electron-browser
/users/foo/src/vs/workbench/parts/extensions/test/electron-browser/extensionsTipsService.test.ts
/users/foo/src/vs/workbench/parts/extensions/test/electron-browser/extensionsActions.test.ts
/users/foo/src/vs/workbench/parts/extensions/test/electron-browser/extensionsWorkbenchService.test.ts
/users/foo/src/vs/workbench/parts/extensions/browser
/users/foo/src/vs/workbench/parts/extensions/browser/extensionsWidgets.ts
/users/foo/src/vs/workbench/parts/extensions/browser/dependenciesViewer.ts
/users/foo/src/vs/workbench/parts/extensions/browser/extensionsQuickOpen.ts
/users/foo/src/vs/workbench/parts/extensions/browser/extensionsList.ts
/users/foo/src/vs/workbench/parts/extensions/browser/extensionsActions.ts
/users/foo/src/vs/workbench/parts/extensions/browser/media
/users/foo/src/vs/workbench/parts/extensions/browser/media/language-icon.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/theme-icon.png
/users/foo/src/vs/workbench/parts/extensions/browser/media/clear.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/HalfStarLight.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/EmptyStar.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/loading.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/manage-inverse.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/extensionActions.css
/users/foo/src/vs/workbench/parts/extensions/browser/media/FullStarLight.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/defaultIcon.png
/users/foo/src/vs/workbench/parts/extensions/browser/media/extensionsWidgets.css
/users/foo/src/vs/workbench/parts/extensions/browser/media/clear-inverse.svg
/users/foo/src/vs/workbench/parts/extensions/browser/media/manage.svg
/users/foo/src/vs/workbench/parts/extensions/common
/users/foo/src/vs/workbench/parts/extensions/common/extensionQuery.ts
/users/foo/src/vs/workbench/parts/extensions/common/extensionsFileTemplate.ts
/users/foo/src/vs/workbench/parts/extensions/common/extensionsInput.ts
/users/foo/src/vs/workbench/parts/extensions/common/extensions.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionsViews.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensions.contribution.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionEditor.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionsViewlet.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionsUtils.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/runtimeExtensionsEditor.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionsActions.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionTipsService.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/extensionProfileService.ts
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/markdown.css
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/runtimeExtensionsEditor.css
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/profile-start.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/profile-start-inverse.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/save.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/loading.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/save-inverse.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/profile-stop-inverse.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/extensions.css
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/extensionEditor.css
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/profile-stop.svg
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/extensionsViewlet.css
/users/foo/src/vs/workbench/parts/extensions/electron-browser/media/extensions-dark.svg
/users/foo/src/vs/workbench/parts/extensions/node
/users/foo/src/vs/workbench/parts/extensions/node/extensionsWorkbenchService.ts
/users/foo/src/vs/workbench/parts/search
/users/foo/src/vs/workbench/parts/search/test
/users/foo/src/vs/workbench/parts/search/test/browser
/users/foo/src/vs/workbench/parts/search/test/browser/openFileHandler.test.ts
/users/foo/src/vs/workbench/parts/search/test/browser/searchViewlet.test.ts
/users/foo/src/vs/workbench/parts/search/test/browser/searchActions.test.ts
/users/foo/src/vs/workbench/parts/search/test/common
/users/foo/src/vs/workbench/parts/search/test/common/queryBuilder.test.ts
/users/foo/src/vs/workbench/parts/search/test/common/searchResult.test.ts
/users/foo/src/vs/workbench/parts/search/test/common/searchModel.test.ts
/users/foo/src/vs/workbench/parts/search/browser
/users/foo/src/vs/workbench/parts/search/browser/openSymbolHandler.ts
/users/foo/src/vs/workbench/parts/search/browser/replaceContributions.ts
/users/foo/src/vs/workbench/parts/search/browser/patternInputWidget.ts
/users/foo/src/vs/workbench/parts/search/browser/replaceService.ts
/users/foo/src/vs/workbench/parts/search/browser/openFileHandler.ts
/users/foo/src/vs/workbench/parts/search/browser/searchViewLocationUpdater.ts
/users/foo/src/vs/workbench/parts/search/browser/openAnythingHandler.ts
/users/foo/src/vs/workbench/parts/search/browser/searchActions.ts
/users/foo/src/vs/workbench/parts/search/browser/searchView.ts
/users/foo/src/vs/workbench/parts/search/browser/searchResultsView.ts
/users/foo/src/vs/workbench/parts/search/browser/searchWidget.ts
/users/foo/src/vs/workbench/parts/search/browser/media
/users/foo/src/vs/workbench/parts/search/browser/media/expando-expanded.svg
/users/foo/src/vs/workbench/parts/search/browser/media/pattern.svg
/users/foo/src/vs/workbench/parts/search/browser/media/action-remove-focus.svg
/users/foo/src/vs/workbench/parts/search/browser/media/ellipsis-inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/clear-search-results.svg
/users/foo/src/vs/workbench/parts/search/browser/media/replace-all.svg
/users/foo/src/vs/workbench/parts/search/browser/media/expando-collapsed.svg
/users/foo/src/vs/workbench/parts/search/browser/media/excludeSettings.svg
/users/foo/src/vs/workbench/parts/search/browser/media/action-query-clear.svg
/users/foo/src/vs/workbench/parts/search/browser/media/CollapseAll.svg
/users/foo/src/vs/workbench/parts/search/browser/media/clear-search-results-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/action-remove.svg
/users/foo/src/vs/workbench/parts/search/browser/media/action-remove-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/CollapseAll_inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/replace-inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/stop.svg
/users/foo/src/vs/workbench/parts/search/browser/media/Refresh_inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/Refresh.svg
/users/foo/src/vs/workbench/parts/search/browser/media/replace-all-inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/excludeSettings-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/searchview.css
/users/foo/src/vs/workbench/parts/search/browser/media/expando-expanded-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/ellipsis.svg
/users/foo/src/vs/workbench/parts/search/browser/media/configure-inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/action-query-clear-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/stop-inverse.svg
/users/foo/src/vs/workbench/parts/search/browser/media/replace.svg
/users/foo/src/vs/workbench/parts/search/browser/media/expando-collapsed-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/pattern-dark.svg
/users/foo/src/vs/workbench/parts/search/browser/media/configure.svg
/users/foo/src/vs/workbench/parts/search/common
/users/foo/src/vs/workbench/parts/search/common/queryBuilder.ts
/users/foo/src/vs/workbench/parts/search/common/replace.ts
/users/foo/src/vs/workbench/parts/search/common/search.ts
/users/foo/src/vs/workbench/parts/search/common/searchModel.ts
/users/foo/src/vs/workbench/parts/search/common/constants.ts
/users/foo/src/vs/workbench/parts/search/electron-browser
/users/foo/src/vs/workbench/parts/search/electron-browser/search.contribution.ts
/users/foo/src/vs/workbench/parts/search/electron-browser/media
/users/foo/src/vs/workbench/parts/search/electron-browser/media/search.contribution.css
/users/foo/src/vs/workbench/parts/search/electron-browser/media/search-dark.svg
/users/foo/src/vs/workbench/parts/watermark
/users/foo/src/vs/workbench/parts/watermark/electron-browser
/users/foo/src/vs/workbench/parts/watermark/electron-browser/watermark.css
/users/foo/src/vs/workbench/parts/watermark/electron-browser/watermark.ts
/users/foo/src/vs/workbench/parts/logs
/users/foo/src/vs/workbench/parts/logs/common
/users/foo/src/vs/workbench/parts/logs/common/logConstants.ts
/users/foo/src/vs/workbench/parts/logs/electron-browser
/users/foo/src/vs/workbench/parts/logs/electron-browser/logs.contribution.ts
/users/foo/src/vs/workbench/parts/logs/electron-browser/logsActions.ts
/users/foo/src/vs/workbench/parts/outline
/users/foo/src/vs/workbench/parts/outline/electron-browser
/users/foo/src/vs/workbench/parts/outline/electron-browser/outlinePanel.css
/users/foo/src/vs/workbench/parts/outline/electron-browser/outline.contribution.ts
/users/foo/src/vs/workbench/parts/outline/electron-browser/outlineTree.ts
/users/foo/src/vs/workbench/parts/outline/electron-browser/outlinePanel.ts
/users/foo/src/vs/workbench/parts/outline/electron-browser/outlineModel.ts
/users/foo/src/vs/workbench/parts/outline/electron-browser/media
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Constant_16x.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/symbol-sprite.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/EnumItem_inverse_16x.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Template_16x_vscode.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Operator_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Structure_16x_vscode.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/symbol-icons.css
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Event_16x_vscode.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Structure_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Event_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Template_16x_vscode_inverse.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/EnumItem_16x.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Operator_16x_vscode.svg
/users/foo/src/vs/workbench/parts/outline/electron-browser/media/Constant_16x_inverse.svg
/users/foo/src/vs/workbench/parts/execution
/users/foo/src/vs/workbench/parts/execution/test
/users/foo/src/vs/workbench/parts/execution/test/electron-browser
/users/foo/src/vs/workbench/parts/execution/test/electron-browser/terminalService.test.ts
/users/foo/src/vs/workbench/parts/execution/common
/users/foo/src/vs/workbench/parts/execution/common/execution.ts
/users/foo/src/vs/workbench/parts/execution/electron-browser
/users/foo/src/vs/workbench/parts/execution/electron-browser/terminalService.ts
/users/foo/src/vs/workbench/parts/execution/electron-browser/TerminalHelper.scpt
/users/foo/src/vs/workbench/parts/execution/electron-browser/iTermHelper.scpt
/users/foo/src/vs/workbench/parts/execution/electron-browser/execution.contribution.ts
/users/foo/src/vs/workbench/parts/execution/electron-browser/terminal.ts
/users/foo/src/vs/workbench/parts/files
/users/foo/src/vs/workbench/parts/files/test
/users/foo/src/vs/workbench/parts/files/test/browser
/users/foo/src/vs/workbench/parts/files/test/browser/fileEditorInput.test.ts
/users/foo/src/vs/workbench/parts/files/test/browser/fileEditorTracker.test.ts
/users/foo/src/vs/workbench/parts/files/test/electron-browser
/users/foo/src/vs/workbench/parts/files/test/electron-browser/fileActions.test.ts
/users/foo/src/vs/workbench/parts/files/test/electron-browser/explorerModel.test.ts
/users/foo/src/vs/workbench/parts/files/browser
/users/foo/src/vs/workbench/parts/files/browser/editors
/users/foo/src/vs/workbench/parts/files/browser/editors/textFileEditor.ts
/users/foo/src/vs/workbench/parts/files/browser/editors/binaryFileEditor.ts
/users/foo/src/vs/workbench/parts/files/browser/editors/fileEditorTracker.ts
/users/foo/src/vs/workbench/parts/files/browser/files.ts
/users/foo/src/vs/workbench/parts/files/common
/users/foo/src/vs/workbench/parts/files/common/explorerModel.ts
/users/foo/src/vs/workbench/parts/files/common/editors
/users/foo/src/vs/workbench/parts/files/common/editors/fileEditorInput.ts
/users/foo/src/vs/workbench/parts/files/common/dirtyFilesTracker.ts
/users/foo/src/vs/workbench/parts/files/common/files.ts
/users/foo/src/vs/workbench/parts/files/electron-browser
/users/foo/src/vs/workbench/parts/files/electron-browser/fileCommands.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/files.contribution.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/saveErrorHandler.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/fileActions.contribution.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/fileActions.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/views
/users/foo/src/vs/workbench/parts/files/electron-browser/views/explorerViewer.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/views/explorerDecorationsProvider.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/views/emptyView.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/views/openEditorsView.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/views/explorerView.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/explorerViewlet.ts
/users/foo/src/vs/workbench/parts/files/electron-browser/media
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close-dirty.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/check-inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close-focus.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/saveall.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/undo-inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/AddFile_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/new-file-tb.png
/users/foo/src/vs/workbench/parts/files/electron-browser/media/CollapseAll.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/explorerviewlet.css
/users/foo/src/vs/workbench/parts/files/electron-browser/media/closeall_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/split-editor-vertical.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/saveall_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/files-dark.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close-dirty-focus.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/CollapseAll_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/closeall.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/split-editor-horizontal-inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/split-editor-horizontal.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/Preview.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/check.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/split-editor-vertical-inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/fileactions.css
/users/foo/src/vs/workbench/parts/files/electron-browser/media/Refresh_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/Refresh.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/AddFile.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close-dirty-dark.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/action-close-dark.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/undo.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/Preview_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/AddFolder_inverse.svg
/users/foo/src/vs/workbench/parts/files/electron-browser/media/AddFolder.svg
/users/foo/src/vs/workbench/parts/surveys
/users/foo/src/vs/workbench/parts/surveys/electron-browser
/users/foo/src/vs/workbench/parts/surveys/electron-browser/languageSurveys.contribution.ts
/users/foo/src/vs/workbench/parts/surveys/electron-browser/nps.contribution.ts
/users/foo/src/vs/workbench/parts/scm
/users/foo/src/vs/workbench/parts/scm/common
/users/foo/src/vs/workbench/parts/scm/common/scm.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser
/users/foo/src/vs/workbench/parts/scm/electron-browser/scmUtil.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/scmViewlet.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/scmActivity.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/scmMenus.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/scm.contribution.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/dirtydiffDecorator.ts
/users/foo/src/vs/workbench/parts/scm/electron-browser/media
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/check-inverse.svg
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/icon-dark.svg
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/icon-light.svg
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/check.svg
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/dirtydiffDecorator.css
/users/foo/src/vs/workbench/parts/scm/electron-browser/media/scmViewlet.css
/users/foo/src/vs/workbench/parts/performance
/users/foo/src/vs/workbench/parts/performance/electron-browser
/users/foo/src/vs/workbench/parts/performance/electron-browser/stats.ts
/users/foo/src/vs/workbench/parts/performance/electron-browser/startupTimings.ts
/users/foo/src/vs/workbench/parts/performance/electron-browser/performance.contribution.ts
/users/foo/src/vs/workbench/parts/performance/electron-browser/startupProfiler.ts
/users/foo/src/vs/workbench/parts/backup
/users/foo/src/vs/workbench/parts/backup/common
/users/foo/src/vs/workbench/parts/backup/common/backupRestorer.ts
/users/foo/src/vs/workbench/parts/backup/common/backup.contribution.ts
/users/foo/src/vs/workbench/parts/backup/common/backupModelTracker.ts
/users/foo/src/vs/workbench/parts/themes
/users/foo/src/vs/workbench/parts/themes/test
/users/foo/src/vs/workbench/parts/themes/test/electron-browser
/users/foo/src/vs/workbench/parts/themes/test/electron-browser/themes.test.contribution.ts
/users/foo/src/vs/workbench/parts/themes/test/electron-browser/fixtures
/users/foo/src/vs/workbench/parts/themes/test/electron-browser/fixtures/foo.js
/users/foo/src/vs/workbench/parts/themes/electron-browser
/users/foo/src/vs/workbench/parts/themes/electron-browser/themes.contribution.ts
/users/foo/src/vs/workbench/parts/relauncher
/users/foo/src/vs/workbench/parts/relauncher/electron-browser
/users/foo/src/vs/workbench/parts/relauncher/electron-browser/relauncher.contribution.ts
/users/foo/src/vs/workbench/parts/stats
/users/foo/src/vs/workbench/parts/stats/test
/users/foo/src/vs/workbench/parts/stats/test/workspaceStats.test.ts
/users/foo/src/vs/workbench/parts/stats/node
/users/foo/src/vs/workbench/parts/stats/node/stats.contribution.ts
/users/foo/src/vs/workbench/parts/stats/node/workspaceStats.ts
/users/foo/src/vs/workbench/parts/debug
/users/foo/src/vs/workbench/parts/debug/test
/users/foo/src/vs/workbench/parts/debug/test/browser
/users/foo/src/vs/workbench/parts/debug/test/browser/debugANSIHandling.test.ts
/users/foo/src/vs/workbench/parts/debug/test/browser/baseDebugView.test.ts
/users/foo/src/vs/workbench/parts/debug/test/common
/users/foo/src/vs/workbench/parts/debug/test/common/debugUtils.test.ts
/users/foo/src/vs/workbench/parts/debug/test/common/mockDebug.ts
/users/foo/src/vs/workbench/parts/debug/test/common/replHistory.test.ts
/users/foo/src/vs/workbench/parts/debug/test/common/debugViewModel.test.ts
/users/foo/src/vs/workbench/parts/debug/test/common/debugSource.test.ts
/users/foo/src/vs/workbench/parts/debug/test/node
/users/foo/src/vs/workbench/parts/debug/test/node/debugModel.test.ts
/users/foo/src/vs/workbench/parts/debug/test/node/debugger.test.ts
/users/foo/src/vs/workbench/parts/debug/browser
/users/foo/src/vs/workbench/parts/debug/browser/baseDebugView.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugStatus.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugActionsWidget.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugActionItems.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugEditorActions.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugContentProvider.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugViewlet.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugQuickOpen.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugCommands.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugEditorModelManager.ts
/users/foo/src/vs/workbench/parts/debug/browser/breakpointsView.ts
/users/foo/src/vs/workbench/parts/debug/browser/linkDetector.ts
/users/foo/src/vs/workbench/parts/debug/browser/statusbarColorProvider.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugActions.ts
/users/foo/src/vs/workbench/parts/debug/browser/exceptionWidget.ts
/users/foo/src/vs/workbench/parts/debug/browser/debugANSIHandling.ts
/users/foo/src/vs/workbench/parts/debug/browser/media
/users/foo/src/vs/workbench/parts/debug/browser/media/step-out-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/debugViewlet.css
/users/foo/src/vs/workbench/parts/debug/browser/media/remove-all.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/restart.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-conditional.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/restart-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-function.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoints-activate.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/disconnect.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-conditional-disabled.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/disconnect-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/drag.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/clear-repl.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/debug.contribution.css
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-hint.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/continue-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-unsupported.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/clear-repl-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/exceptionWidget.css
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-log.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/continue.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/debugActionsWidget.css
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-function-disabled.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/stop.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpointWidget.css
/users/foo/src/vs/workbench/parts/debug/browser/media/add.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-log-unverified.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/remove-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/repl.css
/users/foo/src/vs/workbench/parts/debug/browser/media/current-arrow.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/remove-all-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/debugHover.css
/users/foo/src/vs/workbench/parts/debug/browser/media/start.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/start-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/debug-dark.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/repl-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/add-focus.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/step-over-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/step-into.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/step-into-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/repl.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-disabled.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/current-and-breakpoint.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/add-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/remove.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-log-disabled.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/stackframe-and-breakpoint.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-unverified.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/configure-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/stop-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/step-over.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/stackframe-arrow.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/pause.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-conditional-unverified.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/step-out.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/remove-focus.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoint-function-unverified.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/pause-inverse.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/configure.svg
/users/foo/src/vs/workbench/parts/debug/browser/media/breakpoints-activate-inverse.svg
/users/foo/src/vs/workbench/parts/debug/common
/users/foo/src/vs/workbench/parts/debug/common/debugSource.ts
/users/foo/src/vs/workbench/parts/debug/common/debugModel.ts
/users/foo/src/vs/workbench/parts/debug/common/debugUtils.ts
/users/foo/src/vs/workbench/parts/debug/common/debugSchemas.ts
/users/foo/src/vs/workbench/parts/debug/common/debugViewModel.ts
/users/foo/src/vs/workbench/parts/debug/common/debug.ts
/users/foo/src/vs/workbench/parts/debug/common/debugProtocol.d.ts
/users/foo/src/vs/workbench/parts/debug/common/replHistory.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser
/users/foo/src/vs/workbench/parts/debug/electron-browser/terminalSupport.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/debugEditorContribution.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/debug.contribution.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/debugConfigurationManager.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/rawDebugSession.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/debugHover.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/simpleDebugEditor.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/breakpointWidget.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/variablesView.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/electronDebugActions.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/repl.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/debugService.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/watchExpressionsView.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/replViewer.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/callStackView.ts
/users/foo/src/vs/workbench/parts/debug/electron-browser/media
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/stop-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/stepinto-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/continue-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/stepover-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/restart-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/stepout-tb.png
/users/foo/src/vs/workbench/parts/debug/electron-browser/media/pause-tb.png
/users/foo/src/vs/workbench/parts/debug/node
/users/foo/src/vs/workbench/parts/debug/node/debugger.ts
/users/foo/src/vs/workbench/parts/debug/node/telemetryApp.ts
/users/foo/src/vs/workbench/parts/debug/node/terminals.ts
/users/foo/src/vs/workbench/parts/debug/node/debugAdapter.ts
/users/foo/src/vs/workbench/api
/users/foo/src/vs/workbench/api/shared
/users/foo/src/vs/workbench/api/shared/tasks.ts
/users/foo/src/vs/workbench/api/browser
/users/foo/src/vs/workbench/api/browser/viewsExtensionPoint.ts
/users/foo/src/vs/workbench/api/browser/viewsContainersExtensionPoint.ts
/users/foo/src/vs/workbench/api/browser/media
/users/foo/src/vs/workbench/api/browser/media/test.svg
/users/foo/src/vs/workbench/api/electron-browser
/users/foo/src/vs/workbench/api/electron-browser/mainThreadSCM.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadSaveParticipant.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadTask.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDocuments.ts
/users/foo/src/vs/workbench/api/electron-browser/extHostCustomers.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadExtensionService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDebugService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadConfiguration.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDocumentsAndEditors.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadOutputService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadTelemetry.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadWindow.ts
/users/foo/src/vs/workbench/api/electron-browser/extensionHost.contribution.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadEditors.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDocumentContentProviders.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadStatusBar.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadFileSystem.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDiagnostics.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadWorkspace.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadHeapService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadCommands.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadWebview.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadLanguageFeatures.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDialogs.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadDecorations.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadTreeViews.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadProgress.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadLogService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadSearch.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadLanguages.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadStorage.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadUrls.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadEditor.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadErrors.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadFileSystemEventService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadMessageService.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadQuickOpen.ts
/users/foo/src/vs/workbench/api/electron-browser/mainThreadTerminalService.ts
/users/foo/src/vs/workbench/api/node
/users/foo/src/vs/workbench/api/node/extHostWorkspace.ts
/users/foo/src/vs/workbench/api/node/apiCommands.ts
/users/foo/src/vs/workbench/api/node/extHostDocumentsAndEditors.ts
/users/foo/src/vs/workbench/api/node/extHostHeapService.ts
/users/foo/src/vs/workbench/api/node/extHostDecorations.ts
/users/foo/src/vs/workbench/api/node/extHostTextEditors.ts
/users/foo/src/vs/workbench/api/node/extHostLanguageFeatures.ts
/users/foo/src/vs/workbench/api/node/extHostLanguages.ts
/users/foo/src/vs/workbench/api/node/extHostLogService.ts
/users/foo/src/vs/workbench/api/node/extHostTreeViews.ts
/users/foo/src/vs/workbench/api/node/extHostExtensionActivator.ts
/users/foo/src/vs/workbench/api/node/extHostWindow.ts
/users/foo/src/vs/workbench/api/node/extHostApiCommands.ts
/users/foo/src/vs/workbench/api/node/extHost.api.impl.ts
/users/foo/src/vs/workbench/api/node/extHostTask.ts
/users/foo/src/vs/workbench/api/node/extHostMessageService.ts
/users/foo/src/vs/workbench/api/node/extHostQuickOpen.ts
/users/foo/src/vs/workbench/api/node/extHostTerminalService.ts
/users/foo/src/vs/workbench/api/node/extHostDebugService.ts
/users/foo/src/vs/workbench/api/node/extHostStorage.ts
/users/foo/src/vs/workbench/api/node/extHostDocumentData.ts
/users/foo/src/vs/workbench/api/node/extHostCommands.ts
/users/foo/src/vs/workbench/api/node/extHostDocumentSaveParticipant.ts
/users/foo/src/vs/workbench/api/node/extHostOutputService.ts
/users/foo/src/vs/workbench/api/node/extHostDocuments.ts
/users/foo/src/vs/workbench/api/node/extHostUrls.ts
/users/foo/src/vs/workbench/api/node/extHostConfiguration.ts
/users/foo/src/vs/workbench/api/node/extHostExtensionService.ts
/users/foo/src/vs/workbench/api/node/extHostSCM.ts
/users/foo/src/vs/workbench/api/node/extHost.protocol.ts
/users/foo/src/vs/workbench/api/node/extHostProgress.ts
/users/foo/src/vs/workbench/api/node/extHostTextEditor.ts
/users/foo/src/vs/workbench/api/node/extHostSearch.ts
/users/foo/src/vs/workbench/api/node/extHostStatusBar.ts
/users/foo/src/vs/workbench/api/node/extHostFileSystemEventService.ts
/users/foo/src/vs/workbench/api/node/extHostDocumentContentProviders.ts
/users/foo/src/vs/workbench/api/node/extHostTypes.ts
/users/foo/src/vs/workbench/api/node/extHostWebview.ts
/users/foo/src/vs/workbench/api/node/extHostDialogs.ts
/users/foo/src/vs/workbench/api/node/extHostFileSystem.ts
/users/foo/src/vs/workbench/api/node/extHostTypeConverters.ts
/users/foo/src/vs/workbench/api/node/extHostDiagnostics.ts
/users/foo/src/vs/workbench/electron-browser
/users/foo/src/vs/workbench/electron-browser/commands.ts
/users/foo/src/vs/workbench/electron-browser/bootstrap
/users/foo/src/vs/workbench/electron-browser/bootstrap/preload.js
/users/foo/src/vs/workbench/electron-browser/bootstrap/index.html
/users/foo/src/vs/workbench/electron-browser/bootstrap/index.js
/users/foo/src/vs/workbench/electron-browser/main.ts
/users/foo/src/vs/workbench/electron-browser/main.contribution.ts
/users/foo/src/vs/workbench/electron-browser/resources.ts
/users/foo/src/vs/workbench/electron-browser/workbench.ts
/users/foo/src/vs/workbench/electron-browser/actions.ts
/users/foo/src/vs/workbench/electron-browser/shell.ts
/users/foo/src/vs/workbench/electron-browser/window.ts
/users/foo/src/vs/workbench/electron-browser/media
/users/foo/src/vs/workbench/electron-browser/media/clear.svg
/users/foo/src/vs/workbench/electron-browser/media/actions.css
/users/foo/src/vs/workbench/electron-browser/media/shell.css
/users/foo/src/vs/workbench/electron-browser/media/remove.svg
/users/foo/src/vs/workbench/electron-browser/media/remove-dark.svg
/users/foo/src/vs/workbench/electron-browser/media/workbench.css
/users/foo/src/vs/workbench/buildfile.js
/users/foo/src/vs/workbench/node
/users/foo/src/vs/workbench/node/extensionHostProcess.ts
/users/foo/src/vs/workbench/node/extensionHostMain.ts
/users/foo/src/vs/workbench/workbench.main.nls.js
/users/foo/src/vs/workbench/services
/users/foo/src/vs/workbench/services/keybinding
/users/foo/src/vs/workbench/services/keybinding/test
/users/foo/src/vs/workbench/services/keybinding/test/linux_en_uk.txt
/users/foo/src/vs/workbench/services/keybinding/test/linux_de_ch.txt
/users/foo/src/vs/workbench/services/keybinding/test/macLinuxKeyboardMapper.test.ts
/users/foo/src/vs/workbench/services/keybinding/test/win_por_ptb.js
/users/foo/src/vs/workbench/services/keybinding/test/linux_en_uk.js
/users/foo/src/vs/workbench/services/keybinding/test/win_ru.js
/users/foo/src/vs/workbench/services/keybinding/test/win_ru.txt
/users/foo/src/vs/workbench/services/keybinding/test/linux_ru.txt
/users/foo/src/vs/workbench/services/keybinding/test/linux_en_us.js
/users/foo/src/vs/workbench/services/keybinding/test/mac_en_us.txt
/users/foo/src/vs/workbench/services/keybinding/test/macLinuxFallbackKeyboardMapper.test.ts
/users/foo/src/vs/workbench/services/keybinding/test/win_en_us.js
/users/foo/src/vs/workbench/services/keybinding/test/win_en_us.txt
/users/foo/src/vs/workbench/services/keybinding/test/mac_en_us.js
/users/foo/src/vs/workbench/services/keybinding/test/mac_de_ch.js
/users/foo/src/vs/workbench/services/keybinding/test/win_de_ch.js
/users/foo/src/vs/workbench/services/keybinding/test/win_de_ch.txt
/users/foo/src/vs/workbench/services/keybinding/test/mac_de_ch.txt
/users/foo/src/vs/workbench/services/keybinding/test/win_por_ptb.txt
/users/foo/src/vs/workbench/services/keybinding/test/linux_de_ch.js
/users/foo/src/vs/workbench/services/keybinding/test/linux_ru.js
/users/foo/src/vs/workbench/services/keybinding/test/keybindingIO.test.ts
/users/foo/src/vs/workbench/services/keybinding/test/mac_zh_hant.js
/users/foo/src/vs/workbench/services/keybinding/test/electron-browser
/users/foo/src/vs/workbench/services/keybinding/test/electron-browser/keybindingEditing.test.ts
/users/foo/src/vs/workbench/services/keybinding/test/windowsKeyboardMapper.test.ts
/users/foo/src/vs/workbench/services/keybinding/test/keyboardMapperTestUtils.ts
/users/foo/src/vs/workbench/services/keybinding/test/linux_en_us.txt
/users/foo/src/vs/workbench/services/keybinding/test/mac_zh_hant.txt
/users/foo/src/vs/workbench/services/keybinding/common
/users/foo/src/vs/workbench/services/keybinding/common/keyboardMapper.ts
/users/foo/src/vs/workbench/services/keybinding/common/windowsKeyboardMapper.ts
/users/foo/src/vs/workbench/services/keybinding/common/keybindingEditing.ts
/users/foo/src/vs/workbench/services/keybinding/common/macLinuxFallbackKeyboardMapper.ts
/users/foo/src/vs/workbench/services/keybinding/common/scanCode.ts
/users/foo/src/vs/workbench/services/keybinding/common/macLinuxKeyboardMapper.ts
/users/foo/src/vs/workbench/services/keybinding/common/keybindingIO.ts
/users/foo/src/vs/workbench/services/keybinding/electron-browser
/users/foo/src/vs/workbench/services/keybinding/electron-browser/keybindingService.ts
/users/foo/src/vs/workbench/services/textmodelResolver
/users/foo/src/vs/workbench/services/textmodelResolver/test
/users/foo/src/vs/workbench/services/textmodelResolver/test/textModelResolverService.test.ts
/users/foo/src/vs/workbench/services/textmodelResolver/common
/users/foo/src/vs/workbench/services/textmodelResolver/common/textModelResolverService.ts
/users/foo/src/vs/workbench/services/configuration
/users/foo/src/vs/workbench/services/configuration/test
/users/foo/src/vs/workbench/services/configuration/test/common
/users/foo/src/vs/workbench/services/configuration/test/common/configurationModels.test.ts
/users/foo/src/vs/workbench/services/configuration/test/electron-browser
/users/foo/src/vs/workbench/services/configuration/test/electron-browser/configurationEditingService.test.ts
/users/foo/src/vs/workbench/services/configuration/test/electron-browser/configurationService.test.ts
/users/foo/src/vs/workbench/services/configuration/common
/users/foo/src/vs/workbench/services/configuration/common/configurationModels.ts
/users/foo/src/vs/workbench/services/configuration/common/jsonEditing.ts
/users/foo/src/vs/workbench/services/configuration/common/configurationExtensionPoint.ts
/users/foo/src/vs/workbench/services/configuration/common/configuration.ts
/users/foo/src/vs/workbench/services/configuration/node
/users/foo/src/vs/workbench/services/configuration/node/configurationService.ts
/users/foo/src/vs/workbench/services/configuration/node/jsonEditingService.ts
/users/foo/src/vs/workbench/services/configuration/node/configurationEditingService.ts
/users/foo/src/vs/workbench/services/configuration/node/configuration.ts
/users/foo/src/vs/workbench/services/preferences
/users/foo/src/vs/workbench/services/preferences/test
/users/foo/src/vs/workbench/services/preferences/test/common
/users/foo/src/vs/workbench/services/preferences/test/common/keybindingsEditorModel.test.ts
/users/foo/src/vs/workbench/services/preferences/browser
/users/foo/src/vs/workbench/services/preferences/browser/preferencesService.ts
/users/foo/src/vs/workbench/services/preferences/common
/users/foo/src/vs/workbench/services/preferences/common/preferencesModels.ts
/users/foo/src/vs/workbench/services/preferences/common/preferencesEditorInput.ts
/users/foo/src/vs/workbench/services/preferences/common/preferences.ts
/users/foo/src/vs/workbench/services/preferences/common/keybindingsEditorModel.ts
/users/foo/src/vs/workbench/services/panel
/users/foo/src/vs/workbench/services/panel/common
/users/foo/src/vs/workbench/services/panel/common/panelService.ts
/users/foo/src/vs/workbench/services/progress
/users/foo/src/vs/workbench/services/progress/test
/users/foo/src/vs/workbench/services/progress/test/progressService.test.ts
/users/foo/src/vs/workbench/services/progress/browser
/users/foo/src/vs/workbench/services/progress/browser/progressService2.ts
/users/foo/src/vs/workbench/services/progress/browser/progressService.ts
/users/foo/src/vs/workbench/services/progress/browser/media
/users/foo/src/vs/workbench/services/progress/browser/media/progressService2.css
/users/foo/src/vs/workbench/services/mode
/users/foo/src/vs/workbench/services/mode/common
/users/foo/src/vs/workbench/services/mode/common/workbenchModeService.ts
/users/foo/src/vs/workbench/services/hash
/users/foo/src/vs/workbench/services/hash/common
/users/foo/src/vs/workbench/services/hash/common/hashService.ts
/users/foo/src/vs/workbench/services/hash/node
/users/foo/src/vs/workbench/services/hash/node/hashService.ts
/users/foo/src/vs/workbench/services/title
/users/foo/src/vs/workbench/services/title/common
/users/foo/src/vs/workbench/services/title/common/titleService.ts
/users/foo/src/vs/workbench/services/group
/users/foo/src/vs/workbench/services/group/common
/users/foo/src/vs/workbench/services/group/common/groupService.ts
/users/foo/src/vs/workbench/services/notification
/users/foo/src/vs/workbench/services/notification/common
/users/foo/src/vs/workbench/services/notification/common/notificationService.ts
/users/foo/src/vs/workbench/services/activity
/users/foo/src/vs/workbench/services/activity/browser
/users/foo/src/vs/workbench/services/activity/browser/activityService.ts
/users/foo/src/vs/workbench/services/activity/common
/users/foo/src/vs/workbench/services/activity/common/activity.ts
/users/foo/src/vs/workbench/services/viewlet
/users/foo/src/vs/workbench/services/viewlet/browser
/users/foo/src/vs/workbench/services/viewlet/browser/viewlet.ts
/users/foo/src/vs/workbench/services/viewlet/browser/viewletService.ts
/users/foo/src/vs/workbench/services/workspace
/users/foo/src/vs/workbench/services/workspace/common
/users/foo/src/vs/workbench/services/workspace/common/workspaceEditing.ts
/users/foo/src/vs/workbench/services/workspace/node
/users/foo/src/vs/workbench/services/workspace/node/workspaceEditingService.ts
/users/foo/src/vs/workbench/services/timer
/users/foo/src/vs/workbench/services/timer/common
/users/foo/src/vs/workbench/services/timer/common/timerService.ts
/users/foo/src/vs/workbench/services/timer/node
/users/foo/src/vs/workbench/services/timer/node/timerService.ts
/users/foo/src/vs/workbench/services/textMate
/users/foo/src/vs/workbench/services/textMate/electron-browser
/users/foo/src/vs/workbench/services/textMate/electron-browser/textMateService.ts
/users/foo/src/vs/workbench/services/textMate/electron-browser/TMGrammars.ts
/users/foo/src/vs/workbench/services/textMate/electron-browser/TMSyntax.ts
/users/foo/src/vs/workbench/services/textMate/electron-browser/TMHelper.ts
/users/foo/src/vs/workbench/services/textMate/electron-browser/OSSREADME.json
/users/foo/src/vs/workbench/services/bulkEdit
/users/foo/src/vs/workbench/services/bulkEdit/electron-browser
/users/foo/src/vs/workbench/services/bulkEdit/electron-browser/bulkEditService.ts
/users/foo/src/vs/workbench/services/jsonschemas
/users/foo/src/vs/workbench/services/jsonschemas/common
/users/foo/src/vs/workbench/services/jsonschemas/common/jsonValidationExtensionPoint.ts
/users/foo/src/vs/workbench/services/contextview
/users/foo/src/vs/workbench/services/contextview/electron-browser
/users/foo/src/vs/workbench/services/contextview/electron-browser/contextmenuService.ts
/users/foo/src/vs/workbench/services/crashReporter
/users/foo/src/vs/workbench/services/crashReporter/electron-browser
/users/foo/src/vs/workbench/services/crashReporter/electron-browser/crashReporterService.ts
/users/foo/src/vs/workbench/services/decorations
/users/foo/src/vs/workbench/services/decorations/test
/users/foo/src/vs/workbench/services/decorations/test/browser
/users/foo/src/vs/workbench/services/decorations/test/browser/decorationsService.test.ts
/users/foo/src/vs/workbench/services/decorations/browser
/users/foo/src/vs/workbench/services/decorations/browser/decorations.ts
/users/foo/src/vs/workbench/services/decorations/browser/decorationsService.ts
/users/foo/src/vs/workbench/services/extensions
/users/foo/src/vs/workbench/services/extensions/common
/users/foo/src/vs/workbench/services/extensions/common/extensionsRegistry.ts
/users/foo/src/vs/workbench/services/extensions/common/extensions.ts
/users/foo/src/vs/workbench/services/extensions/electron-browser
/users/foo/src/vs/workbench/services/extensions/electron-browser/extensionHostProfiler.ts
/users/foo/src/vs/workbench/services/extensions/electron-browser/extensionService.ts
/users/foo/src/vs/workbench/services/extensions/electron-browser/extensionHost.ts
/users/foo/src/vs/workbench/services/extensions/node
/users/foo/src/vs/workbench/services/extensions/node/extensionDescriptionRegistry.ts
/users/foo/src/vs/workbench/services/extensions/node/rpcProtocol.ts
/users/foo/src/vs/workbench/services/extensions/node/lazyPromise.ts
/users/foo/src/vs/workbench/services/extensions/node/extensionPoints.ts
/users/foo/src/vs/workbench/services/extensions/node/proxyIdentifier.ts
/users/foo/src/vs/workbench/services/search
/users/foo/src/vs/workbench/services/search/test
/users/foo/src/vs/workbench/services/search/test/node
/users/foo/src/vs/workbench/services/search/test/node/ripgrepTextSearch.test.ts
/users/foo/src/vs/workbench/services/search/test/node/textSearch.integrationTest.ts
/users/foo/src/vs/workbench/services/search/test/node/searchService.test.ts
/users/foo/src/vs/workbench/services/search/test/node/fixtures2
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438/more
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438/more/modules
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438/more/modules/find.txt
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438/modules
/users/foo/src/vs/workbench/services/search/test/node/fixtures2/36438/modules/do-not-find.txt
/users/foo/src/vs/workbench/services/search/test/node/search.test.ts
/users/foo/src/vs/workbench/services/search/test/node/fixtures
/users/foo/src/vs/workbench/services/search/test/node/fixtures/index.html
/users/foo/src/vs/workbench/services/search/test/node/fixtures/binary.wuff
/users/foo/src/vs/workbench/services/search/test/node/fixtures/üm laut汉语
/users/foo/src/vs/workbench/services/search/test/node/fixtures/üm laut汉语/汉语.txt
/users/foo/src/vs/workbench/services/search/test/node/fixtures/some_utf16le.css
/users/foo/src/vs/workbench/services/search/test/node/fixtures/more
/users/foo/src/vs/workbench/services/search/test/node/fixtures/more/file.txt
/users/foo/src/vs/workbench/services/search/test/node/fixtures/some_utf16be.css
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/subfolder
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/subfolder/subfile.txt
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/subfolder/anotherfolder
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/subfolder/anotherfolder/anotherfile.txt
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/company.js
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/NullPoinderException.js
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/employee.js
/users/foo/src/vs/workbench/services/search/test/node/fixtures/examples/small.js
/users/foo/src/vs/workbench/services/search/test/node/fixtures/site.css
/users/foo/src/vs/workbench/services/search/test/node/fixtures/site.less
/users/foo/src/vs/workbench/services/search/node
/users/foo/src/vs/workbench/services/search/node/fileSearch.ts
/users/foo/src/vs/workbench/services/search/node/searchIpc.ts
/users/foo/src/vs/workbench/services/search/node/search.ts
/users/foo/src/vs/workbench/services/search/node/ripgrepTextSearch.ts
/users/foo/src/vs/workbench/services/search/node/textSearchWorkerProvider.ts
/users/foo/src/vs/workbench/services/search/node/textSearch.ts
/users/foo/src/vs/workbench/services/search/node/searchService.ts
/users/foo/src/vs/workbench/services/search/node/ripgrepFileSearch.ts
/users/foo/src/vs/workbench/services/search/node/worker
/users/foo/src/vs/workbench/services/search/node/worker/searchWorkerIpc.ts
/users/foo/src/vs/workbench/services/search/node/worker/searchWorkerApp.ts
/users/foo/src/vs/workbench/services/search/node/worker/searchWorker.ts
/users/foo/src/vs/workbench/services/search/node/searchApp.ts
/users/foo/src/vs/workbench/services/search/node/rawSearchService.ts
/users/foo/src/vs/workbench/services/textfile
/users/foo/src/vs/workbench/services/textfile/test
/users/foo/src/vs/workbench/services/textfile/test/textFileService.test.ts
/users/foo/src/vs/workbench/services/textfile/test/textFileEditorModel.test.ts
/users/foo/src/vs/workbench/services/textfile/test/textFileEditorModelManager.test.ts
/users/foo/src/vs/workbench/services/textfile/common
/users/foo/src/vs/workbench/services/textfile/common/textFileEditorModel.ts
/users/foo/src/vs/workbench/services/textfile/common/textFileService.ts
/users/foo/src/vs/workbench/services/textfile/common/textfiles.ts
/users/foo/src/vs/workbench/services/textfile/common/textFileEditorModelManager.ts
/users/foo/src/vs/workbench/services/textfile/electron-browser
/users/foo/src/vs/workbench/services/textfile/electron-browser/textFileService.ts
/users/foo/src/vs/workbench/services/issue
/users/foo/src/vs/workbench/services/issue/common
/users/foo/src/vs/workbench/services/issue/common/issue.ts
/users/foo/src/vs/workbench/services/issue/electron-browser
/users/foo/src/vs/workbench/services/issue/electron-browser/workbenchIssueService.ts
/users/foo/src/vs/workbench/services/history
/users/foo/src/vs/workbench/services/history/common
/users/foo/src/vs/workbench/services/history/common/history.ts
/users/foo/src/vs/workbench/services/history/electron-browser
/users/foo/src/vs/workbench/services/history/electron-browser/history.ts
/users/foo/src/vs/workbench/services/actions
/users/foo/src/vs/workbench/services/actions/test
/users/foo/src/vs/workbench/services/actions/test/common
/users/foo/src/vs/workbench/services/actions/test/common/menuService.test.ts
/users/foo/src/vs/workbench/services/actions/common
/users/foo/src/vs/workbench/services/actions/common/menuService.ts
/users/foo/src/vs/workbench/services/actions/electron-browser
/users/foo/src/vs/workbench/services/actions/electron-browser/menusExtensionPoint.ts
/users/foo/src/vs/workbench/services/files
/users/foo/src/vs/workbench/services/files/test
/users/foo/src/vs/workbench/services/files/test/electron-browser
/users/foo/src/vs/workbench/services/files/test/electron-browser/watcher.test.ts
/users/foo/src/vs/workbench/services/files/test/electron-browser/utils.ts
/users/foo/src/vs/workbench/services/files/test/electron-browser/resolver.test.ts
/users/foo/src/vs/workbench/services/files/test/electron-browser/fileService.test.ts
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/index.html
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/small_umlaut.txt
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/some_utf8_bom.txt
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/deep
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/deep/company.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/deep/conway.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/deep/employee.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/deep/small.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/some_utf16le.css
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/lorem.txt
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/small.txt
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/service/binary.txt
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/index.html
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other/deep
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other/deep/company.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other/deep/conway.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other/deep/employee.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/other/deep/small.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/examples
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/examples/company.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/examples/conway.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/examples/employee.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/examples/small.js
/users/foo/src/vs/workbench/services/files/test/electron-browser/fixtures/resolver/site.css
/users/foo/src/vs/workbench/services/files/electron-browser
/users/foo/src/vs/workbench/services/files/electron-browser/remoteFileService.ts
/users/foo/src/vs/workbench/services/files/electron-browser/encoding.ts
/users/foo/src/vs/workbench/services/files/electron-browser/streams.ts
/users/foo/src/vs/workbench/services/files/electron-browser/fileService.ts
/users/foo/src/vs/workbench/services/files/node
/users/foo/src/vs/workbench/services/files/node/watcher
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/nsfwWatcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/test
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/test/nsfwWatcherService.test.ts
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/watcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/watcher.ts
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/watcherIpc.ts
/users/foo/src/vs/workbench/services/files/node/watcher/nsfw/watcherApp.ts
/users/foo/src/vs/workbench/services/files/node/watcher/common.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix
/users/foo/src/vs/workbench/services/files/node/watcher/unix/test
/users/foo/src/vs/workbench/services/files/node/watcher/unix/test/chockidarWatcherService.test.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix/watcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix/watcher.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix/watcherIpc.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix/watcherApp.ts
/users/foo/src/vs/workbench/services/files/node/watcher/unix/chokidarWatcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/win32
/users/foo/src/vs/workbench/services/files/node/watcher/win32/csharpWatcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/win32/watcherService.ts
/users/foo/src/vs/workbench/services/files/node/watcher/win32/CodeHelper.md
/users/foo/src/vs/workbench/services/files/node/watcher/win32/CodeHelper.exe
/users/foo/src/vs/workbench/services/dialogs
/users/foo/src/vs/workbench/services/dialogs/electron-browser
/users/foo/src/vs/workbench/services/dialogs/electron-browser/dialogService.ts
/users/foo/src/vs/workbench/services/commands
/users/foo/src/vs/workbench/services/commands/test
/users/foo/src/vs/workbench/services/commands/test/common
/users/foo/src/vs/workbench/services/commands/test/common/commandService.test.ts
/users/foo/src/vs/workbench/services/commands/common
/users/foo/src/vs/workbench/services/commands/common/commandService.ts
/users/foo/src/vs/workbench/services/part
/users/foo/src/vs/workbench/services/part/common
/users/foo/src/vs/workbench/services/part/common/partService.ts
/users/foo/src/vs/workbench/services/scm
/users/foo/src/vs/workbench/services/scm/common
/users/foo/src/vs/workbench/services/scm/common/scm.ts
/users/foo/src/vs/workbench/services/scm/common/scmService.ts
/users/foo/src/vs/workbench/services/configurationResolver
/users/foo/src/vs/workbench/services/configurationResolver/test
/users/foo/src/vs/workbench/services/configurationResolver/test/electron-browser
/users/foo/src/vs/workbench/services/configurationResolver/test/electron-browser/configurationResolverService.test.ts
/users/foo/src/vs/workbench/services/configurationResolver/common
/users/foo/src/vs/workbench/services/configurationResolver/common/configurationResolver.ts
/users/foo/src/vs/workbench/services/configurationResolver/electron-browser
/users/foo/src/vs/workbench/services/configurationResolver/electron-browser/configurationResolverService.ts
/users/foo/src/vs/workbench/services/configurationResolver/node
/users/foo/src/vs/workbench/services/configurationResolver/node/variableResolver.ts
/users/foo/src/vs/workbench/services/untitled
/users/foo/src/vs/workbench/services/untitled/common
/users/foo/src/vs/workbench/services/untitled/common/untitledEditorService.ts
/users/foo/src/vs/workbench/services/backup
/users/foo/src/vs/workbench/services/backup/test
/users/foo/src/vs/workbench/services/backup/test/electron-browser
/users/foo/src/vs/workbench/services/backup/test/electron-browser/backupFileService.test.ts
/users/foo/src/vs/workbench/services/backup/common
/users/foo/src/vs/workbench/services/backup/common/backup.ts
/users/foo/src/vs/workbench/services/backup/node
/users/foo/src/vs/workbench/services/backup/node/backupFileService.ts
/users/foo/src/vs/workbench/services/themes
/users/foo/src/vs/workbench/services/themes/common
/users/foo/src/vs/workbench/services/themes/common/fileIconThemeSchema.ts
/users/foo/src/vs/workbench/services/themes/common/colorThemeSchema.ts
/users/foo/src/vs/workbench/services/themes/common/workbenchThemeService.ts
/users/foo/src/vs/workbench/services/themes/common/colorExtensionPoint.ts
/users/foo/src/vs/workbench/services/themes/electron-browser
/users/foo/src/vs/workbench/services/themes/electron-browser/colorThemeStore.ts
/users/foo/src/vs/workbench/services/themes/electron-browser/colorThemeData.ts
/users/foo/src/vs/workbench/services/themes/electron-browser/fileIconThemeStore.ts
/users/foo/src/vs/workbench/services/themes/electron-browser/workbenchThemeService.ts
/users/foo/src/vs/workbench/services/themes/electron-browser/fileIconThemeData.ts
/users/foo/src/vs/workbench/services/themes/electron-browser/themeCompatibility.ts
/users/foo/src/vs/workbench/services/editor
/users/foo/src/vs/workbench/services/editor/test
/users/foo/src/vs/workbench/services/editor/test/browser
/users/foo/src/vs/workbench/services/editor/test/browser/editorService.test.ts
/users/foo/src/vs/workbench/services/editor/common
/users/foo/src/vs/workbench/services/editor/common/editorService.ts
/users/foo/src/vs/css.d.ts
/users/foo/src/vs/nls.mock.ts
/users/foo/src/vs/css.js
/users/foo/src/vs/nls.build.js
/users/foo/src/vs/base
/users/foo/src/vs/base/test
/users/foo/src/vs/base/test/browser
/users/foo/src/vs/base/test/browser/ui
/users/foo/src/vs/base/test/browser/ui/splitview
/users/foo/src/vs/base/test/browser/ui/splitview/splitview.test.ts
/users/foo/src/vs/base/test/browser/ui/list
/users/foo/src/vs/base/test/browser/ui/list/rangeMap.test.ts
/users/foo/src/vs/base/test/browser/ui/scrollbar
/users/foo/src/vs/base/test/browser/ui/scrollbar/scrollableElement.test.ts
/users/foo/src/vs/base/test/browser/ui/scrollbar/scrollbarState.test.ts
/users/foo/src/vs/base/test/browser/browser.test.ts
/users/foo/src/vs/base/test/browser/htmlContent.test.ts
/users/foo/src/vs/base/test/browser/comparers.test.ts
/users/foo/src/vs/base/test/browser/progressBar.test.ts
/users/foo/src/vs/base/test/browser/builder.test.ts
/users/foo/src/vs/base/test/browser/highlightedLabel.test.ts
/users/foo/src/vs/base/test/browser/dom.test.ts
/users/foo/src/vs/base/test/common
/users/foo/src/vs/base/test/common/paging.test.ts
/users/foo/src/vs/base/test/common/filters.test.ts
/users/foo/src/vs/base/test/common/uri.test.ts
/users/foo/src/vs/base/test/common/arrays.test.ts
/users/foo/src/vs/base/test/common/keyCodes.test.ts
/users/foo/src/vs/base/test/common/errors.test.ts
/users/foo/src/vs/base/test/common/collections.test.ts
/users/foo/src/vs/base/test/common/async.test.ts
/users/foo/src/vs/base/test/common/assert.test.ts
/users/foo/src/vs/base/test/common/decorators.test.ts
/users/foo/src/vs/base/test/common/scrollable.test.ts
/users/foo/src/vs/base/test/common/uuid.test.ts
/users/foo/src/vs/base/test/common/cache.test.ts
/users/foo/src/vs/base/test/common/labels.test.ts
/users/foo/src/vs/base/test/common/filters.perf.test.ts
/users/foo/src/vs/base/test/common/objects.test.ts
/users/foo/src/vs/base/test/common/types.test.ts
/users/foo/src/vs/base/test/common/history.test.ts
/users/foo/src/vs/base/test/common/network.test.ts
/users/foo/src/vs/base/test/common/filters.perf.data.d.ts
/users/foo/src/vs/base/test/common/color.test.ts
/users/foo/src/vs/base/test/common/filters.perf.data.js
/users/foo/src/vs/base/test/common/utils.ts
/users/foo/src/vs/base/test/common/diff
/users/foo/src/vs/base/test/common/diff/diff.test.ts
/users/foo/src/vs/base/test/common/resources.test.ts
/users/foo/src/vs/base/test/common/charCode.test.ts
/users/foo/src/vs/base/test/common/event.test.ts
/users/foo/src/vs/base/test/common/jsonFormatter.test.ts
/users/foo/src/vs/base/test/common/hash.test.ts
/users/foo/src/vs/base/test/common/winjs.polyfill.promise.test.ts
/users/foo/src/vs/base/test/common/json.test.ts
/users/foo/src/vs/base/test/common/marshalling.test.ts
/users/foo/src/vs/base/test/common/strings.test.ts
/users/foo/src/vs/base/test/common/graph.test.ts
/users/foo/src/vs/base/test/common/mime.test.ts
/users/foo/src/vs/base/test/common/octicon.test.ts
/users/foo/src/vs/base/test/common/map.test.ts
/users/foo/src/vs/base/test/common/jsonEdit.test.ts
/users/foo/src/vs/base/test/common/paths.test.ts
/users/foo/src/vs/base/test/common/cancellation.test.ts
/users/foo/src/vs/base/test/common/linkedList.test.ts
/users/foo/src/vs/base/test/common/lifecycle.test.ts
/users/foo/src/vs/base/test/node
/users/foo/src/vs/base/test/node/glob.test.ts
/users/foo/src/vs/base/test/node/decoder.test.ts
/users/foo/src/vs/base/test/node/extfs
/users/foo/src/vs/base/test/node/extfs/extfs.test.ts
/users/foo/src/vs/base/test/node/extfs/fixtures
/users/foo/src/vs/base/test/node/extfs/fixtures/index.html
/users/foo/src/vs/base/test/node/extfs/fixtures/examples
/users/foo/src/vs/base/test/node/extfs/fixtures/examples/small.jxs
/users/foo/src/vs/base/test/node/extfs/fixtures/examples/conway.jxs
/users/foo/src/vs/base/test/node/extfs/fixtures/examples/company.jxs
/users/foo/src/vs/base/test/node/extfs/fixtures/examples/employee.jxs
/users/foo/src/vs/base/test/node/extfs/fixtures/site.css
/users/foo/src/vs/base/test/node/processes
/users/foo/src/vs/base/test/node/processes/processes.test.ts
/users/foo/src/vs/base/test/node/processes/fixtures
/users/foo/src/vs/base/test/node/processes/fixtures/fork_large.ts
/users/foo/src/vs/base/test/node/processes/fixtures/fork.ts
/users/foo/src/vs/base/test/node/stream
/users/foo/src/vs/base/test/node/stream/stream.test.ts
/users/foo/src/vs/base/test/node/stream/fixtures
/users/foo/src/vs/base/test/node/stream/fixtures/empty.txt
/users/foo/src/vs/base/test/node/stream/fixtures/file.css
/users/foo/src/vs/base/test/node/utils.ts
/users/foo/src/vs/base/test/node/pfs.test.ts
/users/foo/src/vs/base/test/node/encoding
/users/foo/src/vs/base/test/node/encoding/encoding.test.ts
/users/foo/src/vs/base/test/node/encoding/fixtures
/users/foo/src/vs/base/test/node/encoding/fixtures/some.css.qwoff
/users/foo/src/vs/base/test/node/encoding/fixtures/empty.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/utf16_le_nobom.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/some.shiftjis.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/some.json.png
/users/foo/src/vs/base/test/node/encoding/fixtures/some.cp1252.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/some.qwoff.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/some.xml.png
/users/foo/src/vs/base/test/node/encoding/fixtures/some_utf16le.css
/users/foo/src/vs/base/test/node/encoding/fixtures/some_utf16be.css
/users/foo/src/vs/base/test/node/encoding/fixtures/some_utf8.css
/users/foo/src/vs/base/test/node/encoding/fixtures/some.png.txt
/users/foo/src/vs/base/test/node/encoding/fixtures/some.pdf
/users/foo/src/vs/base/test/node/encoding/fixtures/some_ansi.css
/users/foo/src/vs/base/test/node/encoding/fixtures/utf16_be_nobom.txt
/users/foo/src/vs/base/test/node/console.test.ts
/users/foo/src/vs/base/test/node/zip
/users/foo/src/vs/base/test/node/zip/zip.test.ts
/users/foo/src/vs/base/test/node/zip/fixtures
/users/foo/src/vs/base/test/node/zip/fixtures/extract.zip
/users/foo/src/vs/base/test/node/id.test.ts
/users/foo/src/vs/base/test/node/port.test.ts
/users/foo/src/vs/base/test/node/flow.test.ts
/users/foo/src/vs/base/test/node/config.test.ts
/users/foo/src/vs/base/browser
/users/foo/src/vs/base/browser/contextmenu.ts
/users/foo/src/vs/base/browser/ui
/users/foo/src/vs/base/browser/ui/dropdown
/users/foo/src/vs/base/browser/ui/dropdown/dropdown.ts
/users/foo/src/vs/base/browser/ui/dropdown/dropdown.css
/users/foo/src/vs/base/browser/ui/splitview
/users/foo/src/vs/base/browser/ui/splitview/arrow-collapse-dark.svg
/users/foo/src/vs/base/browser/ui/splitview/splitview.css
/users/foo/src/vs/base/browser/ui/splitview/panelview.css
/users/foo/src/vs/base/browser/ui/splitview/splitview.ts
/users/foo/src/vs/base/browser/ui/splitview/panelview.ts
/users/foo/src/vs/base/browser/ui/splitview/arrow-expand-dark.svg
/users/foo/src/vs/base/browser/ui/splitview/arrow-collapse.svg
/users/foo/src/vs/base/browser/ui/splitview/arrow-expand.svg
/users/foo/src/vs/base/browser/ui/iconLabel
/users/foo/src/vs/base/browser/ui/iconLabel/iconLabel.ts
/users/foo/src/vs/base/browser/ui/iconLabel/iconlabel.css
/users/foo/src/vs/base/browser/ui/keybindingLabel
/users/foo/src/vs/base/browser/ui/keybindingLabel/keybindingLabel.css
/users/foo/src/vs/base/browser/ui/keybindingLabel/keybindingLabel.ts
/users/foo/src/vs/base/browser/ui/octiconLabel
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.ttf
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/sprockets-octicons.scss
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.eot
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.woff
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/README.md
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.scss
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.css
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons-animations.css
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.svg
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/OSSREADME.json
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons-local.ttf
/users/foo/src/vs/base/browser/ui/octiconLabel/octicons/octicons.less
/users/foo/src/vs/base/browser/ui/octiconLabel/octiconLabel.ts
/users/foo/src/vs/base/browser/ui/octiconLabel/octiconLabel.mock.ts
/users/foo/src/vs/base/browser/ui/toolbar
/users/foo/src/vs/base/browser/ui/toolbar/ellipsis-inverse.svg
/users/foo/src/vs/base/browser/ui/toolbar/toolbar.css
/users/foo/src/vs/base/browser/ui/toolbar/toolbar.ts
/users/foo/src/vs/base/browser/ui/toolbar/ellipsis.svg
/users/foo/src/vs/base/browser/ui/checkbox
/users/foo/src/vs/base/browser/ui/checkbox/checkbox.ts
/users/foo/src/vs/base/browser/ui/checkbox/checkbox.css
/users/foo/src/vs/base/browser/ui/progressbar
/users/foo/src/vs/base/browser/ui/progressbar/progressbar.css
/users/foo/src/vs/base/browser/ui/progressbar/progressbar.ts
/users/foo/src/vs/base/browser/ui/contextview
/users/foo/src/vs/base/browser/ui/contextview/contextview.ts
/users/foo/src/vs/base/browser/ui/contextview/contextview.css
/users/foo/src/vs/base/browser/ui/selectBox
/users/foo/src/vs/base/browser/ui/selectBox/selectBox.ts
/users/foo/src/vs/base/browser/ui/selectBox/selectBoxNative.ts
/users/foo/src/vs/base/browser/ui/selectBox/selectBoxCustom.css
/users/foo/src/vs/base/browser/ui/selectBox/selectBoxCustom.ts
/users/foo/src/vs/base/browser/ui/selectBox/selectBox.css
/users/foo/src/vs/base/browser/ui/countBadge
/users/foo/src/vs/base/browser/ui/countBadge/countBadge.ts
/users/foo/src/vs/base/browser/ui/countBadge/countBadge.css
/users/foo/src/vs/base/browser/ui/button
/users/foo/src/vs/base/browser/ui/button/button.ts
/users/foo/src/vs/base/browser/ui/button/button.css
/users/foo/src/vs/base/browser/ui/highlightedlabel
/users/foo/src/vs/base/browser/ui/highlightedlabel/highlightedLabel.ts
/users/foo/src/vs/base/browser/ui/aria
/users/foo/src/vs/base/browser/ui/aria/aria.css
/users/foo/src/vs/base/browser/ui/aria/aria.ts
/users/foo/src/vs/base/browser/ui/list
/users/foo/src/vs/base/browser/ui/list/list.css
/users/foo/src/vs/base/browser/ui/list/rangeMap.ts
/users/foo/src/vs/base/browser/ui/list/listWidget.ts
/users/foo/src/vs/base/browser/ui/list/listView.ts
/users/foo/src/vs/base/browser/ui/list/listPaging.ts
/users/foo/src/vs/base/browser/ui/list/rowCache.ts
/users/foo/src/vs/base/browser/ui/list/list.ts
/users/foo/src/vs/base/browser/ui/menu
/users/foo/src/vs/base/browser/ui/menu/menu.ts
/users/foo/src/vs/base/browser/ui/menu/menu.css
/users/foo/src/vs/base/browser/ui/actionbar
/users/foo/src/vs/base/browser/ui/actionbar/actionbar.css
/users/foo/src/vs/base/browser/ui/actionbar/actionbar.ts
/users/foo/src/vs/base/browser/ui/findinput
/users/foo/src/vs/base/browser/ui/findinput/findInput.ts
/users/foo/src/vs/base/browser/ui/findinput/whole-word.svg
/users/foo/src/vs/base/browser/ui/findinput/findInput.css
/users/foo/src/vs/base/browser/ui/findinput/regex.svg
/users/foo/src/vs/base/browser/ui/findinput/case-sensitive.svg
/users/foo/src/vs/base/browser/ui/findinput/findInputCheckboxes.css
/users/foo/src/vs/base/browser/ui/findinput/case-sensitive-dark.svg
/users/foo/src/vs/base/browser/ui/findinput/whole-word-dark.svg
/users/foo/src/vs/base/browser/ui/findinput/findInputCheckboxes.ts
/users/foo/src/vs/base/browser/ui/findinput/regex-dark.svg
/users/foo/src/vs/base/browser/ui/widget.ts
/users/foo/src/vs/base/browser/ui/sash
/users/foo/src/vs/base/browser/ui/sash/sash.ts
/users/foo/src/vs/base/browser/ui/sash/sash.css
/users/foo/src/vs/base/browser/ui/inputbox
/users/foo/src/vs/base/browser/ui/inputbox/inputBox.ts
/users/foo/src/vs/base/browser/ui/inputbox/inputBox.css
/users/foo/src/vs/base/browser/ui/scrollbar
/users/foo/src/vs/base/browser/ui/scrollbar/verticalScrollbar.ts
/users/foo/src/vs/base/browser/ui/scrollbar/scrollbarArrow.ts
/users/foo/src/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.ts
/users/foo/src/vs/base/browser/ui/scrollbar/abstractScrollbar.ts
/users/foo/src/vs/base/browser/ui/scrollbar/horizontalScrollbar.ts
/users/foo/src/vs/base/browser/ui/scrollbar/scrollbarState.ts
/users/foo/src/vs/base/browser/ui/scrollbar/scrollableElementOptions.ts
/users/foo/src/vs/base/browser/ui/scrollbar/scrollableElement.ts
/users/foo/src/vs/base/browser/ui/scrollbar/media
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-down.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-left-dark.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-right.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/scrollbars.css
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-up-dark.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-down-dark.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-right-dark.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-up.svg
/users/foo/src/vs/base/browser/ui/scrollbar/media/arrow-left.svg
/users/foo/src/vs/base/browser/dom.ts
/users/foo/src/vs/base/browser/builder.css
/users/foo/src/vs/base/browser/mouseEvent.ts
/users/foo/src/vs/base/browser/touch.ts
/users/foo/src/vs/base/browser/fastDomNode.ts
/users/foo/src/vs/base/browser/builder.ts
/users/foo/src/vs/base/browser/browser.ts
/users/foo/src/vs/base/browser/iframe.ts
/users/foo/src/vs/base/browser/globalMouseMoveMonitor.ts
/users/foo/src/vs/base/browser/dnd.ts
/users/foo/src/vs/base/browser/htmlContentRenderer.ts
/users/foo/src/vs/base/browser/event.ts
/users/foo/src/vs/base/browser/keyboardEvent.ts
/users/foo/src/vs/base/common
/users/foo/src/vs/base/common/decorators.ts
/users/foo/src/vs/base/common/history.ts
/users/foo/src/vs/base/common/winjs.base.js
/users/foo/src/vs/base/common/filters.ts
/users/foo/src/vs/base/common/uuid.ts
/users/foo/src/vs/base/common/strings.ts
/users/foo/src/vs/base/common/paging.ts
/users/foo/src/vs/base/common/date.ts
/users/foo/src/vs/base/common/json.ts
/users/foo/src/vs/base/common/charCode.ts
/users/foo/src/vs/base/common/normalization.ts
/users/foo/src/vs/base/common/jsonSchema.ts
/users/foo/src/vs/base/common/objects.ts
/users/foo/src/vs/base/common/htmlContent.ts
/users/foo/src/vs/base/common/async.ts
/users/foo/src/vs/base/common/errors.ts
/users/foo/src/vs/base/common/resources.ts
/users/foo/src/vs/base/common/network.ts
/users/foo/src/vs/base/common/comparers.ts
/users/foo/src/vs/base/common/marked
/users/foo/src/vs/base/common/marked/marked.license.txt
/users/foo/src/vs/base/common/marked/marked.js
/users/foo/src/vs/base/common/marked/OSSREADME.json
/users/foo/src/vs/base/common/marked/marked.d.ts
/users/foo/src/vs/base/common/keyCodes.ts
/users/foo/src/vs/base/common/lifecycle.ts
/users/foo/src/vs/base/common/uri.ts
/users/foo/src/vs/base/common/errorMessage.ts
/users/foo/src/vs/base/common/iterator.ts
/users/foo/src/vs/base/common/collections.ts
/users/foo/src/vs/base/common/octicon.ts
/users/foo/src/vs/base/common/functional.ts
/users/foo/src/vs/base/common/scrollable.ts
/users/foo/src/vs/base/common/diff
/users/foo/src/vs/base/common/diff/diff.ts
/users/foo/src/vs/base/common/diff/diffChange.ts
/users/foo/src/vs/base/common/processes.ts
/users/foo/src/vs/base/common/jsonErrorMessages.ts
/users/foo/src/vs/base/common/platform.ts
/users/foo/src/vs/base/common/stopwatch.ts
/users/foo/src/vs/base/common/hash.ts
/users/foo/src/vs/base/common/idGenerator.ts
/users/foo/src/vs/base/common/glob.ts
/users/foo/src/vs/base/common/map.ts
/users/foo/src/vs/base/common/types.ts
/users/foo/src/vs/base/common/assert.ts
/users/foo/src/vs/base/common/performance.js
/users/foo/src/vs/base/common/winjs.polyfill.promise.ts
/users/foo/src/vs/base/common/numbers.ts
/users/foo/src/vs/base/common/parsers.ts
/users/foo/src/vs/base/common/performance.d.ts
/users/foo/src/vs/base/common/jsonEdit.ts
/users/foo/src/vs/base/common/cache.ts
/users/foo/src/vs/base/common/actions.ts
/users/foo/src/vs/base/common/buildunit.json
/users/foo/src/vs/base/common/winjs.base.d.ts
/users/foo/src/vs/base/common/graph.ts
/users/foo/src/vs/base/common/linkedList.ts
/users/foo/src/vs/base/common/diagnostics.ts
/users/foo/src/vs/base/common/sequence.ts
/users/foo/src/vs/base/common/cancellation.ts
/users/foo/src/vs/base/common/severity.ts
/users/foo/src/vs/base/common/paths.ts
/users/foo/src/vs/base/common/jsonFormatter.ts
/users/foo/src/vs/base/common/worker
/users/foo/src/vs/base/common/worker/simpleWorker.ts
/users/foo/src/vs/base/common/color.ts
/users/foo/src/vs/base/common/event.ts
/users/foo/src/vs/base/common/marshalling.ts
/users/foo/src/vs/base/common/mime.ts
/users/foo/src/vs/base/common/keybindingLabels.ts
/users/foo/src/vs/base/common/labels.ts
/users/foo/src/vs/base/common/arrays.ts
/users/foo/src/vs/base/parts
/users/foo/src/vs/base/parts/tree
/users/foo/src/vs/base/parts/tree/test
/users/foo/src/vs/base/parts/tree/test/browser
/users/foo/src/vs/base/parts/tree/test/browser/treeViewModel.test.ts
/users/foo/src/vs/base/parts/tree/test/browser/treeModel.test.ts
/users/foo/src/vs/base/parts/tree/browser
/users/foo/src/vs/base/parts/tree/browser/collapsed.svg
/users/foo/src/vs/base/parts/tree/browser/expanded-dark.svg
/users/foo/src/vs/base/parts/tree/browser/loading-hc.svg
/users/foo/src/vs/base/parts/tree/browser/collapsed-hc.svg
/users/foo/src/vs/base/parts/tree/browser/CollapseAll.svg
/users/foo/src/vs/base/parts/tree/browser/collapsed-dark.svg
/users/foo/src/vs/base/parts/tree/browser/CollapseAll_inverse.svg
/users/foo/src/vs/base/parts/tree/browser/treeDnd.ts
/users/foo/src/vs/base/parts/tree/browser/treeModel.ts
/users/foo/src/vs/base/parts/tree/browser/treeViewModel.ts
/users/foo/src/vs/base/parts/tree/browser/expanded.svg
/users/foo/src/vs/base/parts/tree/browser/treeImpl.ts
/users/foo/src/vs/base/parts/tree/browser/loading.svg
/users/foo/src/vs/base/parts/tree/browser/treeDefaults.ts
/users/foo/src/vs/base/parts/tree/browser/expanded-hc.svg
/users/foo/src/vs/base/parts/tree/browser/treeView.ts
/users/foo/src/vs/base/parts/tree/browser/tree.ts
/users/foo/src/vs/base/parts/tree/browser/tree.css
/users/foo/src/vs/base/parts/tree/browser/loading-dark.svg
/users/foo/src/vs/base/parts/quickopen
/users/foo/src/vs/base/parts/quickopen/test
/users/foo/src/vs/base/parts/quickopen/test/browser
/users/foo/src/vs/base/parts/quickopen/test/browser/quickopen.test.ts
/users/foo/src/vs/base/parts/quickopen/test/common
/users/foo/src/vs/base/parts/quickopen/test/common/quickOpenScorer.test.ts
/users/foo/src/vs/base/parts/quickopen/browser
/users/foo/src/vs/base/parts/quickopen/browser/quickopen.css
/users/foo/src/vs/base/parts/quickopen/browser/quickOpenModel.ts
/users/foo/src/vs/base/parts/quickopen/browser/quickOpenViewer.ts
/users/foo/src/vs/base/parts/quickopen/browser/quickOpenWidget.ts
/users/foo/src/vs/base/parts/quickopen/common
/users/foo/src/vs/base/parts/quickopen/common/quickOpenScorer.ts
/users/foo/src/vs/base/parts/quickopen/common/quickOpen.ts
/users/foo/src/vs/base/parts/ipc
/users/foo/src/vs/base/parts/ipc/test
/users/foo/src/vs/base/parts/ipc/test/node
/users/foo/src/vs/base/parts/ipc/test/node/testApp.ts
/users/foo/src/vs/base/parts/ipc/test/node/ipc.perf.ts
/users/foo/src/vs/base/parts/ipc/test/node/ipc.net.test.ts
/users/foo/src/vs/base/parts/ipc/test/node/ipc.test.ts
/users/foo/src/vs/base/parts/ipc/test/node/testService.ts
/users/foo/src/vs/base/parts/ipc/common
/users/foo/src/vs/base/parts/ipc/node/ipc.electron.ts
/users/foo/src/vs/base/parts/ipc/node/ipc.ts
/users/foo/src/vs/base/parts/ipc/electron-browser
/users/foo/src/vs/base/parts/ipc/electron-browser/ipc.electron-browser.ts
/users/foo/src/vs/base/parts/ipc/node
/users/foo/src/vs/base/parts/ipc/node/ipc.cp.ts
/users/foo/src/vs/base/parts/ipc/node/ipc.net.ts
/users/foo/src/vs/base/parts/ipc/electron-main
/users/foo/src/vs/base/parts/ipc/electron-main/ipc.electron-main.ts
/users/foo/src/vs/base/node
/users/foo/src/vs/base/node/pfs.ts
/users/foo/src/vs/base/node/ps.ts
/users/foo/src/vs/base/node/stats.ts
/users/foo/src/vs/base/node/crypto.ts
/users/foo/src/vs/base/node/zip.ts
/users/foo/src/vs/base/node/processes.ts
/users/foo/src/vs/base/node/stream.ts
/users/foo/src/vs/base/node/flow.ts
/users/foo/src/vs/base/node/terminateProcess.sh
/users/foo/src/vs/base/node/request.ts
/users/foo/src/vs/base/node/ports.ts
/users/foo/src/vs/base/node/extfs.ts
/users/foo/src/vs/base/node/stdFork.ts
/users/foo/src/vs/base/node/proxy.ts
/users/foo/src/vs/base/node/paths.ts
/users/foo/src/vs/base/node/encoding.ts
/users/foo/src/vs/base/node/config.ts
/users/foo/src/vs/base/node/id.ts
/users/foo/src/vs/base/node/stdForkStart.js
/users/foo/src/vs/base/node/console.ts
/users/foo/src/vs/base/node/decoder.ts
/users/foo/src/vs/base/worker
/users/foo/src/vs/base/worker/defaultWorkerFactory.ts
/users/foo/src/vs/base/worker/workerMain.ts
/users/foo/src/vs/editor
/users/foo/src/vs/editor/editor.api.ts
/users/foo/src/vs/editor/editor.worker.ts
/users/foo/src/vs/editor/test
/users/foo/src/vs/editor/test/browser
/users/foo/src/vs/editor/test/browser/editorTestServices.ts
/users/foo/src/vs/editor/test/browser/core
/users/foo/src/vs/editor/test/browser/core/editorState.test.ts
/users/foo/src/vs/editor/test/browser/controller
/users/foo/src/vs/editor/test/browser/controller/imeTester.ts
/users/foo/src/vs/editor/test/browser/controller/imeTester.html
/users/foo/src/vs/editor/test/browser/controller/inputRecorder.html
/users/foo/src/vs/editor/test/browser/controller/cursor.test.ts
/users/foo/src/vs/editor/test/browser/controller/cursorMoveCommand.test.ts
/users/foo/src/vs/editor/test/browser/controller/textAreaState.test.ts
/users/foo/src/vs/editor/test/browser/testCodeEditor.ts
/users/foo/src/vs/editor/test/browser/testCommand.ts
/users/foo/src/vs/editor/test/browser/view
/users/foo/src/vs/editor/test/browser/view/minimapFontCreator.html
/users/foo/src/vs/editor/test/browser/view/viewLayer.test.ts
/users/foo/src/vs/editor/test/browser/view/minimapFontCreator.ts
/users/foo/src/vs/editor/test/browser/commands
/users/foo/src/vs/editor/test/browser/commands/sideEditing.test.ts
/users/foo/src/vs/editor/test/browser/commands/trimTrailingWhitespaceCommand.test.ts
/users/foo/src/vs/editor/test/browser/commands/shiftCommand.test.ts
/users/foo/src/vs/editor/test/browser/services
/users/foo/src/vs/editor/test/browser/services/decorationRenderOptions.test.ts
/users/foo/src/vs/editor/test/common
/users/foo/src/vs/editor/test/common/mocks
/users/foo/src/vs/editor/test/common/mocks/mockMode.ts
/users/foo/src/vs/editor/test/common/mocks/testConfiguration.ts
/users/foo/src/vs/editor/test/common/viewModel
/users/foo/src/vs/editor/test/common/viewModel/testViewModel.ts
/users/foo/src/vs/editor/test/common/viewModel/viewModelImpl.test.ts
/users/foo/src/vs/editor/test/common/viewModel/prefixSumComputer.test.ts
/users/foo/src/vs/editor/test/common/viewModel/viewModelDecorations.test.ts
/users/foo/src/vs/editor/test/common/viewModel/characterHardWrappingLineMapper.test.ts
/users/foo/src/vs/editor/test/common/viewModel/splitLinesCollection.test.ts
/users/foo/src/vs/editor/test/common/modesTestUtils.ts
/users/foo/src/vs/editor/test/common/core
/users/foo/src/vs/editor/test/common/core/range.test.ts
/users/foo/src/vs/editor/test/common/core/viewLineToken.ts
/users/foo/src/vs/editor/test/common/core/characterClassifier.test.ts
/users/foo/src/vs/editor/test/common/core/lineTokens.test.ts
/users/foo/src/vs/editor/test/common/modes
/users/foo/src/vs/editor/test/common/modes/supports
/users/foo/src/vs/editor/test/common/modes/supports/richEditBrackets.test.ts
/users/foo/src/vs/editor/test/common/modes/supports/tokenization.test.ts
/users/foo/src/vs/editor/test/common/modes/supports/characterPair.test.ts
/users/foo/src/vs/editor/test/common/modes/supports/onEnter.test.ts
/users/foo/src/vs/editor/test/common/modes/supports/electricCharacter.test.ts
/users/foo/src/vs/editor/test/common/modes/languageSelector.test.ts
/users/foo/src/vs/editor/test/common/modes/textToHtmlTokenizer.test.ts
/users/foo/src/vs/editor/test/common/modes/linkComputer.test.ts
/users/foo/src/vs/editor/test/common/modes/languageConfiguration.test.ts
/users/foo/src/vs/editor/test/common/viewLayout
/users/foo/src/vs/editor/test/common/viewLayout/viewLineRenderer.test.ts
/users/foo/src/vs/editor/test/common/viewLayout/whitespaceComputer.test.ts
/users/foo/src/vs/editor/test/common/viewLayout/editorLayoutProvider.test.ts
/users/foo/src/vs/editor/test/common/viewLayout/linesLayout.test.ts
/users/foo/src/vs/editor/test/common/viewLayout/lineDecorations.test.ts
/users/foo/src/vs/editor/test/common/standalone
/users/foo/src/vs/editor/test/common/standalone/standaloneBase.test.ts
/users/foo/src/vs/editor/test/common/config
/users/foo/src/vs/editor/test/common/config/commonEditorConfig.test.ts
/users/foo/src/vs/editor/test/common/diff
/users/foo/src/vs/editor/test/common/diff/diffComputer.test.ts
/users/foo/src/vs/editor/test/common/controller
/users/foo/src/vs/editor/test/common/controller/cursorMoveHelper.test.ts
/users/foo/src/vs/editor/test/common/model
/users/foo/src/vs/editor/test/common/model/benchmark
/users/foo/src/vs/editor/test/common/model/benchmark/benchmarkUtils.ts
/users/foo/src/vs/editor/test/common/model/benchmark/searchNReplace.benchmark.ts
/users/foo/src/vs/editor/test/common/model/benchmark/modelbuilder.benchmark.ts
/users/foo/src/vs/editor/test/common/model/benchmark/entry.ts
/users/foo/src/vs/editor/test/common/model/benchmark/bootstrap.js
/users/foo/src/vs/editor/test/common/model/benchmark/operations.benchmark.ts
/users/foo/src/vs/editor/test/common/model/editableTextModel.test.ts
/users/foo/src/vs/editor/test/common/model/model.test.ts
/users/foo/src/vs/editor/test/common/model/textModelSearch.test.ts
/users/foo/src/vs/editor/test/common/model/linesTextBuffer
/users/foo/src/vs/editor/test/common/model/linesTextBuffer/linesTextBufferBuilder.test.ts
/users/foo/src/vs/editor/test/common/model/linesTextBuffer/linesTextBuffer.test.ts
/users/foo/src/vs/editor/test/common/model/linesTextBuffer/textBufferAutoTestUtils.ts
/users/foo/src/vs/editor/test/common/model/textModel.test.ts
/users/foo/src/vs/editor/test/common/model/editableTextModelAuto.test.ts
/users/foo/src/vs/editor/test/common/model/intervalTree.test.ts
/users/foo/src/vs/editor/test/common/model/model.line.test.ts
/users/foo/src/vs/editor/test/common/model/editableTextModelTestUtils.ts
/users/foo/src/vs/editor/test/common/model/pieceTreeTextBuffer
/users/foo/src/vs/editor/test/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.test.ts
/users/foo/src/vs/editor/test/common/model/modelDecorations.test.ts
/users/foo/src/vs/editor/test/common/model/modelEditOperation.test.ts
/users/foo/src/vs/editor/test/common/model/textModelWithTokens.test.ts
/users/foo/src/vs/editor/test/common/model/model.modes.test.ts
/users/foo/src/vs/editor/test/common/view
/users/foo/src/vs/editor/test/common/view/minimapCharRendererFactory.ts
/users/foo/src/vs/editor/test/common/view/minimapCharRenderer.test.ts
/users/foo/src/vs/editor/test/common/view/overviewZoneManager.test.ts
/users/foo/src/vs/editor/test/common/commentMode.ts
/users/foo/src/vs/editor/test/common/editorTestUtils.ts
/users/foo/src/vs/editor/test/common/services
/users/foo/src/vs/editor/test/common/services/languagesRegistry.test.ts
/users/foo/src/vs/editor/test/common/services/modelService.test.ts
/users/foo/src/vs/editor/test/common/services/editorSimpleWorker.test.ts
/users/foo/src/vs/editor/standalone
/users/foo/src/vs/editor/standalone/test
/users/foo/src/vs/editor/standalone/test/browser
/users/foo/src/vs/editor/standalone/test/browser/simpleServices.test.ts
/users/foo/src/vs/editor/standalone/test/browser/standaloneLanguages.test.ts
/users/foo/src/vs/editor/standalone/browser
/users/foo/src/vs/editor/standalone/browser/iPadShowKeyboard
/users/foo/src/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.ts
/users/foo/src/vs/editor/standalone/browser/iPadShowKeyboard/keyboard.svg
/users/foo/src/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.css
/users/foo/src/vs/editor/standalone/browser/iPadShowKeyboard/keyboard-inverse.svg
/users/foo/src/vs/editor/standalone/browser/simpleServices.ts
/users/foo/src/vs/editor/standalone/browser/toggleHighContrast
/users/foo/src/vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast.ts
/users/foo/src/vs/editor/standalone/browser/standaloneCodeEditor.ts
/users/foo/src/vs/editor/standalone/browser/accessibilityHelp
/users/foo/src/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.ts
/users/foo/src/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.css
/users/foo/src/vs/editor/standalone/browser/quickOpen
/users/foo/src/vs/editor/standalone/browser/quickOpen/quickOutline.ts
/users/foo/src/vs/editor/standalone/browser/quickOpen/editorQuickOpen.ts
/users/foo/src/vs/editor/standalone/browser/quickOpen/gotoLine.ts
/users/foo/src/vs/editor/standalone/browser/quickOpen/symbol-sprite.svg
/users/foo/src/vs/editor/standalone/browser/quickOpen/quickOutline.css
/users/foo/src/vs/editor/standalone/browser/quickOpen/quickOpenEditorWidget.ts
/users/foo/src/vs/editor/standalone/browser/quickOpen/quickCommand.ts
/users/foo/src/vs/editor/standalone/browser/quickOpen/gotoLine.css
/users/foo/src/vs/editor/standalone/browser/standaloneThemeServiceImpl.ts
/users/foo/src/vs/editor/standalone/browser/standaloneLanguages.ts
/users/foo/src/vs/editor/standalone/browser/inspectTokens
/users/foo/src/vs/editor/standalone/browser/inspectTokens/inspectTokens.ts
/users/foo/src/vs/editor/standalone/browser/inspectTokens/inspectTokens.css
/users/foo/src/vs/editor/standalone/browser/standalone-tokens.css
/users/foo/src/vs/editor/standalone/browser/standaloneEditor.ts
/users/foo/src/vs/editor/standalone/browser/colorizer.ts
/users/foo/src/vs/editor/standalone/browser/standaloneServices.ts
/users/foo/src/vs/editor/standalone/browser/referenceSearch
/users/foo/src/vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch.ts
/users/foo/src/vs/editor/standalone/common
/users/foo/src/vs/editor/standalone/common/standaloneThemeService.ts
/users/foo/src/vs/editor/standalone/common/monarch
/users/foo/src/vs/editor/standalone/common/monarch/monarchCommon.ts
/users/foo/src/vs/editor/standalone/common/monarch/monarchTypes.ts
/users/foo/src/vs/editor/standalone/common/monarch/monarchCompile.ts
/users/foo/src/vs/editor/standalone/common/monarch/monarchLexer.ts
/users/foo/src/vs/editor/standalone/common/themes.ts
/users/foo/src/vs/editor/contrib
/users/foo/src/vs/editor/contrib/suggest
/users/foo/src/vs/editor/contrib/suggest/test
/users/foo/src/vs/editor/contrib/suggest/test/completionModel.test.ts
/users/foo/src/vs/editor/contrib/suggest/test/suggest.test.ts
/users/foo/src/vs/editor/contrib/suggest/test/suggestMemory.test.ts
/users/foo/src/vs/editor/contrib/suggest/test/suggestModel.test.ts
/users/foo/src/vs/editor/contrib/suggest/suggestController.ts
/users/foo/src/vs/editor/contrib/suggest/suggestModel.ts
/users/foo/src/vs/editor/contrib/suggest/completionModel.ts
/users/foo/src/vs/editor/contrib/suggest/suggest.ts
/users/foo/src/vs/editor/contrib/suggest/suggestWidget.ts
/users/foo/src/vs/editor/contrib/suggest/suggestMemory.ts
/users/foo/src/vs/editor/contrib/suggest/media
/users/foo/src/vs/editor/contrib/suggest/media/Field_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Misc_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Constant_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Folder_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Ruler_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/IntelliSenseKeyword_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Class_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/close-dark.svg
/users/foo/src/vs/editor/contrib/suggest/media/ColorPalette_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/LocalVariable_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Property_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/EnumItem_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Template_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/Enumerator_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Operator_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Interface_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Snippet_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/LocalVariable_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/String_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Folder_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Structure_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/Snippet_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/suggest.css
/users/foo/src/vs/editor/contrib/suggest/media/Event_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/info.svg
/users/foo/src/vs/editor/contrib/suggest/media/Enumerator_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/close.svg
/users/foo/src/vs/editor/contrib/suggest/media/String_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/ColorPalette_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Method_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/ImportFile_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Namespace_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Field_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Structure_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Event_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Template_16x_vscode_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/IntelliSenseKeyword_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/EnumItem_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Interface_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Namespace_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Property_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Misc_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Operator_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/Constant_16x_inverse.svg
/users/foo/src/vs/editor/contrib/suggest/media/Ruler_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/ImportFile_16x_vscode.svg
/users/foo/src/vs/editor/contrib/suggest/media/Method_inverse_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Document_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Class_16x.svg
/users/foo/src/vs/editor/contrib/suggest/media/Document_inverse_16x.svg
/users/foo/src/vs/editor/contrib/folding
/users/foo/src/vs/editor/contrib/folding/arrow-collapse-dark.svg
/users/foo/src/vs/editor/contrib/folding/folding.css
/users/foo/src/vs/editor/contrib/folding/syntaxRangeProvider.ts
/users/foo/src/vs/editor/contrib/folding/test
/users/foo/src/vs/editor/contrib/folding/test/foldingRanges.test.ts
/users/foo/src/vs/editor/contrib/folding/test/indentFold.test.ts
/users/foo/src/vs/editor/contrib/folding/test/indentRangeProvider.test.ts
/users/foo/src/vs/editor/contrib/folding/test/foldingModel.test.ts
/users/foo/src/vs/editor/contrib/folding/test/syntaxFold.test.ts
/users/foo/src/vs/editor/contrib/folding/test/hiddenRangeModel.test.ts
/users/foo/src/vs/editor/contrib/folding/foldingRanges.ts
/users/foo/src/vs/editor/contrib/folding/hiddenRangeModel.ts
/users/foo/src/vs/editor/contrib/folding/folding.ts
/users/foo/src/vs/editor/contrib/folding/arrow-expand-dark.svg
/users/foo/src/vs/editor/contrib/folding/arrow-collapse.svg
/users/foo/src/vs/editor/contrib/folding/indentRangeProvider.ts
/users/foo/src/vs/editor/contrib/folding/arrow-expand.svg
/users/foo/src/vs/editor/contrib/folding/foldingModel.ts
/users/foo/src/vs/editor/contrib/folding/foldingDecorations.ts
/users/foo/src/vs/editor/contrib/smartSelect
/users/foo/src/vs/editor/contrib/smartSelect/test
/users/foo/src/vs/editor/contrib/smartSelect/test/tokenSelectionSupport.test.ts
/users/foo/src/vs/editor/contrib/smartSelect/tokenSelectionSupport.ts
/users/foo/src/vs/editor/contrib/smartSelect/tokenTree.ts
/users/foo/src/vs/editor/contrib/smartSelect/smartSelect.ts
/users/foo/src/vs/editor/contrib/hover
/users/foo/src/vs/editor/contrib/hover/hover.ts
/users/foo/src/vs/editor/contrib/hover/getHover.ts
/users/foo/src/vs/editor/contrib/hover/hoverOperation.ts
/users/foo/src/vs/editor/contrib/hover/hover.css
/users/foo/src/vs/editor/contrib/hover/modesContentHover.ts
/users/foo/src/vs/editor/contrib/hover/hoverWidgets.ts
/users/foo/src/vs/editor/contrib/hover/modesGlyphHover.ts
/users/foo/src/vs/editor/contrib/cursorUndo
/users/foo/src/vs/editor/contrib/cursorUndo/cursorUndo.ts
/users/foo/src/vs/editor/contrib/gotoError
/users/foo/src/vs/editor/contrib/gotoError/gotoErrorWidget.css
/users/foo/src/vs/editor/contrib/gotoError/gotoError.ts
/users/foo/src/vs/editor/contrib/gotoError/gotoErrorWidget.ts
/users/foo/src/vs/editor/contrib/toggleTabFocusMode
/users/foo/src/vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.ts
/users/foo/src/vs/editor/contrib/multicursor
/users/foo/src/vs/editor/contrib/multicursor/test
/users/foo/src/vs/editor/contrib/multicursor/test/multicursor.test.ts
/users/foo/src/vs/editor/contrib/multicursor/multicursor.ts
/users/foo/src/vs/editor/contrib/quickOpen
/users/foo/src/vs/editor/contrib/quickOpen/quickOpen.ts
/users/foo/src/vs/editor/contrib/linesOperations
/users/foo/src/vs/editor/contrib/linesOperations/test
/users/foo/src/vs/editor/contrib/linesOperations/test/copyLinesCommand.test.ts
/users/foo/src/vs/editor/contrib/linesOperations/test/linesOperations.test.ts
/users/foo/src/vs/editor/contrib/linesOperations/test/sortLinesCommand.test.ts
/users/foo/src/vs/editor/contrib/linesOperations/test/moveLinesCommand.test.ts
/users/foo/src/vs/editor/contrib/linesOperations/test/deleteLinesCommand.test.ts
/users/foo/src/vs/editor/contrib/linesOperations/deleteLinesCommand.ts
/users/foo/src/vs/editor/contrib/linesOperations/linesOperations.ts
/users/foo/src/vs/editor/contrib/linesOperations/copyLinesCommand.ts
/users/foo/src/vs/editor/contrib/linesOperations/moveLinesCommand.ts
/users/foo/src/vs/editor/contrib/linesOperations/sortLinesCommand.ts
/users/foo/src/vs/editor/contrib/comment
/users/foo/src/vs/editor/contrib/comment/test
/users/foo/src/vs/editor/contrib/comment/test/blockCommentCommand.test.ts
/users/foo/src/vs/editor/contrib/comment/test/lineCommentCommand.test.ts
/users/foo/src/vs/editor/contrib/comment/lineCommentCommand.ts
/users/foo/src/vs/editor/contrib/comment/blockCommentCommand.ts
/users/foo/src/vs/editor/contrib/comment/comment.ts
/users/foo/src/vs/editor/contrib/markdown
/users/foo/src/vs/editor/contrib/markdown/markdownRenderer.ts
/users/foo/src/vs/editor/contrib/dnd
/users/foo/src/vs/editor/contrib/dnd/dragAndDropCommand.ts
/users/foo/src/vs/editor/contrib/dnd/dnd.css
/users/foo/src/vs/editor/contrib/dnd/dnd.ts
/users/foo/src/vs/editor/contrib/caretOperations
/users/foo/src/vs/editor/contrib/caretOperations/test
/users/foo/src/vs/editor/contrib/caretOperations/test/moveCarretCommand.test.ts
/users/foo/src/vs/editor/contrib/caretOperations/caretOperations.ts
/users/foo/src/vs/editor/contrib/caretOperations/transpose.ts
/users/foo/src/vs/editor/contrib/caretOperations/moveCaretCommand.ts
/users/foo/src/vs/editor/contrib/goToDefinition
/users/foo/src/vs/editor/contrib/goToDefinition/goToDefinitionMouse.css
/users/foo/src/vs/editor/contrib/goToDefinition/goToDefinitionMouse.ts
/users/foo/src/vs/editor/contrib/goToDefinition/goToDefinition.ts
/users/foo/src/vs/editor/contrib/goToDefinition/goToDefinitionCommands.ts
/users/foo/src/vs/editor/contrib/goToDefinition/clickLinkGesture.ts
/users/foo/src/vs/editor/contrib/snippet
/users/foo/src/vs/editor/contrib/snippet/test
/users/foo/src/vs/editor/contrib/snippet/test/snippetController2.test.ts
/users/foo/src/vs/editor/contrib/snippet/test/snippetSession.test.ts
/users/foo/src/vs/editor/contrib/snippet/test/snippetParser.test.ts
/users/foo/src/vs/editor/contrib/snippet/test/snippetVariables.test.ts
/users/foo/src/vs/editor/contrib/snippet/test/snippetController2.old.test.ts
/users/foo/src/vs/editor/contrib/snippet/snippetSession.css
/users/foo/src/vs/editor/contrib/snippet/snippetParser.ts
/users/foo/src/vs/editor/contrib/snippet/snippetController2.ts
/users/foo/src/vs/editor/contrib/snippet/snippetVariables.ts
/users/foo/src/vs/editor/contrib/snippet/snippet.md
/users/foo/src/vs/editor/contrib/snippet/snippetSession.ts
/users/foo/src/vs/editor/contrib/contextmenu
/users/foo/src/vs/editor/contrib/contextmenu/contextmenu.ts
/users/foo/src/vs/editor/contrib/message
/users/foo/src/vs/editor/contrib/message/messageController.css
/users/foo/src/vs/editor/contrib/message/messageController.ts
/users/foo/src/vs/editor/contrib/fontZoom
/users/foo/src/vs/editor/contrib/fontZoom/fontZoom.ts
/users/foo/src/vs/editor/contrib/indentation
/users/foo/src/vs/editor/contrib/indentation/test
/users/foo/src/vs/editor/contrib/indentation/test/indentation.test.ts
/users/foo/src/vs/editor/contrib/indentation/indentation.ts
/users/foo/src/vs/editor/contrib/indentation/indentUtils.ts
/users/foo/src/vs/editor/contrib/codeAction
/users/foo/src/vs/editor/contrib/codeAction/codeActionWidget.ts
/users/foo/src/vs/editor/contrib/codeAction/test
/users/foo/src/vs/editor/contrib/codeAction/test/codeAction.test.ts
/users/foo/src/vs/editor/contrib/codeAction/test/codeActionModel.test.ts
/users/foo/src/vs/editor/contrib/codeAction/lightBulbWidget.css
/users/foo/src/vs/editor/contrib/codeAction/codeActionCommands.ts
/users/foo/src/vs/editor/contrib/codeAction/codeActionContributions.ts
/users/foo/src/vs/editor/contrib/codeAction/lightbulb-dark.svg
/users/foo/src/vs/editor/contrib/codeAction/lightbulb.svg
/users/foo/src/vs/editor/contrib/codeAction/codeActionModel.ts
/users/foo/src/vs/editor/contrib/codeAction/codeAction.ts
/users/foo/src/vs/editor/contrib/codeAction/codeActionTrigger.ts
/users/foo/src/vs/editor/contrib/codeAction/lightBulbWidget.ts
/users/foo/src/vs/editor/contrib/links
/users/foo/src/vs/editor/contrib/links/links.css
/users/foo/src/vs/editor/contrib/links/links.ts
/users/foo/src/vs/editor/contrib/links/getLinks.ts
/users/foo/src/vs/editor/contrib/colorPicker
/users/foo/src/vs/editor/contrib/colorPicker/colorPickerModel.ts
/users/foo/src/vs/editor/contrib/colorPicker/images
/users/foo/src/vs/editor/contrib/colorPicker/images/opacity-background.png
/users/foo/src/vs/editor/contrib/colorPicker/colorPicker.css
/users/foo/src/vs/editor/contrib/colorPicker/colorDetector.ts
/users/foo/src/vs/editor/contrib/colorPicker/colorPickerWidget.ts
/users/foo/src/vs/editor/contrib/colorPicker/color.ts
/users/foo/src/vs/editor/contrib/bracketMatching
/users/foo/src/vs/editor/contrib/bracketMatching/bracketMatching.css
/users/foo/src/vs/editor/contrib/bracketMatching/test
/users/foo/src/vs/editor/contrib/bracketMatching/test/bracketMatching.test.ts
/users/foo/src/vs/editor/contrib/bracketMatching/bracketMatching.ts
/users/foo/src/vs/editor/contrib/format
/users/foo/src/vs/editor/contrib/format/formattingEdit.ts
/users/foo/src/vs/editor/contrib/format/format.ts
/users/foo/src/vs/editor/contrib/format/formatActions.ts
/users/foo/src/vs/editor/contrib/zoneWidget
/users/foo/src/vs/editor/contrib/zoneWidget/zoneWidget.css
/users/foo/src/vs/editor/contrib/zoneWidget/zoneWidget.ts
/users/foo/src/vs/editor/contrib/codelens
/users/foo/src/vs/editor/contrib/codelens/codelensWidget.css
/users/foo/src/vs/editor/contrib/codelens/codelens.ts
/users/foo/src/vs/editor/contrib/codelens/codelensWidget.ts
/users/foo/src/vs/editor/contrib/codelens/codelensController.ts
/users/foo/src/vs/editor/contrib/rename
/users/foo/src/vs/editor/contrib/rename/renameInputField.ts
/users/foo/src/vs/editor/contrib/rename/renameInputField.css
/users/foo/src/vs/editor/contrib/rename/rename.ts
/users/foo/src/vs/editor/contrib/inPlaceReplace
/users/foo/src/vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand.ts
/users/foo/src/vs/editor/contrib/inPlaceReplace/inPlaceReplace.ts
/users/foo/src/vs/editor/contrib/parameterHints
/users/foo/src/vs/editor/contrib/parameterHints/arrow-down.svg
/users/foo/src/vs/editor/contrib/parameterHints/parameterHintsWidget.ts
/users/foo/src/vs/editor/contrib/parameterHints/test
/users/foo/src/vs/editor/contrib/parameterHints/test/parameterHintsModel.test.ts
/users/foo/src/vs/editor/contrib/parameterHints/parameterHints.css
/users/foo/src/vs/editor/contrib/parameterHints/arrow-up-dark.svg
/users/foo/src/vs/editor/contrib/parameterHints/arrow-down-dark.svg
/users/foo/src/vs/editor/contrib/parameterHints/provideSignatureHelp.ts
/users/foo/src/vs/editor/contrib/parameterHints/arrow-up.svg
/users/foo/src/vs/editor/contrib/parameterHints/parameterHints.ts
/users/foo/src/vs/editor/contrib/find
/users/foo/src/vs/editor/contrib/find/findWidget.ts
/users/foo/src/vs/editor/contrib/find/findOptionsWidget.ts
/users/foo/src/vs/editor/contrib/find/simpleFindWidget.css
/users/foo/src/vs/editor/contrib/find/simpleFindWidget.ts
/users/foo/src/vs/editor/contrib/find/test
/users/foo/src/vs/editor/contrib/find/test/findController.test.ts
/users/foo/src/vs/editor/contrib/find/test/replacePattern.test.ts
/users/foo/src/vs/editor/contrib/find/test/findModel.test.ts
/users/foo/src/vs/editor/contrib/find/test/find.test.ts
/users/foo/src/vs/editor/contrib/find/replacePattern.ts
/users/foo/src/vs/editor/contrib/find/findModel.ts
/users/foo/src/vs/editor/contrib/find/images
/users/foo/src/vs/editor/contrib/find/images/expando-expanded.svg
/users/foo/src/vs/editor/contrib/find/images/replace-all.svg
/users/foo/src/vs/editor/contrib/find/images/expando-collapsed.svg
/users/foo/src/vs/editor/contrib/find/images/next-inverse.svg
/users/foo/src/vs/editor/contrib/find/images/close-dark.svg
/users/foo/src/vs/editor/contrib/find/images/cancelSelectionFind-inverse.svg
/users/foo/src/vs/editor/contrib/find/images/replace-inverse.svg
/users/foo/src/vs/editor/contrib/find/images/close.svg
/users/foo/src/vs/editor/contrib/find/images/previous.svg
/users/foo/src/vs/editor/contrib/find/images/replace-all-inverse.svg
/users/foo/src/vs/editor/contrib/find/images/next.svg
/users/foo/src/vs/editor/contrib/find/images/previous-inverse.svg
/users/foo/src/vs/editor/contrib/find/images/expando-expanded-dark.svg
/users/foo/src/vs/editor/contrib/find/images/cancelSelectionFind.svg
/users/foo/src/vs/editor/contrib/find/images/replace.svg
/users/foo/src/vs/editor/contrib/find/images/expando-collapsed-dark.svg
/users/foo/src/vs/editor/contrib/find/findState.ts
/users/foo/src/vs/editor/contrib/find/findController.ts
/users/foo/src/vs/editor/contrib/find/findWidget.css
/users/foo/src/vs/editor/contrib/find/replaceAllCommand.ts
/users/foo/src/vs/editor/contrib/find/findDecorations.ts
/users/foo/src/vs/editor/contrib/wordOperations
/users/foo/src/vs/editor/contrib/wordOperations/wordOperations.ts
/users/foo/src/vs/editor/contrib/wordOperations/test
/users/foo/src/vs/editor/contrib/wordOperations/test/wordOperations.test.ts
/users/foo/src/vs/editor/contrib/referenceSearch
/users/foo/src/vs/editor/contrib/referenceSearch/referencesController.ts
/users/foo/src/vs/editor/contrib/referenceSearch/test
/users/foo/src/vs/editor/contrib/referenceSearch/test/referencesModel.test.ts
/users/foo/src/vs/editor/contrib/referenceSearch/referencesWidget.ts
/users/foo/src/vs/editor/contrib/referenceSearch/peekViewWidget.ts
/users/foo/src/vs/editor/contrib/referenceSearch/referencesModel.ts
/users/foo/src/vs/editor/contrib/referenceSearch/referenceSearch.ts
/users/foo/src/vs/editor/contrib/referenceSearch/media
/users/foo/src/vs/editor/contrib/referenceSearch/media/referencesWidget.css
/users/foo/src/vs/editor/contrib/referenceSearch/media/close-inverse.svg
/users/foo/src/vs/editor/contrib/referenceSearch/media/close.svg
/users/foo/src/vs/editor/contrib/referenceSearch/media/peekViewWidget.css
/users/foo/src/vs/editor/contrib/wordHighlighter
/users/foo/src/vs/editor/contrib/wordHighlighter/wordHighlighter.ts
/users/foo/src/vs/editor/contrib/clipboard
/users/foo/src/vs/editor/contrib/clipboard/clipboard.ts
/users/foo/src/vs/editor/contrib/clipboard/clipboard.css
/users/foo/src/vs/editor/browser
/users/foo/src/vs/editor/browser/viewParts
/users/foo/src/vs/editor/browser/viewParts/lineNumbers
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/flipped-cursor-mac-2x.svg
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/flipped-cursor.svg
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/flipped-cursor-mac.svg
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/lineNumbers.ts
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css
/users/foo/src/vs/editor/browser/viewParts/lineNumbers/flipped-cursor-2x.svg
/users/foo/src/vs/editor/browser/viewParts/selections
/users/foo/src/vs/editor/browser/viewParts/selections/selections.ts
/users/foo/src/vs/editor/browser/viewParts/selections/selections.css
/users/foo/src/vs/editor/browser/viewParts/linesDecorations
/users/foo/src/vs/editor/browser/viewParts/linesDecorations/linesDecorations.ts
/users/foo/src/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css
/users/foo/src/vs/editor/browser/viewParts/editorScrollbar
/users/foo/src/vs/editor/browser/viewParts/editorScrollbar/editorScrollbar.ts
/users/foo/src/vs/editor/browser/viewParts/viewCursors
/users/foo/src/vs/editor/browser/viewParts/viewCursors/viewCursors.css
/users/foo/src/vs/editor/browser/viewParts/viewCursors/viewCursor.ts
/users/foo/src/vs/editor/browser/viewParts/viewCursors/viewCursors.ts
/users/foo/src/vs/editor/browser/viewParts/currentLineHighlight
/users/foo/src/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.ts
/users/foo/src/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css
/users/foo/src/vs/editor/browser/viewParts/minimap
/users/foo/src/vs/editor/browser/viewParts/minimap/minimap.ts
/users/foo/src/vs/editor/browser/viewParts/minimap/minimap.css
/users/foo/src/vs/editor/browser/viewParts/decorations
/users/foo/src/vs/editor/browser/viewParts/decorations/decorations.css
/users/foo/src/vs/editor/browser/viewParts/decorations/decorations.ts
/users/foo/src/vs/editor/browser/viewParts/scrollDecoration
/users/foo/src/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css
/users/foo/src/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.ts
/users/foo/src/vs/editor/browser/viewParts/viewZones
/users/foo/src/vs/editor/browser/viewParts/viewZones/viewZones.ts
/users/foo/src/vs/editor/browser/viewParts/overlayWidgets
/users/foo/src/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.ts
/users/foo/src/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css
/users/foo/src/vs/editor/browser/viewParts/indentGuides
/users/foo/src/vs/editor/browser/viewParts/indentGuides/indentGuides.css
/users/foo/src/vs/editor/browser/viewParts/indentGuides/indentGuides.ts
/users/foo/src/vs/editor/browser/viewParts/currentLineMarginHighlight
/users/foo/src/vs/editor/browser/viewParts/currentLineMarginHighlight/currentLineMarginHighlight.ts
/users/foo/src/vs/editor/browser/viewParts/currentLineMarginHighlight/currentLineMarginHighlight.css
/users/foo/src/vs/editor/browser/viewParts/contentWidgets
/users/foo/src/vs/editor/browser/viewParts/contentWidgets/contentWidgets.ts
/users/foo/src/vs/editor/browser/viewParts/margin
/users/foo/src/vs/editor/browser/viewParts/margin/margin.ts
/users/foo/src/vs/editor/browser/viewParts/overviewRuler
/users/foo/src/vs/editor/browser/viewParts/overviewRuler/overviewRuler.ts
/users/foo/src/vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler.ts
/users/foo/src/vs/editor/browser/viewParts/glyphMargin
/users/foo/src/vs/editor/browser/viewParts/glyphMargin/glyphMargin.ts
/users/foo/src/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css
/users/foo/src/vs/editor/browser/viewParts/lines
/users/foo/src/vs/editor/browser/viewParts/lines/viewLine.ts
/users/foo/src/vs/editor/browser/viewParts/lines/viewLines.ts
/users/foo/src/vs/editor/browser/viewParts/lines/rangeUtil.ts
/users/foo/src/vs/editor/browser/viewParts/lines/viewLines.css
/users/foo/src/vs/editor/browser/viewParts/marginDecorations
/users/foo/src/vs/editor/browser/viewParts/marginDecorations/marginDecorations.ts
/users/foo/src/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css
/users/foo/src/vs/editor/browser/viewParts/rulers
/users/foo/src/vs/editor/browser/viewParts/rulers/rulers.ts
/users/foo/src/vs/editor/browser/viewParts/rulers/rulers.css
/users/foo/src/vs/editor/browser/core
/users/foo/src/vs/editor/browser/core/editorState.ts
/users/foo/src/vs/editor/browser/widget
/users/foo/src/vs/editor/browser/widget/embeddedCodeEditorWidget.ts
/users/foo/src/vs/editor/browser/widget/codeEditorWidget.ts
/users/foo/src/vs/editor/browser/widget/diffReview.ts
/users/foo/src/vs/editor/browser/widget/diffEditorWidget.ts
/users/foo/src/vs/editor/browser/widget/diffNavigator.ts
/users/foo/src/vs/editor/browser/widget/media
/users/foo/src/vs/editor/browser/widget/media/editor.css
/users/foo/src/vs/editor/browser/widget/media/diffEditor.css
/users/foo/src/vs/editor/browser/widget/media/close-inverse.svg
/users/foo/src/vs/editor/browser/widget/media/tokens.css
/users/foo/src/vs/editor/browser/widget/media/diffReview.css
/users/foo/src/vs/editor/browser/widget/media/close.svg
/users/foo/src/vs/editor/browser/widget/media/deletion-inverse.svg
/users/foo/src/vs/editor/browser/widget/media/deletion.svg
/users/foo/src/vs/editor/browser/widget/media/addition-inverse.svg
/users/foo/src/vs/editor/browser/widget/media/addition.svg
/users/foo/src/vs/editor/browser/widget/media/diagonal-fill.png
/users/foo/src/vs/editor/browser/config
/users/foo/src/vs/editor/browser/config/elementSizeObserver.ts
/users/foo/src/vs/editor/browser/config/charWidthReader.ts
/users/foo/src/vs/editor/browser/config/configuration.ts
/users/foo/src/vs/editor/browser/editorExtensions.ts
/users/foo/src/vs/editor/browser/controller
/users/foo/src/vs/editor/browser/controller/textAreaState.ts
/users/foo/src/vs/editor/browser/controller/mouseHandler.ts
/users/foo/src/vs/editor/browser/controller/textAreaInput.ts
/users/foo/src/vs/editor/browser/controller/coreCommands.ts
/users/foo/src/vs/editor/browser/controller/mouseTarget.ts
/users/foo/src/vs/editor/browser/controller/pointerHandler.ts
/users/foo/src/vs/editor/browser/controller/textAreaHandler.ts
/users/foo/src/vs/editor/browser/controller/textAreaHandler.css
/users/foo/src/vs/editor/browser/editorDom.ts
/users/foo/src/vs/editor/browser/view
/users/foo/src/vs/editor/browser/view/viewOverlays.ts
/users/foo/src/vs/editor/browser/view/viewController.ts
/users/foo/src/vs/editor/browser/view/viewLayer.ts
/users/foo/src/vs/editor/browser/view/dynamicViewOverlay.ts
/users/foo/src/vs/editor/browser/view/viewOutgoingEvents.ts
/users/foo/src/vs/editor/browser/view/viewPart.ts
/users/foo/src/vs/editor/browser/view/viewImpl.ts
/users/foo/src/vs/editor/browser/editorBrowser.ts
/users/foo/src/vs/editor/browser/services
/users/foo/src/vs/editor/browser/services/abstractCodeEditorService.ts
/users/foo/src/vs/editor/browser/services/bulkEditService.ts
/users/foo/src/vs/editor/browser/services/codeEditorServiceImpl.ts
/users/foo/src/vs/editor/browser/services/codeEditorService.ts
/users/foo/src/vs/editor/common
/users/foo/src/vs/editor/common/viewModel
/users/foo/src/vs/editor/common/viewModel/characterHardWrappingLineMapper.ts
/users/foo/src/vs/editor/common/viewModel/viewModelDecorations.ts
/users/foo/src/vs/editor/common/viewModel/splitLinesCollection.ts
/users/foo/src/vs/editor/common/viewModel/prefixSumComputer.ts
/users/foo/src/vs/editor/common/viewModel/viewModel.ts
/users/foo/src/vs/editor/common/viewModel/viewModelImpl.ts
/users/foo/src/vs/editor/common/viewModel/viewEventHandler.ts
/users/foo/src/vs/editor/common/core
/users/foo/src/vs/editor/common/core/stringBuilder.ts
/users/foo/src/vs/editor/common/core/uint.ts
/users/foo/src/vs/editor/common/core/lineTokens.ts
/users/foo/src/vs/editor/common/core/selection.ts
/users/foo/src/vs/editor/common/core/position.ts
/users/foo/src/vs/editor/common/core/editOperation.ts
/users/foo/src/vs/editor/common/core/range.ts
/users/foo/src/vs/editor/common/core/characterClassifier.ts
/users/foo/src/vs/editor/common/core/token.ts
/users/foo/src/vs/editor/common/core/rgba.ts
/users/foo/src/vs/editor/common/modes
/users/foo/src/vs/editor/common/modes/languageConfiguration.ts
/users/foo/src/vs/editor/common/modes/supports
/users/foo/src/vs/editor/common/modes/supports/electricCharacter.ts
/users/foo/src/vs/editor/common/modes/supports/inplaceReplaceSupport.ts
/users/foo/src/vs/editor/common/modes/supports/characterPair.ts
/users/foo/src/vs/editor/common/modes/supports/onEnter.ts
/users/foo/src/vs/editor/common/modes/supports/indentRules.ts
/users/foo/src/vs/editor/common/modes/supports/richEditBrackets.ts
/users/foo/src/vs/editor/common/modes/supports/tokenization.ts
/users/foo/src/vs/editor/common/modes/textToHtmlTokenizer.ts
/users/foo/src/vs/editor/common/modes/languageConfigurationRegistry.ts
/users/foo/src/vs/editor/common/modes/modesRegistry.ts
/users/foo/src/vs/editor/common/modes/supports.ts
/users/foo/src/vs/editor/common/modes/tokenizationRegistry.ts
/users/foo/src/vs/editor/common/modes/nullMode.ts
/users/foo/src/vs/editor/common/modes/linkComputer.ts
/users/foo/src/vs/editor/common/modes/abstractMode.ts
/users/foo/src/vs/editor/common/modes/languageSelector.ts
/users/foo/src/vs/editor/common/modes/languageFeatureRegistry.ts
/users/foo/src/vs/editor/common/viewLayout
/users/foo/src/vs/editor/common/viewLayout/viewLineRenderer.ts
/users/foo/src/vs/editor/common/viewLayout/lineDecorations.ts
/users/foo/src/vs/editor/common/viewLayout/viewLayout.ts
/users/foo/src/vs/editor/common/viewLayout/linesLayout.ts
/users/foo/src/vs/editor/common/viewLayout/whitespaceComputer.ts
/users/foo/src/vs/editor/common/viewLayout/viewLinesViewportData.ts
/users/foo/src/vs/editor/common/standalone
/users/foo/src/vs/editor/common/standalone/standaloneBase.ts
/users/foo/src/vs/editor/common/config
/users/foo/src/vs/editor/common/config/editorOptions.ts
/users/foo/src/vs/editor/common/config/editorZoom.ts
/users/foo/src/vs/editor/common/config/commonEditorConfig.ts
/users/foo/src/vs/editor/common/config/fontInfo.ts
/users/foo/src/vs/editor/common/editorAction.ts
/users/foo/src/vs/editor/common/editorContextKeys.ts
/users/foo/src/vs/editor/common/diff
/users/foo/src/vs/editor/common/diff/diffComputer.ts
/users/foo/src/vs/editor/common/model.ts
/users/foo/src/vs/editor/common/controller
/users/foo/src/vs/editor/common/controller/cursorCollection.ts
/users/foo/src/vs/editor/common/controller/cursor.ts
/users/foo/src/vs/editor/common/controller/cursorDeleteOperations.ts
/users/foo/src/vs/editor/common/controller/cursorWordOperations.ts
/users/foo/src/vs/editor/common/controller/cursorMoveOperations.ts
/users/foo/src/vs/editor/common/controller/cursorCommon.ts
/users/foo/src/vs/editor/common/controller/wordCharacterClassifier.ts
/users/foo/src/vs/editor/common/controller/oneCursor.ts
/users/foo/src/vs/editor/common/controller/cursorTypeOperations.ts
/users/foo/src/vs/editor/common/controller/cursorColumnSelection.ts
/users/foo/src/vs/editor/common/controller/cursorMoveCommands.ts
/users/foo/src/vs/editor/common/controller/cursorEvents.ts
/users/foo/src/vs/editor/common/modes.ts
/users/foo/src/vs/editor/common/model
/users/foo/src/vs/editor/common/model/wordHelper.ts
/users/foo/src/vs/editor/common/model/textModelSearch.ts
/users/foo/src/vs/editor/common/model/editStack.ts
/users/foo/src/vs/editor/common/model/indentationGuesser.ts
/users/foo/src/vs/editor/common/model/textModelTokens.ts
/users/foo/src/vs/editor/common/model/textModelEvents.ts
/users/foo/src/vs/editor/common/model/textModel.ts
/users/foo/src/vs/editor/common/model/pieceTreeTextBuffer
/users/foo/src/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder.ts
/users/foo/src/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.ts
/users/foo/src/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.ts
/users/foo/src/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.ts
/users/foo/src/vs/editor/common/model/mirrorTextModel.ts
/users/foo/src/vs/editor/common/model/intervalTree.ts
/users/foo/src/vs/editor/common/view
/users/foo/src/vs/editor/common/view/renderingContext.ts
/users/foo/src/vs/editor/common/view/minimapCharRenderer.ts
/users/foo/src/vs/editor/common/view/viewContext.ts
/users/foo/src/vs/editor/common/view/viewEventDispatcher.ts
/users/foo/src/vs/editor/common/view/viewEvents.ts
/users/foo/src/vs/editor/common/view/runtimeMinimapCharRenderer.ts
/users/foo/src/vs/editor/common/view/overviewZoneManager.ts
/users/foo/src/vs/editor/common/view/editorColorRegistry.ts
/users/foo/src/vs/editor/common/editorCommon.ts
/users/foo/src/vs/editor/common/commands
/users/foo/src/vs/editor/common/commands/surroundSelectionCommand.ts
/users/foo/src/vs/editor/common/commands/replaceCommand.ts
/users/foo/src/vs/editor/common/commands/trimTrailingWhitespaceCommand.ts
/users/foo/src/vs/editor/common/commands/shiftCommand.ts
/users/foo/src/vs/editor/common/services
/users/foo/src/vs/editor/common/services/modelService.ts
/users/foo/src/vs/editor/common/services/resourceConfigurationImpl.ts
/users/foo/src/vs/editor/common/services/modeServiceImpl.ts
/users/foo/src/vs/editor/common/services/modelServiceImpl.ts
/users/foo/src/vs/editor/common/services/editorWorkerServiceImpl.ts
/users/foo/src/vs/editor/common/services/resourceConfiguration.ts
/users/foo/src/vs/editor/common/services/languagesRegistry.ts
/users/foo/src/vs/editor/common/services/webWorker.ts
/users/foo/src/vs/editor/common/services/resolverService.ts
/users/foo/src/vs/editor/common/services/modeService.ts
/users/foo/src/vs/editor/common/services/editorSimpleWorker.ts
/users/foo/src/vs/editor/common/services/editorWorkerService.ts
/users/foo/src/vs/editor/editor.all.ts
/users/foo/src/vs/editor/editor.main.ts
/users/foo/src/vs/loader.js
/users/foo/src/.DS_Store
/users/foo/src/bootstrap.js
/users/foo/src/typings
/users/foo/src/typings/vscode-nsfw.d.ts
/users/foo/src/typings/fast-plist.d.ts
/users/foo/src/typings/sudo-prompt.d.ts
/users/foo/src/typings/native-watchdog.d.ts
/users/foo/src/typings/graceful-fs.d.ts
/users/foo/src/typings/mocha.d.ts
/users/foo/src/typings/node.processEnv-ext.d.ts
/users/foo/src/typings/keytar.d.ts
/users/foo/src/typings/v8-inspect-profiler.d.ts
/users/foo/src/typings/ansi-regex.d.ts
/users/foo/src/typings/yauzl.d.ts
/users/foo/src/typings/jschardet.d.ts
/users/foo/src/typings/lib.ie11_safe_es6.d.ts
/users/foo/src/typings/semver.d.ts
/users/foo/src/typings/node-pty.d.ts
/users/foo/src/typings/native-keymap.d.ts
/users/foo/src/typings/electron.d.ts
/users/foo/src/typings/https-proxy-agent.d.ts
/users/foo/src/typings/lib.array-ext.d.ts
/users/foo/src/typings/spdlog.d.ts
/users/foo/src/typings/applicationInsights.d.ts
/users/foo/src/typings/iconv-lite.d.ts
/users/foo/src/typings/require.d.ts
/users/foo/src/typings/http-proxy-agent.d.ts
/users/foo/src/typings/OSSREADME.json
/users/foo/src/typings/chokidar.d.ts
/users/foo/src/typings/vscode-textmate.d.ts
/users/foo/src/typings/sinon.d.ts
/users/foo/src/typings/es6-promise.d.ts
/users/foo/src/typings/getmac.d.ts
/users/foo/src/typings/thenable.d.ts
/users/foo/src/typings/windows-mutex.d.ts
/users/foo/src/typings/native-is-elevated.d.ts
/users/foo/src/typings/vscode-ripgrep.d.ts
/users/foo/src/typings/minimist.d.ts
/users/foo/src/typings/node.d.ts
/users/foo/src/typings/gc-signals.d.ts
/users/foo/src/typings/windows-foreground-love.d.ts
/users/foo/src/typings/windows-process-tree.d.ts
/users/foo/src/typings/vscode-xterm.d.ts
/users/foo/src/.eslintrc
/users/foo/src/paths.js
/users/foo/src/main.js
/users/foo/src/tsconfig.monaco.json
/users/foo/src/bootstrap-amd.js
/users/foo/src/tsconfig.json
/users/foo/src/cli.js
/users/foo/src/buildfile.js
