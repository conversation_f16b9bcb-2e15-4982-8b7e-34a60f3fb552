"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class RAGManager {
    constructor(context) {
        this.documents = [];
        this.isInitialized = false;
        this.context = context;
        this.vectorDbPath = path.join(context.extensionPath, 'data', 'vector-db.json');
        // Ensure data directory exists
        const dataDir = path.dirname(this.vectorDbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        this.loadDocuments();
    }
    async initialize() {
        try {
            // Load existing documents
            this.loadDocuments();
            // Initialize default documentation if empty
            if (this.documents.length === 0) {
                await this.loadDefaultDocumentation();
            }
            this.isInitialized = true;
            console.log(`RAG system initialized with ${this.documents.length} documents`);
        }
        catch (error) {
            console.error('RAG initialization failed:', error);
            throw error;
        }
    }
    async addDocument(title, content, source, language) {
        const document = {
            id: this.generateId(),
            title,
            content,
            source,
            language,
            timestamp: Date.now()
        };
        // TODO: Generate embeddings for the document
        // For now, we'll use a simple text-based approach
        document.embedding = await this.generateMockEmbedding(content);
        this.documents.push(document);
        this.saveDocuments();
        return document.id;
    }
    async searchDocuments(query, limit = 5) {
        if (!this.isInitialized || this.documents.length === 0) {
            return [];
        }
        try {
            // TODO: Implement proper vector similarity search
            // For now, use simple text matching
            const results = this.simpleTextSearch(query, limit);
            return results;
        }
        catch (error) {
            console.error('Document search failed:', error);
            return [];
        }
    }
    async getRelevantContext(query, maxTokens = 2000) {
        const searchResults = await this.searchDocuments(query, 3);
        if (searchResults.length === 0) {
            return '';
        }
        let context = 'Relevant documentation:\n\n';
        let tokenCount = 0;
        for (const result of searchResults) {
            const chunk = result.relevantChunk;
            const chunkTokens = chunk.split(' ').length;
            if (tokenCount + chunkTokens > maxTokens) {
                break;
            }
            context += `## ${result.document.title}\n${chunk}\n\n`;
            tokenCount += chunkTokens;
        }
        return context;
    }
    simpleTextSearch(query, limit) {
        const queryLower = query.toLowerCase();
        const results = [];
        for (const doc of this.documents) {
            const contentLower = doc.content.toLowerCase();
            const titleLower = doc.title.toLowerCase();
            let score = 0;
            // Title match gets higher score
            if (titleLower.includes(queryLower)) {
                score += 10;
            }
            // Content match
            const matches = (contentLower.match(new RegExp(queryLower, 'g')) || []).length;
            score += matches;
            if (score > 0) {
                // Find relevant chunk
                const relevantChunk = this.extractRelevantChunk(doc.content, query);
                results.push({
                    document: doc,
                    score,
                    relevantChunk
                });
            }
        }
        return results
            .sort((a, b) => b.score - a.score)
            .slice(0, limit);
    }
    extractRelevantChunk(content, query, chunkSize = 500) {
        const queryLower = query.toLowerCase();
        const contentLower = content.toLowerCase();
        const index = contentLower.indexOf(queryLower);
        if (index === -1) {
            return content.substring(0, chunkSize);
        }
        const start = Math.max(0, index - chunkSize / 2);
        const end = Math.min(content.length, start + chunkSize);
        return content.substring(start, end);
    }
    async generateMockEmbedding(text) {
        // Mock embedding generation - in real implementation, use proper embedding model
        const embedding = new Array(384).fill(0);
        for (let i = 0; i < text.length && i < 384; i++) {
            embedding[i] = text.charCodeAt(i) / 255;
        }
        return embedding;
    }
    async loadDefaultDocumentation() {
        // Add some basic programming documentation
        const defaultDocs = [
            {
                title: 'JavaScript Basics',
                content: `JavaScript is a programming language that enables interactive web pages. 
                
Variables can be declared with var, let, or const:
- var: function-scoped
- let: block-scoped
- const: block-scoped, immutable

Functions can be declared in multiple ways:
function myFunction() { }
const myFunction = () => { }
const myFunction = function() { }`,
                source: 'built-in',
                language: 'javascript'
            },
            {
                title: 'Python Basics',
                content: `Python is a high-level programming language known for its simplicity.

Variables are dynamically typed:
name = "John"
age = 30
is_student = True

Functions are defined with def:
def greet(name):
    return f"Hello, {name}!"

Classes are defined with class:
class Person:
    def __init__(self, name):
        self.name = name`,
                source: 'built-in',
                language: 'python'
            }
        ];
        for (const doc of defaultDocs) {
            await this.addDocument(doc.title, doc.content, doc.source, doc.language);
        }
    }
    loadDocuments() {
        try {
            if (fs.existsSync(this.vectorDbPath)) {
                const data = fs.readFileSync(this.vectorDbPath, 'utf8');
                this.documents = JSON.parse(data);
            }
        }
        catch (error) {
            console.error('Error loading documents:', error);
            this.documents = [];
        }
    }
    saveDocuments() {
        try {
            fs.writeFileSync(this.vectorDbPath, JSON.stringify(this.documents, null, 2));
        }
        catch (error) {
            console.error('Error saving documents:', error);
        }
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    getDocumentCount() {
        return this.documents.length;
    }
    dispose() {
        this.saveDocuments();
    }
}
exports.RAGManager = RAGManager;
//# sourceMappingURL=RAGManager.js.map