/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/** -- icons */

.monaco-workbench {
	.codicon-testing-error-icon {
		color: var(--vscode-testing-iconErrored);
	}

	.codicon-testing-failed-icon {
		color: var(--vscode-testing-iconFailed);
	}

	.codicon-testing-passed-icon {
		color: var(--vscode-testing-iconPassed);
	}

	.codicon-testing-queued-icon {
		color: var(--vscode-testing-iconQueued);
	}

	.codicon-testing-skipped-icon {
		color: var(--vscode-testing-iconSkipped);
	}

	.codicon-testing-unset-icon {
		color: var(--vscode-testing-iconUnset);
	}
}

/** -- explorer */
.test-explorer {
	display: flex;
	flex-direction: column;
}

.test-explorer > .test-explorer-tree {
	flex-grow: 1;
	height: 0px;
	position: relative;
}

.testing-stdtree-container {
	display: flex;
	align-items: center;

	.label {
		flex-grow: 1;
		width: 0;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;

		.codicon {
			vertical-align: middle;
			font-size: 1em;
			transform: scale(1.25);
			margin: 0 0.125em;
		}

		.monaco-list.horizontal-scrolling & {
			width: auto;
			overflow: visible;
		}
	}

	.monaco-action-bar {
		display: none;
		flex-shrink: 0;
		margin-right: 0.8em;
	}

	&:hover, &.focused {
		.monaco-action-bar {
			display: initial;
		}
	}
}

.test-output-peek-tree {
	color: var(--vscode-editor-foreground);
	border-left: 1px solid var(--vscode-panelSection-border);
}

.test-explorer .monaco-list-row .codicon-testing-hidden {
	display: none;
	flex-shrink: 0;
	margin-right: 0.8em;
}

.test-explorer .monaco-list-row .monaco-action-bar.testing-is-continuous-run {
	display: initial;
}

.test-explorer .monaco-list-row .monaco-action-bar .codicon-testing-continuous-is-on {
	color: var(--vscode-inputOption-activeForeground);
	border-color: var(--vscode-inputOption-activeBorder);
	background: var(--vscode-inputOption-activeBackground);
	border: 1px solid var(--vscode-inputOption-activeBorder);
	border-radius: 3px;
}

.test-explorer .monaco-list-row:not(.focused, :hover) .monaco-action-bar.testing-is-continuous-run .action-item {
	display: none;
}

.test-explorer .monaco-list-row .monaco-action-bar.testing-is-continuous-run .action-item:last-child {
	display: block !important;
}

.test-explorer .monaco-list-row .test-is-hidden .codicon-testing-hidden {
	display: block;
	margin-right: 9px;
}

.test-explorer .monaco-list-row:hover .codicon-testing-hidden,
.test-explorer .monaco-list-row.focused .codicon-testing-hidden {
	display: none;
}

.test-explorer .monaco-list-row .error {
	outline: 1px solid var(--vscode-inputValidation-errorBorder);
	background: var(--vscode-inputValidation-errorBackground);
	padding: 2px 4px;
	border-radius: 2px;
	margin: 3px 12px 3px 3px;
	line-height: 17px;
	font-size: 12px;
	height: 17px;
	overflow: hidden;
}

.test-explorer .monaco-list-row .error p {
	margin: 0;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.test-explorer .monaco-list-row .error a {
	color: var(--vscode-textLink-foreground);
}

.test-explorer .monaco-list-row .error a:hover {
	color: var(--vscode-textLink-activeForeground);
}

.test-explorer .computed-state,
.test-output-peek-tree .computed-state {
	margin-right: 0.25em;
}

.test-explorer .test-is-hidden {
	opacity: 0.8;
}

.test-explorer .result-summary-container {
	padding: 0 12px 8px;
	font-variant-numeric: tabular-nums;
	height: 27px;
	box-sizing: border-box;
}

.test-explorer .result-summary {
	display: flex;
	align-items: center;
	gap: 2px;
}

.test-explorer .result-summary > span {
	flex-grow: 1;
}

.monaco-workbench .test-explorer .monaco-action-bar .action-item > .action-label {
	padding: 1px 2px;
	margin-right: 2px;
}

.monaco-workbench .part > .title > .title-actions .action-label.codicon-testing-autorun::after {
	content: '';
	display: none;
	position: absolute;
	width: 0.4em;
	height: 0.4em;
	top: 50%;
	left: 50%;
	margin: 0.1em 0 0 0.05em;
	border-radius: 100%;
}

.monaco-workbench .part > .title > .title-actions .action-label.codicon-testing-autorun.checked::after {
	display: block;
}

.codicon-testing-loading-icon::before {
	/* Use steps to throttle FPS to reduce CPU usage */
	animation: codicon-spin 1.25s steps(30) infinite;
}

.testing-no-test-placeholder {
	display: none;
	padding: 0 20px;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	z-index: 1;
}

.testing-no-test-placeholder.visible {
	display: block;
}

/** -- peek */
.monaco-editor .zone-widget.test-output-peek .zone-widget-container.peekview-widget {
	border-top-width: 2px;
	border-bottom-width: 2px;
}

.monaco-editor .zone-widget.test-output-peek .type-decoration {
	background-color: var(--vscode-peekViewEditor-matchHighlightBackground);
	border: 2px solid var(--vscode-peekViewEditor-matchHighlightBorder);
	box-sizing: border-box;
}

.monaco-editor .zone-widget.test-output-peek .monaco-editor .monaco-editor-background,
.monaco-editor .zone-widget.test-output-peek .monaco-editor .inputarea.ime-input,
.monaco-editor .zone-widget.test-output-peek .test-output-peek-message-container {
	background-color: var(--vscode-peekViewEditor-background);
}

.monaco-editor .zone-widget.test-output-peek .monaco-editor .margin {
	background-color: var(--vscode-peekViewEditorGutter-background);
}

.test-output-peek-message-container {
	overflow: hidden;
}

.test-output-peek-message-container .floating-click-widget {
	position: absolute;
	right: 20px;
	bottom: 10px;
}

.test-output-peek-message-container,
.test-output-peek-tree {
	height: 100%;
}

.test-output-peek-message-container .preview-text {
	padding: 8px 12px 8px 20px;
	line-height: normal;
	white-space: normal;
}

.test-output-peek-message-container .preview-text p:first-child {
	margin-top: 0;
}

.test-output-peek-message-container .preview-text p:last-child {
	margin-bottom: 0;
}

.test-output-peek-message-container .preview-text a {
	cursor: pointer;
}

.testing-followup-action {
	line-height: 25px;
	overflow: hidden;
	pointer-events: none;
	display: flex;
	align-items: center;
	gap: 14px;

	&.animated {
		animation: fadeIn 150ms ease-out;
	}

	> a {
		display: flex;
		align-items: center;
		gap: 4px;
		cursor: pointer;
		pointer-events: auto;
		width: fit-content;
		flex-shrink: 0;

		&,
		.codicon {
			color: var(--vscode-textLink-foreground);
		}

		&:hover {
			color: var(--vscode-textLink-activeForeground);
		}

		&[aria-disabled="true"] {
			color: inherit;
			cursor: default;

			.codicon {
				color: inherit;
			}
		}
	}
}

.test-output-call-stack {
	height: 100%;
}

/** -- filter  */
.monaco-action-bar.testing-filter-action-bar {
	flex-shrink: 0;
	margin: 4px 12px;
	height: auto;
}

.testing-filter-action-item {
	display: flex !important;
	flex-grow: 1;
	max-width: 400px;
	align-items: center;
}

.testing-filter-action-item > .monaco-action-bar .testing-filter-button.checked {
	border-color: var(--vscode-inputOption-activeBorder);
	color: var(--vscode-inputOption-activeForeground);
	background-color: var(--vscode-inputOption-activeBackground);
}

.testing-filter-action-bar .testing-filter-action-item {
	max-width: none;
}

.testing-filter-action-item .testing-filter-wrapper {
	flex-grow: 1;
}

.testing-filter-action-item .testing-filter-wrapper input {
	padding-right: 30px !important;
}

.testing-filter-action-item .monaco-action-bar {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 3px;
	display: flex;
	align-items: center;
}

/** -- decorations  */

.monaco-editor .testing-run-glyph {
	cursor: pointer;
}

.testing-diff-title-widget {
	line-height: 19px;
	font-size: 12px;
	padding-right: 6px;

	overflow: hidden;
	display: inline-block;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.test-message-inline-content {
	font-family: var(--testMessageDecorationFontFamily);
	font-size: var(--testMessageDecorationFontSize);
}

.test-message-inline-content-clickable {
	cursor: pointer;
}

.test-label-description {
	opacity: .7;
	margin-left: 0.5em;
	font-size: .9em;
	white-space: pre;
}

.testing-diff-lens-widget {
	color: var(--vscode-editorCodeLens-foreground);
}

.test-message-inline-content {
	margin-left: 4em;

	+ .test-message-inline-content {
		margin-left: 0;

		&::before {
			display: none;
		}
	}
}

.test-message-inline-content-s1 {
	color: var(--vscode-testing-message-info-decorationForeground) !important;
}

.monaco-workbench .test-error-content-widget {
	z-index: 0;
}

.monaco-workbench .test-error-content-widget .inner {
	margin-left: 20px;
	color: var(--vscode-testing-message-error-badgeForeground) !important;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	padding-right: 3px;
	display: flex !important;
	align-items: center;
	font-size: 11px;
	gap: 3px;
	user-select: none;
	cursor: pointer;
	box-sizing: border-box;
	position: relative;
	border: 1px solid var(--vscode-testing-message-error-badgeBorder);
	border-left: none;

	&.is-current {
		display: none !important;
	}

	.codicon.codicon-testing-failed-icon {
		color: currentColor !important;
		font-size: 11px;
		margin: 1px 0 0 -4px;
		z-index: 1;
	}

	.content {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.arrow svg {
		position: absolute;
		height: calc(100% + 2px);
		top: -1px;
		width: 15px;
		left: -10px;
		z-index: -1;
		stroke-width: 0.71px; /* 1 / sqrt(2) */
		stroke: var(--vscode-testing-message-error-badgeBorder);
	}
}

.monaco-editor .testing-inline-message-severity-0 {
	background: var(--vscode-testing-message-error-lineBackground) !important;
}

.monaco-editor .testing-inline-message-severity-1 {
	background: var(--vscode-testing-message-info-lineBackground) !important;
}

/** -- coverage  */

.test-coverage-bars {
	display: flex;
	align-items: center;
	gap: 4px;
	font-size: 11px;
	margin-right: 12px;
}

.test-coverage-bars .bar {
	height: 8px;
	border: 1px solid currentColor;
	border-radius: 2px;
	position: relative;
	overflow: hidden;
}

.test-coverage-bars .bar::before {
	content: '';
	background: currentColor;
	width: var(--test-bar-width);
	height: 100%;
	position: absolute;
	opacity: 0.7;
}

.test-coverage-list-item .icon {
	margin-right: 0.2em;
}

.test-coverage-list-item.not-covered .name {
	opacity: 0.7;
}

.coverage-summary-widget {
	color: var(--vscode-editor-foreground);
	z-index: 1;
	background: var(--vscode-editor-background);
	left: 0;
	width: 100%;
	box-shadow: var(--vscode-editorStickyScroll-shadow) 0 3px 2px -2px;

	> div {
		display: flex;
		align-items: center;
		padding: 0 22px;
		height: 25px;
	}

	.btn {
		position: relative;
		margin: 0 4px;
		padding: 0 4px;

		&:first-child {
			margin-left: 0;
		}

		&:last-child {
			margin-right: 0;
		}
	}

	.stat,
	.action-label {
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		margin: 0 3px;
	}

	.action-label {
		display: flex;
		align-items: center;
		font-size: 13px;
		padding: 0 4px;

		.codicon {
			margin-right: 4px;
		}
	}

}

.test-coverage-tree-per-test-switcher {
	display: flex;
	background-color: var(--vscode-dropdown-background);
	color: var(--vscode-dropdown-foreground);
	border: 1px solid var(--vscode-dropdown-border);

	margin: 3px 0;
	cursor: pointer;
	margin-right: 22px;
	line-height: 20px;
	padding: 0 6px;
	width: fit-content;
	max-width: calc(100% - 44px);

	span {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	&::after {
		content: '';
		content: var(--vscode-icon-chevron-right-content);
		font-family: var(--vscode-icon-chevron-right-font-family);
		font-size: 18px;
		padding-left: 22px;
	}
}

/** -- coverage in the explorer  */

.explorer-item-with-test-coverage {
	display: flex;
}

.explorer-item-with-test-coverage .explorer-item {
	flex-grow: 1;
}

.explorer-item-with-test-coverage .monaco-icon-label::after {
	margin-right: 12px;
	/* slightly reduce because the bars handle the scrollbar margin */
}

/** -- coverage decorations  */

.coverage-deco-gutter {
	z-index: 0;
}

.coverage-deco-gutter::before {
	content: '';
	position: absolute;
	inset: 0;
	z-index: -1;
}

.coverage-deco-gutter.coverage-deco-hit::before {
	background: var(--vscode-testing-coveredGutterBackground);
	border-color: var(--vscode-testing-coveredGutterBackground);
}

.coverage-deco-gutter.coverage-deco-miss::before {
	background: var(--vscode-testing-uncoveredGutterBackground);
	border-color: var(--vscode-testing-uncoveredGutterBackground);
}

.hc-light .coverage-deco-gutter::before,
.hc-black .coverage-deco-gutter::before {
	border-width: 3px 0 3px 5px;
	border-style: solid;
	background: none;
}

.coverage-deco-gutter.coverage-deco-miss.coverage-deco-hit::before {
	background-image: linear-gradient(45deg,
			var(--vscode-testing-coveredGutterBackground) 25%,
			var(--vscode-testing-uncoveredGutterBackground) 25%,
			var(--vscode-testing-uncoveredGutterBackground) 50%,
			var(--vscode-testing-coveredGutterBackground) 50%,
			75%,
			var(--vscode-testing-uncoveredGutterBackground) 75%,
			var(--vscode-testing-uncoveredGutterBackground) 100%);
	background-size: 6px 6px;
	background-color: transparent;
}

.coverage-deco-inline {
	outline-offset: -1px;
}

.monaco-editor {
	.coverage-deco-inline.coverage-deco-hit {
		background: var(--vscode-testing-coveredBackground);
		outline: 1px solid var(--vscode-testing-coveredBorder);
	}

	.coverage-deco-inline.coverage-deco-miss {
		background: var(--vscode-testing-uncoveredBackground);
		outline: 1px solid var(--vscode-testing-uncoveredBorder);
	}

	.hc-light .coverage-deco-inline.coverage-deco-hit,
	.hc-black .coverage-deco-inline.coverage-deco-hit {
		outline-style: dashed;
	}

	.coverage-deco-branch-miss-indicator {
		height: 100%;
		width: 4ch;
		position: relative;
		display: inline-block;
		font: inherit !important;
	}

	.coverage-deco-branch-miss-indicator::before {
		position: absolute;
		top: 50%;
		left: 50%;
		text-align: center;
		transform: translate(-50%, -50%);
		padding: calc(var(--vscode-testing-coverage-lineHeight) / 10);
		border-radius: 2px;
		font: normal normal normal calc(var(--vscode-testing-coverage-lineHeight) / 2)/1 codicon;
		border: 1px solid;
	}

	.coverage-deco-inline-count {
		position: relative;
		background: var(--vscode-testing-coverCountBadgeBackground);
		color: var(--vscode-testing-coverCountBadgeForeground);
		font-size: 0.7em;
		margin: 0 0.7em 0 0.4em;
		padding: 0.2em 0 0.2em 0.2em;
		/* display: inline-block; */
		border-top-left-radius: 2px;
		border-bottom-left-radius: 2px;

		&::after {
			content: '';
			display: block;
			position: absolute;
			left: 100%;
			top: 0;
			bottom: 0;
			width: 0.5em;
			background-image:
				linear-gradient(to bottom left, transparent 50%, var(--vscode-testing-coverCountBadgeBackground) 0),
				linear-gradient(to bottom right, var(--vscode-testing-coverCountBadgeBackground) 50%, transparent 0);
			background-size: 100% 50%;
			background-repeat: no-repeat;
			background-position: top, bottom;
		}
	}
}
