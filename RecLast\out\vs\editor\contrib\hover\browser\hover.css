/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .hoverHighlight {
	background-color: var(--vscode-editor-hoverHighlightBackground);
}

.monaco-editor .monaco-resizable-hover {
	border: 1px solid var(--vscode-editorHoverWidget-border);
	border-radius: 3px;
	box-sizing: content-box;
}

.monaco-editor .monaco-resizable-hover > .monaco-hover {
	border: none;
	border-radius: none;
}

.monaco-editor .monaco-hover {
	border: 1px solid var(--vscode-editorHoverWidget-border);
	border-radius: 3px;
	color: var(--vscode-editorHoverWidget-foreground);
	background-color: var(--vscode-editorHoverWidget-background);
}

.monaco-editor .monaco-hover a {
	color: var(--vscode-textLink-foreground);
}

.monaco-editor .monaco-hover a:hover {
	color: var(--vscode-textLink-activeForeground);
}

.monaco-editor .monaco-hover .hover-row {
	display: flex;
}

.monaco-editor .monaco-hover .hover-row .hover-row-contents {
	min-width: 0;
	display: flex;
	flex-direction: column;
}

.monaco-editor .monaco-hover .hover-row .verbosity-actions {
	border-right: 1px solid var(--vscode-editorHoverWidget-border);
	width: 22px;
	overflow-y: clip;
}

.monaco-editor .monaco-hover .hover-row .verbosity-actions-inner {
	display: flex;
	flex-direction: column;
	padding-left: 5px;
	padding-right: 5px;
	justify-content: flex-end;
	position: relative;
}

.monaco-editor .monaco-hover .hover-row .verbosity-actions-inner .codicon {
	cursor: pointer;
	font-size: 11px;
}

.monaco-editor .monaco-hover .hover-row .verbosity-actions-inner .codicon.enabled {
	color: var(--vscode-textLink-foreground);
}

.monaco-editor .monaco-hover .hover-row .verbosity-actions-inner .codicon.disabled {
	opacity: 0.6;
}

.monaco-editor .monaco-hover .hover-row .actions {
	background-color: var(--vscode-editorHoverWidget-statusBarBackground);
}

.monaco-editor .monaco-hover code {
	background-color: var(--vscode-textCodeBlock-background);
}
