{"name": "cson-parser", "version": "4.0.9", "description": "Safe parsing of CSON files", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/cson-parser.js", "homepage": "https://github.com/groupon/cson-parser", "repository": {"type": "git", "url": "https://github.com/groupon/cson-parser"}, "bugs": {"url": "https://github.com/groupon/cson-parser/issues"}, "scripts": {"lint": "npm-run-all lint:*", "lint:js": "eslint .", "pretest": "npm-run-all pretest:*", "test": "npm-run-all test:*", "posttest": "npm-run-all posttest:*", "pretest:lint": "npm-run-all lint:*", "posttest:nlm": "nlm verify", "test:unit": "c8 mocha"}, "engines": {"node": ">=10.13"}, "nlm": {"license": {"files": ["lib"]}}, "c8": {"reporter": ["text", "json-summary"]}, "types": "lib/typedefs.d.ts", "dependencies": {"coffeescript": "1.12.7"}, "devDependencies": {"c8": "^7.6.0", "eslint": "^7.22.0", "eslint-config-groupon": "^10.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-mocha": "^8.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.3.1", "mocha": "^8.3.2", "nlm": "^5.4.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1"}, "author": {"name": "Groupon", "email": "<EMAIL>"}, "keywords": ["cson", "parser"], "files": ["*.js", "lib"], "publishConfig": {"registry": "https://registry.npmjs.org"}}