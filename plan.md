# 🧩 RecLast IDE – Proje Planı ve Teknik Dokümantasyon

---

## 📌 Özet

**RecLast IDE**, tamamen yereld<PERSON>alı<PERSON>, MIT lisanslı, **VS Code tabanlı** ve içine gömülü **Codestral Mamba 7B** modeliyle çalı<PERSON>an bir yapay zekâ kod editörüdür. Türkçe komutlarla çalışır, İngilizce kod üretir. <PERSON><PERSON><PERSON> gerektirmez, sadece indirip çalıştırmak yeterlidir. Tüm süreç offline yürür, isteğe bağlı bir web lookup modülü entegre edilebilir.

---

## ✨ Özellikler

- 🔌 **Model Dahili**: `models/mamba-7b/` dizininde gömülü çalışır, taşınabilir.
- 🧠 **Türkçe Komut – İngilizce Kod**: Komutlar Türkçe, çıktı kodları İngilizce.
- 🖥️ **Kurulumsuz**: “İndir ve çalıştır” modeli.
- 🌐 **Offline Çalışma**: Web bağlantısı gerekmez, sadece opsiyonel modül hariç.
- 🔄 **Modlar**: Chat, Manual, Agent olmak üzere üç farklı etkileşim modu.
- 📚 **RAG Destekli**: Laravel / Python gibi framework dökümanlarını belge ile anlama kabiliyeti.
- 🧠 **Bellek Yönetimi**: Snapshot, fonksiyon hafızası ve checkpoint sistemi ile zengin bellek altyapısı.
- 🌍 **Dil Seçimi**: Arayüzde TR / EN geçişi desteklenir.
- 🔎 **Web Lookup**: CURL/requests tabanlı bilgi alma opsiyonu mevcuttur.

---

## 🛠️ Kullanılan Teknolojiler

| Alan               | Teknoloji / Araç                             |
|--------------------|----------------------------------------------|
| IDE Tabanı         | [VS Code](https://github.com/microsoft/vscode) (MIT lisanslı) |
| AI Modeli          | Codestral Mamba 7B (`ggml` formatında)       |
| Çalıştırma Motoru  | [llama.cpp](https://github.com/ggerganov/llama.cpp), `llama-cpp-python` |
| RAG Sistemi        | [Haystack](https://haystack.deepset.ai/) veya [llama_index](https://gpt-index.readthedocs.io/) |
| Vektör Veritabanı  | FAISS, Chroma veya Weaviate (sqlite opsiyonu) |
| Bellek Sistemi     | JSON tabanlı snapshot + vektör hafıza        |
| UI Framework       | VS Code Webview tabanlı UI                   |
| Web Lookup         | CURL / Python `requests`                     |
| Paketleme          | `build.sh`, `start.sh` shell script'leri     |

---

## 📋 Teknik Notlar ve Görüşülenler

- ✅ Codestral Mamba 7B, HumanEval benchmark'ında en yüksek başarıya sahip 7B sınıfı yerel modeldir.
- ✅ Copilot Chat kodları açık kaynak, VS Code fork’uyla gömülü AI destekli editor kolayca oluşturulabilir.
- ✅ TR / EN arayüz geçişi desteklenerek geniş kullanıcı yelpazesine hitap edecek.
- ✅ RAG yapısı sayesinde Laravel, Python belgeleri gibi framework bilgisi vektörle desteklenecek.
- ✅ 3 mod yapısı ile hem geliştirici hem yeni başlayanlar için ideal deneyim sağlanacak:
  - **Chat**: Soru-cevap
  - **Manual**: Kod önerisi
  - **Agent**: Terminal ve dosya sistemine etkileşim
- ✅ Snapshot ve bellek sistemi ile yapılan işlemler kaydedilebilir ve geri çağrılabilir olacak.
- ✅ Web lookup modülü opsiyoneldir ve yalnızca kullanıcı onayıyla çalışır.

---

## ✅ Yapılacaklar Listesi

| No | Görev                         | Açıklama                                                                            |
|----|-------------------------------|-------------------------------------------------------------------------------------|
| 1  | VS Code Fork                  | Copilot Chat tabanlı yapının forklanması                                            |
| 2  | Mamba 7B Entegrasyonu         | `llama.cpp` veya `llama-cpp-python` ile model çalıştırma                           |
| 3  | RAG Sistemi                   | Laravel/Python belgelerini vektörel yapıya entegre etme                            |
| 4  | UI – Dil Seçimi               | Arayüzde TR / EN toggle seçeneği                                                    |
| 5  | Bellek ve Snapshot Sistemi    | Kod segmentleri bellekte tutulacak, geri çağrılabilir yapılar kurulacak             |
| 6  | Mod Sistemi                   | Chat / Manual / Agent modlarının UI ve backend entegrasyonu                         |
| 7  | Export / Snapshot Altyapısı   | JSON / ZIP formatlarında dışa veri aktarımı                                         |
| 8  | Web Lookup Modülü             | Opsiyonel web bilgi alma modülü geliştirilecek                                      |
| 9  | Logo ve İkon Tasarımı         | RecLast IDE için özel branding öğeleri oluşturulacak                               |
| 10 | Dokümantasyon + README        | Kullanım, kurulum ve örneklerle detaylı kullanım rehberi yazılacak                  |

---

## 🏗️ Mimari Yapı

- **Temel Yapı**: VS Code fork + gömülü LLM (Codestral Mamba 7B)
- **Model Entegrasyonu**: `llama-cpp-python` ile model çağrısı
- **Vektör Hafıza**: FAISS / sqlite üzerinden hızlı veri erişimi
- **Modlar**: Kullanıcı düzeyine göre yapılandırılabilir (Chat / Manual / Agent)
- **Bellek Yönetimi**: Snapshot (JSON) ve vektör kayıtlar
- **İnternet Desteği**: Sadece izin verilirse web lookup (opsiyonel)
- **Export Sistemi**: Proje yapısı dışa aktarılabilir, taşınabilir

---

## 📁 Klasör Yapısı

```plaintext
RecLast/
├── src/                      # Forklanmış VS Code kaynakları
│   ├── ui/
│   ├── agents/
│   └── ...
├── models/                   # AI modeli
│   └── mamba-7b/
│       ├── ggml-model.bin
│       └── config.json
├── data/                     # Vektörler ve snapshotlar
│   ├── vektör-db.sqlite
│   └── snapshots/
├── plugins/                  # Opsiyonel plugin altyapısı
│   └── reca-ide-plugin/
├── scripts/                  # Başlatma ve paketleme script'leri
│   ├── start.sh
│   └── build.sh
├── web-fetch/                # Web lookup modülü (opsiyonel)
└── README.md                 # Kullanım dokümantasyonu

🎨 Branding & UI
Proje Adı: RecLast IDE

Logo/İkon: VS Code stilinde AI vurgulu özel ikon tasarımı

Renk Paleti:

Ana Renk: #0066cc

Vurgu Renk: #00cc66

Arayüz: VS Code benzeri ancak RecLast kimliğiyle özelleştirilmiş UI

Dokümantasyon: Blog tarzı anlatım + teknik açıklama içeren README.md

🧭 Sonraki Adımlar & Geliştirmeler
✅ Copilot Chat Fork: Başlangıç olarak VS Code kodları forklanmalı.

⏳ Mamba 7B Test: llama-cpp-python ile modelin gömülü çalışabilirliği denenmeli.

🔄 RAG Prototipi: Laravel dökümanları vektörel yapıya entegre edilerek test edilmeli.

🧪 Dil Seçimi UI: TR/EN geçiş butonu eklenmeli.

🛠️ Manual Mod Geliştirme: Kod öneri yapısı kullanıcı kontrolüyle test edilmeli.

🤖 Agent Mod Tasarımı: Terminal, dosya sistemi ve işlem başlatma entegrasyonu.

🧠 Snapshot Bellek: Proje ve kod geçmişi için bellek sistemi aktif edilmeli.

🌐 Web Lookup: CURL/requests ile internetten bilgi alma modülü geliştirilmeli.

🎨 Logo & Belgeler: Branding dosyaları ve README.md tamamlanmalı.