<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="231px" height="108px" viewBox="-0.5 -0.5 231 108" content="&lt;mxfile&gt;&lt;diagram id=&quot;-Th-3Xml5MnJZz-ctsqk&quot; name=&quot;Page-1&quot;&gt;zVhNc5swEP01nkk7E48EBtvH2Pnooe10JocmRwXWoIlArpBjO7++wkgGAf4IceJcEu2yu0j7np6Qe+40Wd0JMo9/8RBYz0Hhqude9xzH9T31N3esC4ePUOGIBA0LFy4d9/QVtNOELWgImRUoOWeSzm1nwNMUAmn5iBB8aYfNOLPfOicRNBz3AWFN718ayrjwjjxU+n8AjWLzZmzWlxATrB1ZTEK+rLjcm547FZzLYpSspsDy3pm+FHm3O55uJyYglcckaCAyuTZrg1AtVZspT9W/SSwTpiyshrCi8qEyflRj1Pe0dZ0DjIyxNkYqxfqhalSycrNM21gmr7kWvbx8ihWHXtkd8ARUvgoQwIikLzZaRIMebeO2qX84Va9wkObnwKCj6em5yC6R8YUIQGdVu1sr5B0qJImIQDYKqUFlPaVrA147kH5R+IWwhV62jGnWiu5P8qS2owUrYTRK1ThQrQahHC8gJFWEv9IPEhqGeY2JgIy+kqdNvRykeT73zWq8Sc+7Vh6Wl5+Q4DkSfJGGU864KLk046m8JQlleUumqo9Uvc9Bv2HZirjmZz4dWLXtbT0Va/tYXNBZl6iPfAuMS6cwu5LFlMG1FD6bZfBeOJ0GnN8bWG7aC6GGcBlTCfdzEuRPl0p2bYR3bqZGa3e20PVrbDbsXpYaaELiivwN0O6eWi3a049RR51C/aFXkSp8pFCV2rTN6iJUhU5YiBY7Xjfmc+SsrkK+c5wKXQlB1pUwvdV3yybybH6Ma2dPfV4e2hevBsUMuu6hcask9lMuEqV3r3Dx7RzyuFcA84f6cwdjbRsB7TkuQuMxQiZOTxsf1lzKWM0VCRJSKItrd5PJo1MK8NCxFRi/T4HNfEa1qh8hyLjJpj78WxCWXXAZg6jS6jy8OhrkMzDwjSRqoeA+XuGBkdLTEuvSpitGH8CrwRc86BufrZ940ONmQxr9ONCBlp2QScGf4aDYvf2TqH6EoWan8LClVXUudWqV3/GjyL68Dbvd3oadr29f46to4Nagq7P36EveoUKnu+ThYWNzbA6fc5w3J73mGSp/6XveCeRfmeVvO0V4+QOZe/Mf&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <path d="M 70 30 L 163.63 30" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 168.88 30 L 161.88 33.5 L 163.63 30 L 161.88 26.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 21px; margin-left: 117px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    this
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="117" y="25" fill="light-dark(#000000, #ffffff)" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        this
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="10" y="20" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 40px; margin-left: 11px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    *
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="40" y="44" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        *
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 55 60 L 55 80 Q 55 90 65 90 L 190 90 Q 200 90 200 80 L 200 66.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 200 61.12 L 203.5 68.12 L 200 66.37 L 196.5 68.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 80px; margin-left: 138px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #009900; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#009900, #32b532); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    this.normalize()
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="138" y="84" fill="#009900" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        this.normalize()
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 101px; margin-left: 111px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #009900; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#009900, #32b532); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    .equals(other.normalize())
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="111" y="105" fill="#009900" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        .equals(other.normalize())
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="170" y="20" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 40px; margin-left: 171px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    *
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="200" y="44" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        *
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="0" y="0" width="170" height="10" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 70 50 L 163.63 50" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 168.88 50 L 161.88 53.5 L 163.63 50 L 161.88 46.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 42px; margin-left: 117px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    other
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="117" y="46" fill="light-dark(#000000, #ffffff)" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        other
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
