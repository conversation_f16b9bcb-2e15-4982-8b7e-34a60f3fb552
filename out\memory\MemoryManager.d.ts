import * as vscode from 'vscode';
export interface Snapshot {
    id: string;
    name: string;
    timestamp: number;
    workspaceState: any;
    chatHistory: ChatMessage[];
    codeContext: CodeContext[];
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    mode: 'chat' | 'manual' | 'agent';
}
export interface CodeContext {
    filePath: string;
    content: string;
    language: string;
    timestamp: number;
}
export declare class MemoryManager {
    private context;
    private snapshotsPath;
    private chatHistory;
    private codeContexts;
    constructor(context: vscode.ExtensionContext);
    addChatMessage(role: 'user' | 'assistant', content: string, mode: 'chat' | 'manual' | 'agent'): void;
    getChatHistory(mode?: 'chat' | 'manual' | 'agent'): ChatMessage[];
    clearChatHistory(mode?: 'chat' | 'manual' | 'agent'): void;
    addCodeContext(filePath: string, content: string, language: string): void;
    getCodeContexts(): CodeContext[];
    createSnapshot(name: string): Promise<string>;
    loadSnapshot(snapshotId: string): Promise<void>;
    listSnapshots(): {
        id: string;
        name: string;
        timestamp: number;
    }[];
    deleteSnapshot(snapshotId: string): Promise<void>;
    private loadChatHistory;
    private saveChatHistory;
    private generateId;
    dispose(): void;
}
//# sourceMappingURL=MemoryManager.d.ts.map