/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.scm-view {
	height: 100%;
	position: relative;
}

.scm-overflow-widgets-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 0;
	height: 0;
	overflow: visible;
	z-index: 5000;
}

.scm-view .monaco-tl-contents > div {
	padding-right: 12px;
	overflow: hidden;
}

.scm-view .count {
	display: flex;
	margin-left: 6px;
}

.scm-view .count.hidden {
	display: none;
}

.scm-view .scm-provider {
	display: flex;
	flex-direction: column;
	height: 100%;
	align-items: center;
	flex-flow: nowrap;
}

.scm-view.hide-provider-counts .scm-provider > .count,
.scm-view.auto-provider-counts .scm-provider > .count[data-count="0"] {
	display: none;
}

.scm-view .scm-provider > .label {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	min-width: 50px;
}

.scm-view .scm-provider > .label > .name {
	font-weight: bold;
}

.scm-view .scm-provider > .label > .description {
	opacity: 0.7;
	margin-left: 0.5em;
	font-size: 0.9em;
}

.scm-view .scm-provider > .actions {
	overflow: hidden;
	justify-content: flex-end;
}

/**
 * The following rules are very specific because of inline drop down menus
 * https://github.com/microsoft/vscode/issues/101410
 */
.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item {
	padding-left: 4px;
	display: flex;
	align-items: center;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item:nth-child(1) {
	min-width: 20px;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .action-label,
.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .monaco-dropdown > .dropdown-label > .action-label {
	display: flex;
	align-items: center;
	line-height: 16px;
	overflow: hidden;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .action-label > .codicon {
	font-size: 12px;
	justify-content: center;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item:last-of-type {
	padding-right: 0;
}

.scm-view .scm-provider > .body {
	flex-grow: 1;
}

.scm-view .scm-provider > .status > .monaco-action-bar > .actions-container {
	border-color: var(--vscode-sideBar-border);
}

.scm-view .monaco-list-row {
	line-height: 22px;
}

.scm-view .monaco-list-row .monaco-icon-label-container {
	height: 22px;
}

.scm-view .monaco-list-row .history,
.scm-view .monaco-list-row .history-item-group,
.scm-view .monaco-list-row .resource-group {
	display: flex;
	height: 100%;
	align-items: center;
}

.scm-view .monaco-list-row .history-item-group .monaco-icon-label,
.scm-view .monaco-list-row .history-item .monaco-icon-label {
	flex-grow: 1;
	align-items: center;
}

.scm-view .monaco-list-row .history-item {
	display: flex;
	align-items: center;
}

.scm-view .monaco-list-row .monaco-tl-twistie:not(.collapsed) + .monaco-tl-contents > .history-item > .graph-container > svg > path:last-of-type {
	stroke-width: 3px;
}

.scm-view .monaco-list-row .history-item > .graph-container {
	display: flex;
	flex-shrink: 0;
	height: 22px;
}

.scm-view .monaco-list-row .history-item > .graph-container.current > .graph > circle:last-child {
	fill: var(--vscode-sideBar-background);
}

.scm-view .monaco-list-row:hover .history-item > .graph-container.current > .graph > circle:last-child {
	fill: var(--vscode-list-hoverBackground);
}

.scm-view .monaco-list-row .history-item > .graph-container > .graph > circle {
	stroke: var(--vscode-sideBar-background);
}

.scm-view .monaco-list-row:hover .history-item > .graph-container > .graph > circle:first-of-type {
	stroke: transparent;
}

.scm-view .monaco-list-row .history-item > .label-container {
	display: flex;
	flex-shrink: 0;
	margin-left: 4px;
	gap: 4px;
}

.scm-view .monaco-list-row .history-item > .label-container > .label {
	display: flex;
	align-items: center;
	border-radius: 10px;
	line-height: 18px;
}

.scm-view .monaco-list-row .history-item > .label-container > .label > .count {
	font-size: 12px;
	margin-left: 0;
	padding-left: 4px;
}

.scm-view .monaco-list-row .history-item > .label-container > .label > .codicon {
	font-size: 14px;
	color: inherit !important;
	padding: 2px;
}

.scm-view .monaco-list-row .history-item > .label-container > .label > .codicon.codicon-git-branch {
	font-size: 12px;
	padding: 3px;
}

.scm-view .monaco-list-row .history-item > .label-container > .label > .description {
	font-size: 12px;
	padding-right: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100px;
}

.scm-view .monaco-list-row .history-item .monaco-icon-label .icon-container {
	display: flex;
	padding-right: 4px;
}

.scm-view .monaco-list-row .history-item .monaco-icon-label .avatar {
	width: 14px;
	height: 14px;
	border-radius: 14px;
}

.scm-view .monaco-list-row .history-item .monaco-icon-label.history-item-current .label-name {
	font-weight: 600;
}

.scm-view .monaco-list-row .history-item .monaco-icon-label.history-item-current .label-description {
	font-weight: 500;
}

.scm-view .monaco-list-row .history-item > .actions {
	margin-left: 4px;
}

.scm-view .monaco-list-row .history > .name,
.scm-view .monaco-list-row .resource-group > .name {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.scm-view .monaco-list-row .resource {
	display: flex;
	height: 100%;
}

.scm-view .monaco-list-row .resource.faded {
	opacity: 0.7;
}

.scm-view .monaco-list-row .resource > .name {
	flex: 1;
	overflow: hidden;
}

.scm-view .monaco-list-row .resource > .name > .monaco-icon-label::after,
.scm-view .monaco-list-row .history-item-change > .label-container > .monaco-icon-label::after {
	margin-right: 3px;
}

.scm-view .monaco-list-row .resource > .decoration-icon {
	width: 16px;
	height: 100%;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	margin-left: 5px;
}

.scm-view .monaco-list-row .resource > .decoration-icon.codicon {
	margin-right: 0;
	margin-top: 3px;
}

.scm-view .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions {
	flex-grow: 100;
}

.scm-view .monaco-list .monaco-list-row .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list .monaco-list-row .history-item > .actions,
.scm-view .monaco-list .monaco-list-row .history-item-change > .label-container > .monaco-icon-label > .actions {
	display: none;
	max-width: fit-content;
}

.scm-view .monaco-list .monaco-list-row:hover .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row.focused .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row:hover .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list .monaco-list-row.focused .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list:not(.selection-multiple) .monaco-list-row .resource:hover > .actions,
.scm-view .monaco-list .monaco-list-row:hover .history-item > .actions,
.scm-view .monaco-list .monaco-list-row.focused .history-item > .actions,
.scm-view .monaco-list .monaco-list-row:hover .history-item-change > .label-container > .monaco-icon-label > .actions,
.scm-view .monaco-list .monaco-list-row.focused .history-item-change > .label-container > .monaco-icon-label > .actions {
	display: block;
}

.scm-view .monaco-list .monaco-list-row.force-no-hover,
.scm-view .monaco-list .monaco-list-row:hover.force-no-hover,
.scm-view .monaco-list .monaco-list-row.focused.force-no-hover,
.scm-view .monaco-list .monaco-list-row.selected.force-no-hover {
	background: transparent !important;
}

.scm-view .monaco-list .monaco-list-row.cursor-default {
	cursor: default;
}

.scm-view.show-actions .scm-provider > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .scm-input > .scm-editor > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .resource-group > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .history-item > .actions {
	display: block;
}

.scm-view .monaco-list-row .actions .action-label {
	padding: 2px;
}

.scm-view .scm-input {
	height: 100%;
	display: flex;
	align-items: center;
	padding-left: 11px;
}

.scm-view .scm-input .scm-editor .scm-editor-toolbar {
	padding: 1px 3px 1px 1px;
}

.scm-view .scm-input .scm-editor .scm-editor-toolbar.hidden {
	display: none;
}

.scm-view .scm-input .scm-editor .scm-editor-toolbar.scroll-decoration {
	box-shadow: var(--vscode-scrollbar-shadow) 0 6px 6px -6px inset;
}

.scm-view .scm-input .scm-editor .scm-editor-toolbar .action-label.codicon.codicon-debug-stop {
	color: var(--vscode-icon-foreground) !important;
}

.scm-view .scm-editor-container .monaco-editor {
	border-radius: 2px;
}

.scm-view .scm-editor {
	box-sizing: border-box;
	width: 100%;
	display: flex;
	align-items: flex-start;
	box-sizing: border-box;
	border: 1px solid var(--vscode-input-border, transparent);
	background-color: var(--vscode-input-background);
	border-radius: 2px;
}

.scm-view .button-container {
	display: flex;
	height: 100%;
	padding-left: 11px;
	align-items: center;
}

.scm-view .button-container .codicon.codicon-cloud-upload,
.scm-view .button-container .codicon.codicon-sync {
	margin: 0 4px 0 0;
}

.scm-view .button-container .codicon.codicon-arrow-up,
.scm-view .button-container .codicon.codicon-arrow-down {
	font-size: small !important;
	margin: 0 4px 0 0;
}

.scm-view .button-container > .monaco-button-dropdown {
	flex-grow: 1;
	overflow: hidden;
}

.scm-view .button-container > .monaco-button-dropdown > .monaco-dropdown-button {
	display:flex;
	align-items: center;
	padding: 0 4px;
}


.scm-view .button-container > .monaco-button-dropdown > .monaco-button.monaco-text-button {
	min-width: 0;
}

.scm-view .button-container > .monaco-button-dropdown > .monaco-button.monaco-text-button > span:not(.codicon) {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.scm-view .scm-editor.hidden {
	display: none;
}

.monaco-workbench .part.panel .scm-view .scm-editor-container {
	outline: 1px solid var(--vscode-panelInput-border);
}

.scm-view .scm-editor.synthetic-focus,
.monaco-workbench .part.panel .scm-view .scm-editor.synthetic-focus {
	outline: 1px solid var(--vscode-focusBorder);
	outline-offset: -1px;
}

.scm-view .scm-editor.validation-info {
	outline: 1px solid var(--vscode-inputValidation-infoBorder) !important;
	outline-offset: -1px;
}

.scm-view .scm-editor.validation-warning {
	outline: 1px solid var(--vscode-inputValidation-warningBorder) !important;
	outline-offset: -1px;
}

.scm-view .scm-editor.validation-error {
	outline: 1px solid var(--vscode-inputValidation-errorBorder) !important;
	outline-offset: -1px;
}

.scm-editor-validation-container {
	display: flex;
	box-sizing: border-box;
	border-width: 1px;
	border-style: solid;
	border-top: none;
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;
	padding: 2px;
}

.scm-editor-validation-container.validation-info {
	background-color: var(--vscode-inputValidation-infoBackground);
	border-color: var(--vscode-inputValidation-infoBorder);
	color: var(--vscode-inputValidation-infoForeground);
}

.scm-editor-validation-container.validation-warning {
	background-color: var(--vscode-inputValidation-warningBackground);
	border-color: var(--vscode-inputValidation-warningBorder);
	color: var(--vscode-inputValidation-warningForeground);
}

.scm-editor-validation-container.validation-error {
	background-color: var(--vscode-inputValidation-errorBackground);
	border-color: var(--vscode-inputValidation-errorBorder);
	color: var(--vscode-inputValidation-errorForeground);
}

.scm-editor-validation {
	box-sizing: border-box;
	font-size: 0.9em;
	padding: 1px 3px;
	display: block;
	border-style: none;
	flex: auto;
}

.scm-editor-validation p {
	margin: 0;
	padding: 0;
}

.scm-editor-validation a {
	color: var(--vscode-textLink-foreground);
	-webkit-user-select: none;
	user-select: none;
}

.scm-editor-validation a:active,
.scm-editor-validation a:hover {
	color: var(--vscode-textLink-activeForeground);
}

.scm-editor-validation-actions {
	align-self: start;
	margin-top: 1px;
}

.scm-view .scm-editor-container .monaco-editor-background,
.scm-view .scm-editor-container .monaco-editor,
.scm-view .scm-editor-container .monaco-editor .margin,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor .margin,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor .monaco-editor-background {
	color: inherit;
	background-color: var(--vscode-input-background);
}

.scm-view .scm-editor-container .mtk1 {
	color: var(--vscode-input-foreground);
}

.scm-view .scm-editor-container .placeholder-text.mtk1 {
	color: var(--vscode-input-placeholderForeground);
}

/* Repositories */

.scm-repositories-view .scm-provider {
	margin: 0 12px 0 20px;
	overflow: hidden;
}

.scm-repositories-view .scm-provider > .label > .name {
	font-weight: normal;
}

/* History item hover */

.monaco-hover.history-item-hover p:first-child {
	margin-top: 4px;
}

.monaco-hover.history-item-hover p:last-child {
	margin-bottom: 2px !important;
}

.monaco-hover.history-item-hover p:last-child span:not(.codicon) {
	padding: 2px 0;
}

.monaco-hover.history-item-hover hr {
	margin-top: 4px;
	margin-bottom: 4px;
}

.monaco-hover.history-item-hover hr + p {
	margin: 4px 0;
}

.monaco-hover.history-item-hover span:not(.codicon) {
	margin-bottom: 0 !important;
}

.monaco-hover.history-item-hover p > span > span.codicon.codicon-git-branch {
	font-size: 12px;
	margin-bottom: 2px !important;
}

.monaco-hover.history-item-hover p > span > span.codicon.codicon-tag,
.monaco-hover.history-item-hover p > span > span.codicon.codicon-target {
	font-size: 14px;
	margin-bottom: 2px !important;
}

.monaco-hover.history-item-hover p > span > span.codicon.codicon-cloud {
	font-size: 14px;
	margin-bottom: 1px !important;
}

.monaco-hover.history-item-hover .hover-row.status-bar .action {
	display: flex;
	align-items: center;
}

.monaco-hover.history-item-hover .hover-row.status-bar .action .codicon {
	color: inherit;
	font-size: 12px;
}

/* Graph */

.pane-header .scm-graph-view-badge-container {
	display: flex;
	align-items: center;
	min-width: fit-content;
}

.pane-header .scm-graph-view-badge-container > .scm-graph-view-badge.monaco-count-badge.long {
	background-color: var(--vscode-badge-background);
	color: var(--vscode-badge-foreground);
	border: 1px solid var(--vscode-contrastBorder);
	margin: 0 6px;
	padding: 2px 4px;
}

.monaco-workbench .part.sidebar > .title > .title-actions .action-label.scm-graph-repository-picker,
.monaco-workbench .part.sidebar > .title > .title-actions .action-label.scm-graph-history-item-picker,
.monaco-workbench .part.auxiliarybar > .title > .title-actions .action-label.scm-graph-repository-picker,
.monaco-workbench .part.auxiliarybar > .title > .title-actions .action-label.scm-graph-history-item-picker,
.monaco-workbench .part.panel > .title > .title-actions .action-label.scm-graph-repository-picker,
.monaco-workbench .part.panel > .title > .title-actions .action-label.scm-graph-history-item-picker {
	display: flex;
}

.monaco-toolbar .action-item > .action-label.scm-graph-repository-picker,
.monaco-toolbar .action-item > .action-label.scm-graph-history-item-picker {
	color: var(--vscode-icon-foreground);
	align-items: center;
	font-weight: normal;
	line-height: 16px;
}

.monaco-toolbar .action-item.disabled > .action-label.scm-graph-repository-picker,
.monaco-toolbar .action-item.disabled > .action-label.scm-graph-history-item-picker {
	color: var(--vscode-disabledForeground);
}

.monaco-toolbar .action-label.scm-graph-repository-picker .codicon,
.monaco-toolbar .action-label.scm-graph-history-item-picker .codicon {
	font-size: 14px;
	color: inherit;
}

.monaco-toolbar .action-label.scm-graph-repository-picker > .name,
.monaco-toolbar .action-label.scm-graph-history-item-picker > .name {
	max-width: 100px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.scm-history-view .monaco-list-row > .monaco-tl-row > .monaco-tl-twistie.force-no-twistie {
	display: none !important;
}

.scm-history-view .scm-provider .label-name {
	font-weight: bold;
}

.scm-history-view .scm-provider .monaco-icon-label {
	align-items: center;
}

.scm-history-view .scm-provider .state-label.monaco-count-badge.long {
	display: flex;
	font-size: 0.8em;
	margin: 0 10px;
	color: var(--vscode-debugView-stateLabelForeground);
	background: var(--vscode-debugView-stateLabelBackground);
	border-radius: 2px;
}

.scm-history-view .scm-provider .actions {
	display: flex;
	flex-grow: 1;
}

.scm-view .monaco-list-row .history-item-change {
	display: flex;
	align-items: center;
}

.scm-view .monaco-list-row .history-item-change > .graph-placeholder {
	position: absolute;
	height: 22px;
}

.scm-view .monaco-list-row .history-item-change > .label-container {
	display: flex;
	flex: 1;
	overflow: hidden;
}

.scm-view .monaco-list-row .history-item-change > .label-container > .monaco-icon-label {
	flex-grow: 1;
}

.scm-history-view .history-item-load-more {
	display: flex;
	height: 22px;
}

.scm-history-view .history-item-load-more .graph-placeholder {
	mask-image: linear-gradient(black, transparent);
}

.scm-history-view .history-item-load-more .history-item-placeholder {
	flex-grow: 1;
}

.scm-history-view .history-item-load-more .history-item-placeholder .monaco-highlighted-label {
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--vscode-textLink-foreground)
}

.scm-history-view .history-item-load-more .history-item-placeholder .monaco-highlighted-label .codicon {
	font-size: 12px;
	color: var(--vscode-textLink-foreground)
}

.scm-history-view .history-item-load-more .history-item-placeholder.shimmer {
	padding: 2px 0;
}

.scm-history-view .history-item-load-more .history-item-placeholder.shimmer .monaco-icon-label-container {
	height: 18px;
	background: var(--vscode-scmGraph-historyItemHoverDefaultLabelBackground);
	border-radius: 2px;
	opacity: 0.5;
}
