{"extends": "./tsconfig.base.json", "compilerOptions": {"esModuleInterop": true, "removeComments": false, "preserveConstEnums": true, "sourceMap": false, "allowJs": true, "resolveJsonModule": true, "isolatedModules": false, "outDir": "../out/vs", "types": ["@webgpu/types", "mocha", "semver", "sinon", "trusted-types", "winreg", "wicg-file-system-access"], "plugins": [{"name": "tsec", "exemptionConfig": "./tsec.exemptions.json"}]}, "include": ["./*.ts", "./typings", "./vs/**/*.ts", "./vscode-dts/vscode.proposed.*.d.ts", "./vscode-dts/vscode.d.ts"]}