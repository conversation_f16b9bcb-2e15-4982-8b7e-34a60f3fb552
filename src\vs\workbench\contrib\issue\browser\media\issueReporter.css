/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.web.issue-reporter-body {
	position: absolute;
	overflow-y: scroll;
}

.web.issue-reporter-body .monaco-workbench select{
	-webkit-appearance: auto;
	appearance: auto;
}

/**
 * Table
 */

.issue-reporter-body table {
	width: 100%;
	max-width: 100%;
	background-color: transparent;
	border-collapse: collapse;
}

.issue-reporter-body th {
	vertical-align: bottom;
	border-bottom: 1px solid;
	padding: 5px;
	text-align: inherit;
}

.issue-reporter-body td {
	padding: 5px;
	vertical-align: top;
}

.issue-reporter-body tr td:first-child {
	width: 30%;
}

.issue-reporter-body label {
	user-select: none;
}

.issue-reporter-body .block-settingsSearchResults-details {
	padding-bottom: .5rem;
}

.issue-reporter-body .block-settingsSearchResults-details > div {
	padding: .5rem .75rem;
}

.issue-reporter-body .section {
	margin-bottom: .5em;
}

/**
 * Forms
 */
.issue-reporter-body input[type="text"],
.issue-reporter-body textarea {
	display: block;
	width: 100%;
	padding: .375rem .75rem;
	font-size: 1rem;
	line-height: 1.5;
	color: #495057;
}

.issue-reporter-body textarea {
	overflow: auto;
	resize: vertical;
}

/**
 * Button
 */

.issue-reporter-body .monaco-text-button {
	display: block;
	width: auto;
	padding: 4px 10px;
	align-self: flex-end;
	margin-bottom: 1em;
	font-size: 13px;
}

.issue-reporter-body select {
	height: calc(2.25rem + 2px);
	display: inline-block;
	padding: 3px 3px;
	font-size: 14px;
	line-height: 1.5;
	color: #495057;
	border: none;
}

.issue-reporter-body * {
	box-sizing: border-box;
}

.issue-reporter-body .issue-reporter textarea,
.issue-reporter-body .issue-reporter input,
.issue-reporter-body .issue-reporter select {
	font-family: inherit;
}

.issue-reporter-body html {
	color: #CCCCCC;
	height: 100%;
}

.issue-reporter-body .extension-caption .codicon-modifier-spin {
	padding-bottom: 3px;
	margin-left: 2px;
}

/* Font Families (with CJK support) */

.issue-reporter-body .mac.web {
	font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

.issue-reporter-body .mac.web:lang(zh-Hans) {
	font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", sans-serif;
}

.issue-reporter-body .mac.web:lang(zh-Hant) {
	font-family: -apple-system, BlinkMacSystemFont, "PingFang TC", sans-serif;
}

.issue-reporter-body .mac.web:lang(ja) {
	font-family: -apple-system, BlinkMacSystemFont, "Hiragino Kaku Gothic Pro", sans-serif;
}

.issue-reporter-body .mac.web:lang(ko) {
	font-family: -apple-system, BlinkMacSystemFont, "Apple SD Gothic Neo", "Nanum Gothic", "AppleGothic", sans-serif;
}

.issue-reporter-body .windows.web {
	font-family: "Segoe WPC", "Segoe UI", sans-serif;
}

.issue-reporter-body .windows.web:lang(zh-Hans) {
	font-family: "Segoe WPC", "Segoe UI", "Microsoft YaHei", sans-serif;
}

.issue-reporter-body .windows.web:lang(zh-Hant) {
	font-family: "Segoe WPC", "Segoe UI", "Microsoft Jhenghei", sans-serif;
}

.issue-reporter-body .windows.web:lang(ja) {
	font-family: "Segoe WPC", "Segoe UI", "Yu Gothic UI", "Meiryo UI", sans-serif;
}

.issue-reporter-body .windows.web:lang(ko) {
	font-family: "Segoe WPC", "Segoe UI", "Malgun Gothic", "Dotom", sans-serif;
}


/* Linux: add `system-ui` as first font and not `Ubuntu` to allow other distribution pick their standard OS font */
.issue-reporter-body .linux.web {
	font-family: system-ui, "Ubuntu", "Droid Sans", sans-serif;
}

.issue-reporter-body .linux.web:lang(zh-Hans) {
	font-family: system-ui, "Ubuntu", "Droid Sans", "Source Han Sans SC", "Source Han Sans CN", "Source Han Sans", sans-serif;
}

.issue-reporter-body .linux.web:lang(zh-Hant) {
	font-family: system-ui, "Ubuntu", "Droid Sans", "Source Han Sans TC", "Source Han Sans TW", "Source Han Sans", sans-serif;
}

.issue-reporter-body .linux.web:lang(ja) {
	font-family: system-ui, "Ubuntu", "Droid Sans", "Source Han Sans J", "Source Han Sans JP", "Source Han Sans", sans-serif;
}

.issue-reporter-body .linux.web:lang(ko) {
	font-family: system-ui, "Ubuntu", "Droid Sans", "Source Han Sans K", "Source Han Sans JR", "Source Han Sans", "UnDotum", "FBaekmuk Gulim", sans-serif;
}

body.issue-reporter-body  {
	margin: 0;
	overflow-y: auto;
	height: 100%;
	background-color: var(--vscode-editor-background)
}

.issue-reporter-body .hidden {
	display: none;
}

.issue-reporter-body .block {
	font-size: 12px;
}

.issue-reporter-body .block .block-info {
	width: 100%;
	font-size: 12px;
	overflow: auto;
	overflow-wrap: break-word;
	margin: 5px;
	padding: 10px;
}

.issue-reporter-body #issue-reporter {
	max-width: 85vw;
	margin-left: auto;
	margin-right: auto;
	padding-top: 2em;
	padding-bottom: 2em;
	display: flex;
	flex-direction: column;
	overflow: visible;
}

.issue-reporter-body .description-section {
	flex-grow: 0;
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
}

.issue-reporter-body textarea {
	flex-grow: 1;
	height: 200px;
}

.issue-reporter-body .block-info-text {
	display: flex;
	flex-grow: 0;
	flex-direction: column;
}

.issue-reporter-body #github-submit-btn {
	flex-shrink: 0;
	margin-left: auto;
	margin-top: 10px;
	margin-bottom: 10px;
}

.issue-reporter-body .two-col {
	display: inline-block;
	width: 49%;
}

.issue-reporter-body #vscode-version {
	width: 90%;
}

.issue-reporter-body .issue-reporter .input-group {
	margin-bottom: 1em;
	font-size: 16px;
}

.issue-reporter-body #extension-selection {
	margin-top: 1em;
}

.issue-reporter-body .issue-reporter select,
.issue-reporter-body .issue-reporter input,
.issue-reporter-body .issue-reporter textarea {
	border: 1px solid transparent;
	margin-top: 10px;
}


.issue-reporter-body .validation-error {
	font-size: 12px;
	padding: 10px;
	border-top: 0px !important;
}

.issue-reporter-body .system-info {
	margin-bottom: 10px;
}


.issue-reporter-body .issue-reporter input[type="checkbox"] {
	width: auto;
	display: inline-block;
	margin-top: 0;
	vertical-align: middle;
	cursor: pointer;
}

.issue-reporter-body .issue-reporter input:disabled {
	opacity: 0.6;
}

.issue-reporter-body .list-title {
	margin-top: 1em;
	margin-left: 1em;
}

.issue-reporter-body .instructions {
	font-size: 12px;
	margin-top: .5em;
}

.issue-reporter-body a,
.issue-reporter-body .workbenchCommand {
	cursor: pointer;
	border: 1px solid transparent;
	color: var(--vscode-textLink-foreground);
}

.issue-reporter-body .workbenchCommand:disabled {
	color: #868e96;
	cursor: default
}

.issue-reporter-body .block-extensions .block-info {
	margin-bottom: 1.5em;
}

.issue-reporter-body .showInfo,
.issue-reporter-body .input-group a {
	color: var(--vscode-textLink-foreground);
}

.issue-reporter-body .section .input-group .validation-error {
	margin-left: 100px;
}

.issue-reporter-body .section .inline-form-control,
.issue-reporter-body .section .inline-label {
	display: inline-block;
	font-size: initial;
}

.issue-reporter-body .section .inline-label {
	width: 95px;
}

.issue-reporter-body .section .inline-form-control,
.issue-reporter-body .section .input-group .validation-error {
	width: calc(100% - 100px);
}

.issue-reporter-body .issue-reporter .inline-label,
.issue-reporter-body .issue-reporter #issue-description-label {
	font-size: initial;
	cursor: default;
}

.issue-reporter-body .monaco-workbench .issue-reporter label {
	cursor: default;
}

.issue-reporter-body #issue-type,
.issue-reporter-body #issue-source,
.issue-reporter-body #extension-selector {
	cursor: pointer;
	cursor: pointer;
	appearance: auto;
	border: none;
	border-right: 6px solid transparent;
	padding-left: 10px;
}

.issue-reporter-body #similar-issues {
	margin-left: 15%;
	display: block;
}

.issue-reporter-body #problem-source-help-text {
	margin-left: calc(15% + 1em);
}

@media (max-width: 950px) {
	.issue-reporter-body .section .inline-label {
		width: 15%;
		font-size: 16px;
	}

	.issue-reporter-body #problem-source-help-text {
		margin-left: calc(15% + 1em);
	}

	.issue-reporter-body .section .inline-form-control,
	.issue-reporter-body .section .input-group .validation-error {
		width: calc(85% - 5px);
	}

	.issue-reporter-body .section .input-group .validation-error {
		margin-left: calc(15% + 4px);
	}
}

@media (max-width: 620px) {
	.issue-reporter-body .section .inline-label {
		display: none !important;
	}

	.issue-reporter-body #problem-source-help-text {
		margin-left: 1em;
	}

	.issue-reporter-body .section .inline-form-control,
	.issue-reporter-body .section .input-group .validation-error {
		width: 100%;
	}

	.issue-reporter-body #similar-issues,
	.issue-reporter-body .section .input-group .validation-error {
		margin-left: 0;
	}
}

.issue-reporter-body::-webkit-scrollbar {
	width: 14px;
}

.issue-reporter-body::-webkit-scrollbar-thumb {
	min-height: 20px;
}

.issue-reporter-body::-webkit-scrollbar-corner {
	display: none;
}

.issue-reporter-body .issues-container {
	margin-left: 1.5em;
	margin-top: .5em;
	max-height: 92px;
	overflow-y: auto;
}

.issue-reporter-body .issues-container > .issue {
	padding: 4px 0;
	display: flex;
}

.issue-reporter-body .issues-container > .issue > .issue-link {
	width: calc(100% - 82px);
	overflow: hidden;
	padding-top: 3px;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.issue-reporter-body .issues-container > .issue > .issue-state .codicon {
	width: 16px;
}

.issue-reporter-body .issues-container > .issue > .issue-state {
	display: flex;
	width: 77px;
	padding: 3px 6px;
	margin-right: 5px;
	color: #CCCCCC;
	background-color: #3c3c3c;
	border-radius: .25rem;
}

.issue-reporter-body .issues-container > .issue .label {
	padding-top: 2px;
	margin-left: 5px;
	width: 44px;
	text-overflow: ellipsis;
	overflow: hidden;
}

.issue-reporter-body .issues-container > .issue .issue-icon {
	padding-top: 2px;
}

.issue-reporter-body a {
	color: var(--vscode-textLink-foreground);
}

.issue-reporter-body .issue-reporter input[type="text"],
.issue-reporter-body .issue-reporter textarea,
.issue-reporter-body .issue-reporter select,
.issue-reporter-body .issue-reporter .issues-container > .issue > .issue-state,
.issue-reporter-body .issue-reporter .block-info {
	background-color: var(--vscode-input-background);
	color: var(--vscode-input-foreground);
}

.issue-reporter-body .monaco-workbench,
.issue-reporter-body::-webkit-scrollbar-track {
	background-color: var(--vscode-editor-background) !important;
}

.issue-reporter-body .issue-reporter input[type="text"],
.issue-reporter-body .issue-reporter textarea,
.issue-reporter-body .issue-reporter select {
	border: 1px solid var(--vscode-input-border)
}

.issue-reporter-body .issue-reporter input[type='text']:focus,
.issue-reporter-body .issue-reporter textarea:focus,
.issue-reporter-body .issue-reporter select:focus,
.issue-reporter-body .issue-reporter summary:focus,
.issue-reporter-body .issue-reporter button:focus,
.issue-reporter-body .issue-reporter a:focus,
.issue-reporter-body .issue-reporter .workbenchCommand:focus {
	border: 1px solid var(--vscode-inputOption-activeBorder);
	outline-style: none;
}

.issue-reporter-body .invalid-input,
.issue-reporter-body .invalid-input:focus,
.issue-reporter-body .validation-error {
	border: 1px solid var(--vscode-inputValidation-errorBorder) !important
}

.issue-reporter-body .required-input {
	color: var(--vscode-inputValidation-errorBorder)
}

.issue-reporter-body .validation-error {
	background: var(--vscode-inputValidation-errorBackground);
	color: var(--vscode-inputValidation-errorForeground)
}

.issue-reporter-body a,
.issue-reporter-body .workbenchCommand {
	color: var(--vscode-textLink-foreground)
}

.issue-reporter-body a:hover,
.issue-reporter-body .workbenchCommand:hover {
	color: var(--vscode-textLink-activeForeground)
}

.issue-reporter-body::-webkit-scrollbar-thumb:active {
	background-color: var(--vscode-scrollbarSlider-activeBackground)
}


.issue-reporter-body::-webkit-scrollbar-thumb,
.issue-reporter-body::-webkit-scrollbar-thumb:hover {
	background-color: var(--vscode-scrollbarSlider-hoverBackground)
}

.issue-reporter-update-banner {
	color: var(--vscode-textLink-foreground);
	color: var(--vscode-button-foreground);
	background-color: var(--vscode-button-background);
	padding: 10px;
	text-align: center;
	position: sticky;
	top: 0;
	z-index: 1000;
}
