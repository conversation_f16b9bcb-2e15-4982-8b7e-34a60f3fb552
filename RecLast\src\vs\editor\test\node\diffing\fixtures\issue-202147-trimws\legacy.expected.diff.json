{"original": {"content": "\n\n\t\titem.row!.domNode.classList.toggle('drop-target', item.dropTarget);\n\t}\n\n\na\nx\na\na\na", "fileName": "./1.tst"}, "modified": {"content": "\n\n\t\titem.row!.domNode.classList.toggle('drop-target', item.dropTarget !== false);\n\t\tif (typeof item.dropTarget !== 'boolean') {\n\t\t\titem.row!.domNode.classList.toggle('drop-target-start', item.dropTarget.first);\n\t\t}\n\t}\n\n\na\nx\na\na\n", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[3,4)", "modifiedRange": "[3,7)", "innerChanges": [{"originalRange": "[3,68 -> 3,70 EOL]", "modifiedRange": "[3,68 -> 6,4 EOL]"}]}, {"originalRange": "[11,12)", "modifiedRange": "[14,15)", "innerChanges": null}]}