[{"c": "@", "t": "text.html.cshtml meta.structure.razor.codeblock keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "{", "t": "text.html.cshtml meta.structure.razor.codeblock keyword.control.razor.directive.codeblock.open", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "    ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "var", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs storage.type.var.cs", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "total", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs entity.name.variable.local.cs", "r": {"dark_plus": "entity.name.variable: #9CDCFE", "light_plus": "entity.name.variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "entity.name.variable: #9CDCFE", "hc_light": "entity.name.variable: #001080", "light_modern": "entity.name.variable: #001080"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs keyword.operator.assignment.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "0", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs constant.numeric.decimal.cs", "r": {"dark_plus": "constant.numeric: #B5CEA8", "light_plus": "constant.numeric: #098658", "dark_vs": "constant.numeric: #B5CEA8", "light_vs": "constant.numeric: #098658", "hc_black": "constant.numeric: #B5CEA8", "dark_modern": "constant.numeric: #B5CEA8", "hc_light": "constant.numeric: #096D48", "light_modern": "constant.numeric: #098658"}}, {"c": ";", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs punctuation.terminator.statement.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "    ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "var", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs storage.type.var.cs", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "totalMessage", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs entity.name.variable.local.cs", "r": {"dark_plus": "entity.name.variable: #9CDCFE", "light_plus": "entity.name.variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "entity.name.variable: #9CDCFE", "hc_light": "entity.name.variable: #001080", "light_modern": "entity.name.variable: #001080"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs keyword.operator.assignment.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs string.quoted.double.cs punctuation.definition.string.begin.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs string.quoted.double.cs punctuation.definition.string.end.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": ";", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs punctuation.terminator.statement.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "    ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "@", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "*", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor keyword.control.razor.comment.star", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": " a multiline", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor comment.block.razor", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "      razor comment embedded in csharp ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor comment.block.razor", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "*", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor keyword.control.razor.comment.star", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "@", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.comment.razor keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "    ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "if", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor keyword.control.conditional.if.cs", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "(", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor punctuation.parenthesis.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "IsPost", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor variable.other.readwrite.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ")", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor punctuation.parenthesis.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "{", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.curlybrace.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "        ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs punctuation.whitespace.comment.leading.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "//", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs punctuation.definition.comment.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": " Retrieve the numbers that the user entered.", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "        ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "var", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock storage.type.var.cs", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "num1", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.variable.local.cs", "r": {"dark_plus": "entity.name.variable: #9CDCFE", "light_plus": "entity.name.variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "entity.name.variable: #9CDCFE", "hc_light": "entity.name.variable: #001080", "light_modern": "entity.name.variable: #001080"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock keyword.operator.assignment.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "Request", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock variable.other.object.property.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": "[", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.squarebracket.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs punctuation.definition.string.begin.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "text1", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs punctuation.definition.string.end.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "]", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.squarebracket.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ";", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.terminator.statement.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "        ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "var", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock storage.type.var.cs", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "num2", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.variable.local.cs", "r": {"dark_plus": "entity.name.variable: #9CDCFE", "light_plus": "entity.name.variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "entity.name.variable: #9CDCFE", "hc_light": "entity.name.variable: #001080", "light_modern": "entity.name.variable: #001080"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock keyword.operator.assignment.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "Request", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock variable.other.object.property.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": "[", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.squarebracket.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs punctuation.definition.string.begin.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "text2", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "\"", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock string.quoted.double.cs punctuation.definition.string.end.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "]", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.squarebracket.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ";", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.terminator.statement.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "        ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs punctuation.whitespace.comment.leading.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "//", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs punctuation.definition.comment.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": " Convert the entered strings into integers numbers and add.", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock comment.line.double-slash.cs", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "        ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "total", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock variable.other.readwrite.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "=", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock keyword.operator.assignment.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "num1", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock variable.other.object.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ".", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.accessor.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "AsInt", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.function.cs", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.parenthesis.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ")", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.parenthesis.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "+", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock keyword.operator.arithmetic.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": " ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "num2", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock variable.other.object.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ".", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.accessor.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "AsInt", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.function.cs", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.parenthesis.open.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ")", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.parenthesis.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": ";", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.terminator.statement.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\t\t", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "italic", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "bold", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "totalMessage = \"Total = \" + total;", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "bold", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "italic", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "}", "t": "text.html.cshtml meta.structure.razor.codeblock source.cs meta.statement.if.razor meta.structure.razor.csharp.codeblock punctuation.curlybrace.close.cs", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "}", "t": "text.html.cshtml meta.structure.razor.codeblock keyword.control.razor.directive.codeblock.close", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "<!", "t": "text.html.cshtml meta.tag.metadata.doctype.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "DOCTYPE", "t": "text.html.cshtml meta.tag.metadata.doctype.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.metadata.doctype.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "html", "t": "text.html.cshtml meta.tag.metadata.doctype.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.metadata.doctype.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.html.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "html", "t": "text.html.cshtml meta.tag.structure.html.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.html.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "lang", "t": "text.html.cshtml meta.tag.structure.html.start.html meta.attribute.lang.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.html.start.html meta.attribute.lang.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.html.start.html meta.attribute.lang.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "en", "t": "text.html.cshtml meta.tag.structure.html.start.html meta.attribute.lang.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.html.start.html meta.attribute.lang.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.html.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.head.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "head", "t": "text.html.cshtml meta.tag.structure.head.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.head.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.metadata.title.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "title", "t": "text.html.cshtml meta.tag.metadata.title.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.metadata.title.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Add Numbers", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.metadata.title.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "title", "t": "text.html.cshtml meta.tag.metadata.title.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.metadata.title.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.metadata.meta.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "meta", "t": "text.html.cshtml meta.tag.metadata.meta.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.metadata.meta.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "charset", "t": "text.html.cshtml meta.tag.metadata.meta.void.html meta.attribute.charset.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.metadata.meta.void.html meta.attribute.charset.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "utf-8", "t": "text.html.cshtml meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.metadata.meta.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "/>", "t": "text.html.cshtml meta.tag.metadata.meta.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.head.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "head", "t": "text.html.cshtml meta.tag.structure.head.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.head.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.body.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "body", "t": "text.html.cshtml meta.tag.structure.body.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.body.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Enter two whole numbers and then click ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.inline.strong.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "strong", "t": "text.html.cshtml meta.tag.inline.strong.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.inline.strong.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Add", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.inline.strong.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "strong", "t": "text.html.cshtml meta.tag.inline.strong.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.inline.strong.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": ".", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.form.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "form", "t": "text.html.cshtml meta.tag.structure.form.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.form.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "action", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.action.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.action.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.action.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.action.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.form.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "method", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.method.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.method.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.method.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "post", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.method.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.form.start.html meta.attribute.method.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.form.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.label.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "label", "t": "text.html.cshtml meta.tag.structure.label.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.label.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "for", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text1", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.label.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "First Number:", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.label.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "label", "t": "text.html.cshtml meta.tag.structure.label.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.label.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "      ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "input", "t": "text.html.cshtml meta.tag.structure.input.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "type", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "name", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text1", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "/>", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.label.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "label", "t": "text.html.cshtml meta.tag.structure.label.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.label.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "for", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text2", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.label.start.html meta.attribute.for.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.label.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Second Number:", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.label.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "label", "t": "text.html.cshtml meta.tag.structure.label.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.label.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "      ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "input", "t": "text.html.cshtml meta.tag.structure.input.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "type", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "name", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text2", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.name.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "/>", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "    ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "input", "t": "text.html.cshtml meta.tag.structure.input.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "type", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "submit", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "value", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.value.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.value.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.value.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "Add", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.value.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.cshtml meta.tag.structure.input.void.html meta.attribute.value.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.cshtml meta.tag.structure.input.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "/>", "t": "text.html.cshtml meta.tag.structure.input.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.form.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "form", "t": "text.html.cshtml meta.tag.structure.form.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.form.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "@", "t": "text.html.cshtml meta.comment.razor keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "*", "t": "text.html.cshtml meta.comment.razor keyword.control.razor.comment.star", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": " now we call the totalMessage method", "t": "text.html.cshtml meta.comment.razor comment.block.razor", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "\t   (a multi line razor comment outside code) ", "t": "text.html.cshtml meta.comment.razor comment.block.razor", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "*", "t": "text.html.cshtml meta.comment.razor keyword.control.razor.comment.star", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "@", "t": "text.html.cshtml meta.comment.razor keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "@", "t": "text.html.cshtml meta.expression.implicit.cshtml keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "totalMessage", "t": "text.html.cshtml meta.expression.implicit.cshtml source.cs variable.other.object.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  ", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "@", "t": "text.html.cshtml meta.expression.explicit.cshtml keyword.control.cshtml.transition", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "(", "t": "text.html.cshtml meta.expression.explicit.cshtml keyword.control.cshtml", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "totalMessage", "t": "text.html.cshtml meta.expression.explicit.cshtml variable.other.readwrite.cs", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": "+", "t": "text.html.cshtml meta.expression.explicit.cshtml keyword.operator.arithmetic.cs", "r": {"dark_plus": "keyword.operator: #D4D4D4", "light_plus": "keyword.operator: #000000", "dark_vs": "keyword.operator: #D4D4D4", "light_vs": "keyword.operator: #000000", "hc_black": "keyword.operator: #D4D4D4", "dark_modern": "keyword.operator: #D4D4D4", "hc_light": "keyword.operator: #000000", "light_modern": "keyword.operator: #000000"}}, {"c": "\"", "t": "text.html.cshtml meta.expression.explicit.cshtml string.quoted.double.cs punctuation.definition.string.begin.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "!", "t": "text.html.cshtml meta.expression.explicit.cshtml string.quoted.double.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "\"", "t": "text.html.cshtml meta.expression.explicit.cshtml string.quoted.double.cs punctuation.definition.string.end.cs", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": ")", "t": "text.html.cshtml meta.expression.explicit.cshtml keyword.control.cshtml", "r": {"dark_plus": "keyword.control: #C586C0", "light_plus": "keyword.control: #AF00DB", "dark_vs": "keyword.control: #569CD6", "light_vs": "keyword.control: #0000FF", "hc_black": "keyword.control: #C586C0", "dark_modern": "keyword.control: #C586C0", "hc_light": "keyword.control: #B5200D", "light_modern": "keyword.control: #AF00DB"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "p", "t": "text.html.cshtml meta.tag.structure.p.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.p.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "  An email address (with escaped at character): name", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "@@", "t": "text.html.cshtml constant.character.escape.razor.transition", "r": {"dark_plus": "constant.character.escape: #D7BA7D", "light_plus": "constant.character.escape: #EE0000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "constant.character: #569CD6", "dark_modern": "constant.character.escape: #D7BA7D", "hc_light": "constant.character.escape: #EE0000", "light_modern": "constant.character.escape: #EE0000"}}, {"c": "domain.com", "t": "text.html.cshtml", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.body.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "body", "t": "text.html.cshtml meta.tag.structure.body.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.body.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.cshtml meta.tag.structure.html.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "html", "t": "text.html.cshtml meta.tag.structure.html.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.cshtml meta.tag.structure.html.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}]