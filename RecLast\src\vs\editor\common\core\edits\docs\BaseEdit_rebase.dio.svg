<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="341px" height="141px" viewBox="-0.5 -0.5 341 141" content="&lt;mxfile&gt;&lt;diagram id=&quot;-Th-3Xml5MnJZz-ctsqk&quot; name=&quot;Page-1&quot;&gt;7Vlbb9owFP41SNsDlePEDnks9LKHbZrUh21PlUtMsBpiZkyB/frZxCaxk0JLQ9tNK1Kxj49Pjr9zjemFo9n6WpD59AtPad6DIF33wosehIMoUv81YWMIeFASMsHSkhRUhBv2mxoiMNQlS+nCYZSc55LNXeKYFwUdS4dGhOArl23Cc/epc5LRBuFmTPIm9TtL5dScAoGK/omybGqfHACzMiOW2RAWU5LyVY0UXvbCkeBclqPZekRzjZ3Fpdx39cjqTjFBC/mkDYnRQ27s4WiqzmqmBS/U13AqZ7maBWqo5IrNDzUBdvJTT86QnV6s64sXGztbM/nDylDjchdEZlrt0hO7qXkac8AFX4qx0RcaByAio4bLuJc+SW2bQeCa8hlVqikGQXMi2YNrVWKcI9vx7bZ+40wpAoHx48ga3LgxwuBsAF0ppaZmY90QniwUerLC5AyAYGA/oSu2PG1D7LkQZFNjm2uGxZ4TYO+pMdirZYM/cvjVoNTAzmpoV6StC7a7ow2LB5IvrTtak7se+pncqZziuCbJWVao8Vh5CxWK8ECFZCpqz83CjKWpljEUdMF+k7utPO1nBiYlHA176EJRci1+SMb3meDLIh3xnIsqHia8kFdkxnINw0hZmKnnQfCVrsyi5e/BEGz/2p3ZBJ/Wk67bMpfR0UkOjgebXX0VSsDu3TiSXujjMHaE9gPsSuCTyYJKzwmebfbouVmoNZvE+7PJsanr2CwEw5OkoRfHePK8GMew4xhvhHjD+NuYo6kx8GrKJL2Zky3UK9VQuL7wqHkaYfVo+ISDRiIv56uqulvStFbYTfZrtZUTDvvgwJ34/nGuDzvx/ajF99G78H0c7a1Xh/njjn1/0Kxv8PYtCtzBEma67iBoK2lJsi1pB6oky3OPlAmSMloJq9VT6/AtLawNkZfXSXAWARydoEz2Q+jWSZicoE5G7y91IuhFDHy91Bk8AY8DCLT46EIKfk89YidFBrlIgSZSQdwCVdBFlQkaUNEmWP9MY91VvohirxfaZ4v2zBAMTpAIbFf+Jk1D3E3DHP41XQP2L1EOdQ1Jt11D1JbncC51spqTwvED/Gup746G4zJQzjVY2d0HpZB6DAhQWA7Ax62dgI6p/sREnGZWEshMJ0kjxw1Cb7EmY2USlpYRm7AEd7s473v6QBSUitQHpU72BGqU6W8a3NrTKqDKA5crb5G/nplZvEYm3ptt1Fs8sr3Oro/opD3xmhNbOLvNSeH7705wS8092Ysden94YP8u4BVfdKMn4EGL9Fzfz1c1LCWL6Q6gGhia/o1IFc/FlgJBuOvd7J083IHmFZQWFJuQ1SBBLZBY2gsjEwH31g4D5Ip46gUyDlxBKPJ0eeTK+Jh6hP9bsuXebJB4BoDHWRIlriAMYEeWVNPqp6WSvfp9Lrz8Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <path d="M 60 70 L 70 70 Q 80 70 80 60 L 80 50 Q 80 40 90 40 L 133.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 138.88 40 L 131.88 43.5 L 133.63 40 L 131.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 30px; margin-left: 106px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    e1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="106" y="34" fill="#000000" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        e1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 60 90 L 70 90 Q 80 90 80 100 L 80 110 Q 80 120 90 120 L 133.63 120" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 138.88 120 L 131.88 123.5 L 133.63 120 L 131.88 116.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="0" y="60" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 200 40 L 250 40 Q 260 40 260 50 L 260 60 Q 260 70 266.82 70 L 273.63 70" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 278.88 70 L 271.88 73.5 L 273.63 70 L 271.88 66.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 29px; margin-left: 229px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #009900; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#009900, #32b532); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    e2_
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="229" y="32" fill="#009900" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        e2_
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="140" y="20" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="5" y="0" width="170" height="10" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 108px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    e2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="108" y="114" fill="#000000" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        e2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 200 120 L 250 120 Q 260 120 260 110 L 260 100 Q 260 90 266.82 90 L 273.63 90" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 278.88 90 L 271.88 93.5 L 273.63 90 L 271.88 86.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 108px; margin-left: 229px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    <span style="color: light-dark(rgb(0, 153, 0), rgb(50, 181, 50)); font-family: &quot;Courier New&quot;; font-weight: 700; background-color: light-dark(rgb(251, 251, 251), rgb(21, 21, 21));">
                                        e1_
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="229" y="112" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        e1_
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="140" y="100" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="280" y="60" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 121 105 L 231 41" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 109 42 L 219 102" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
