{"version": 3, "file": "MemoryManager.js", "sourceRoot": "", "sources": ["../../src/memory/MemoryManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAyB;AACzB,2CAA6B;AA0B7B,MAAa,aAAa;IAMtB,YAAY,OAAgC;QAHpC,gBAAW,GAAkB,EAAE,CAAC;QAChC,iBAAY,GAAkB,EAAE,CAAC;QAGrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAE3E,oCAAoC;QACpC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACpC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SACzD;QAED,+CAA+C;QAC/C,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc,CAAC,IAA0B,EAAE,OAAe,EAAE,IAAiC;QACzF,MAAM,OAAO,GAAgB;YACzB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;SACP,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/B,uDAAuD;QACvD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE;YAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc,CAAC,IAAkC;QAC7C,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;SAC5D;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gBAAgB,CAAC,IAAkC;QAC/C,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;SACxE;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;SACzB;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc,CAAC,QAAgB,EAAE,OAAe,EAAE,QAAgB;QAC9D,MAAM,OAAO,GAAgB;YACzB,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,4CAA4C;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhC,kCAAkC;QAClC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SACpD;IACL,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,IAAY;QAC7B,MAAM,QAAQ,GAAa;YACvB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACrE,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClD,OAAO,KAAK,CAAC;YACjB,CAAC,EAAE,EAAS,CAAC;YACb,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;SACtC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;QAE1E,IAAI;YACA,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAClE,OAAO,QAAQ,CAAC,EAAE,CAAC;SACtB;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SAC1D;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,UAAkB;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,UAAU,OAAO,CAAC,CAAC;QAEzE,IAAI;YACA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAa,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAEpD,uBAAuB;YACvB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,wBAAwB;YACxB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC;YAEzC,0BAA0B;YAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;gBAChE,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACxD;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;SACxD;IACL,CAAC;IAED,aAAa;QACT,IAAI;YACA,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,CAAC;YAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBACxB,IAAI;wBACA,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;wBACzD,MAAM,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;wBAC3D,MAAM,QAAQ,GAAa,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBAEpD,SAAS,CAAC,IAAI,CAAC;4BACX,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,IAAI,EAAE,QAAQ,CAAC,IAAI;4BACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;yBAChC,CAAC,CAAC;qBACN;oBAAC,OAAO,KAAK,EAAE;wBACZ,OAAO,CAAC,KAAK,CAAC,0BAA0B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;qBAC3D;iBACJ;aACJ;YAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;SAC9D;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,UAAU,OAAO,CAAC,CAAC;QAEzE,IAAI;YACA,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;gBAC7B,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;aAC/B;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SAC1D;IACL,CAAC;IAEO,eAAe;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAgB,aAAa,EAAE,EAAE,CAAC,CAAC;QAC/E,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;IAC/B,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC;IAEO,UAAU;QACd,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,OAAO;QACH,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;CACJ;AAzLD,sCAyLC"}