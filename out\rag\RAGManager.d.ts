import * as vscode from 'vscode';
export interface Document {
    id: string;
    title: string;
    content: string;
    source: string;
    language: string;
    timestamp: number;
    embedding?: number[];
}
export interface SearchResult {
    document: Document;
    score: number;
    relevantChunk: string;
}
export declare class RAGManager {
    private context;
    private documents;
    private vectorDbPath;
    private isInitialized;
    constructor(context: vscode.ExtensionContext);
    initialize(): Promise<void>;
    addDocument(title: string, content: string, source: string, language: string): Promise<string>;
    searchDocuments(query: string, limit?: number): Promise<SearchResult[]>;
    getRelevantContext(query: string, maxTokens?: number): Promise<string>;
    private simpleTextSearch;
    private extractRelevantChunk;
    private generateMockEmbedding;
    private loadDefaultDocumentation;
    private loadDocuments;
    private saveDocuments;
    private generateId;
    getDocumentCount(): number;
    dispose(): void;
}
//# sourceMappingURL=RAGManager.d.ts.map