<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="391px" height="140px" viewBox="-0.5 -0.5 391 140" content="&lt;mxfile&gt;&lt;diagram id=&quot;-Th-3Xml5MnJZz-ctsqk&quot; name=&quot;Page-1&quot;&gt;1VhLU9swEP41nmkPMPIrjo8kPHpoO53hUDgKW7E1yFZGVkjCr69srWIrdopTQoELaD/vrlbfPiRw/HmxuRF4mf/gKWGOh9KN4186nhf4U/WzBrYaiNxYA5mgqYbcFrilzwRABOiKpqSyFCXnTNKlDSa8LEkiLQwLwde22oIze9clzkgPuE0w66O/aSpzjU5D1OLfCM1ys7OL4EuBjTIAVY5Tvu5A/pXjzwXnUq+KzZywmjvDi7a7PvB1F5ggpRxjEEIYcmvORlJ1VBBLXqpfs1wWTEmuWpINlXed9b1ao/MQpMs6wcgIWyOUUmzvukLHqhZbs0Yydv2zwPEqvhIJhOtB+rHICGgFGqoP0jGD898QXhC1i1IQhGFJn+ycYiiNbKfXsqcWQOAwmRPt4gmzFTiVOa0GGf6OH1RLWNRiRrNSrRN1XCIU8ESEpKroLuBDQdO09jETpKLP+KHxVxO15LSUTdzhzAkvFcJq9zOcPGaCr8p0zhkXbT4XvJTXuKCsZnquyKRqPw/9JOtB1qFG6nDIZqi/IBSrhC2mweoMnSPgCBr/DNI3OhXg+1d94o4b17NN+GJRqXLYz90upFHpNE6Pbo62Ie47X15qjrYfdlZv0BzRqZujMb0QAm87ClCR/YRBJfgxsuogjPfG1J5+5P5VXy10BON2m5hBbcLVnIHVEWH0HGmme47+pfj8wWFynvBiySvyhcuciK+fbLbUH42+4/kIxTFCgJu2co2sb323kSlje/tkAqeUtO4AHmgL75TjK4giqwLc180vE49dVmfxG0wzr1dPveJp8klSSMI6p5LcLnEzTNbqHWeX1MEB1CP6IKH+ZK+rzato3T6qjEreeU8F6DDBY+/q6Wd/+AT/YbaPJTPu1VYznj7ZdOrTPj3l6Ig8u8lfOTp2bvZMTjIsgo83LHbD4R2GRfTx6JhM348Od0R5vMDAwI1eScEfycv3+dG3TGgzFYRxjyk3GqDKPZ4qJbZ/u+u+a/8B4l/9AQ==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <path d="M 70 81 L 163.63 81" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 168.88 81 L 161.88 84.5 L 163.63 81 L 161.88 77.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 71px; margin-left: 117px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    this
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="117" y="75" fill="light-dark(#000000, #ffffff)" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        this
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 40 101 L 40 121 Q 40 131 50 131 L 350 131 Q 360 131 360 121 L 360 107.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 360 102.12 L 363.5 109.12 L 360 107.37 L 356.5 109.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 121px; margin-left: 201px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #009900; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#009900, #32b532); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    this.compose(other)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="201" y="125" fill="#009900" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        this.compose(other)
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="10" y="61" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 230 81 L 323.63 81" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 328.88 81 L 321.88 84.5 L 323.63 81 L 321.88 77.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 71px; margin-left: 277px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Courier New&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    other
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="277" y="75" fill="light-dark(#000000, #ffffff)" font-family="&quot;Courier New&quot;" font-size="11px" text-anchor="middle">
                        other
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="170" y="61" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="330" y="61" width="60" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="0" y="0" width="170" height="10" fill="none" stroke="none" pointer-events="all"/>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
