{"version": 3, "sources": ["split.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,mCAAmC;AAE5B,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAA7D,QAAA,aAAa,iBAAgD;AAE1E;;;;GAIG;AACH,MAAa,cAAe,SAAQ,kBAAS;IAG5C,YAA6B,QAAgB;QAC5C,KAAK,EAAE,CAAC;QADoB,aAAQ,GAAR,QAAQ,CAAQ;IAE7C,CAAC;IAEQ,UAAU,CAAC,KAAa,EAAE,SAAiB,EAAE,QAAoD;QACzG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACrB,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClB,MAAM;YACP,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAC/C,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvF,QAAQ,EAAE,CAAC;IACZ,CAAC;IAEQ,MAAM,CAAC,QAAoD;QACnE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAED,QAAQ,EAAE,CAAC;IACZ,CAAC;CACD;AApCD,wCAoCC", "file": "split.js", "sourceRoot": "../src/"}