/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .pane-body.integrated-terminal {
	align-content: flex-start;
	align-items: baseline;
	display: flex;
	flex-direction: column;
	background-color: transparent !important;
	user-select: initial;
	-webkit-user-select: initial;
	position: relative;
	/* Create stacking context before to terminal canvases. */
	z-index: 0;
}

.terminal-command-decoration.hide {
	visibility: hidden;
}
.monaco-workbench .part.panel .pane-body.integrated-terminal .terminal-outer-container {
	background-color: var(--vscode-terminal-background, var(--vscode-panel-background));
}
.monaco-workbench .pane-body.integrated-terminal .terminal-outer-container,
.monaco-workbench .pane-body.integrated-terminal .terminal-groups-container,
.monaco-workbench .pane-body.integrated-terminal .terminal-group,
.monaco-workbench .pane-body.integrated-terminal .terminal-split-pane,
.monaco-workbench .terminal-editor .terminal-split-pane,
.monaco-workbench .terminal-editor .terminal-outer-container {
	height: 100%;
}
.monaco-workbench .part.sidebar .pane-body.integrated-terminal .terminal-outer-container,
.monaco-workbench .part.auxiliarybar .pane-body.integrated-terminal .terminal-outer-container {
	background-color: var(--vscode-terminal-background, var(--vscode-sideBar-background));
}

.monaco-workbench .pane-body.integrated-terminal .split-view-view:not(:first-child),
.monaco-workbench .pane-body.integrated-terminal .tabs-container {
	border-color: var(--vscode-terminal-border);
}

.monaco-workbench .pane-body.integrated-terminal .terminal-drop-overlay {
	background-color: var(--vscode-terminal-dropBackground, var(--vscode-editorGroup-dropBackground));
}

.monaco-workbench .pane-body.integrated-terminal .terminal-tabs-entry.is-active::before {
	background-color: var(--vscode-terminal-tab-activeBorder);
}
/* Override monaco's styles for terminal editors */
.monaco-workbench .terminal-editor .xterm textarea:focus {
	opacity: 0 !important;
	outline: 0 !important;
}

.monaco-workbench .xterm .xterm-helper-textarea:focus {
	/* Override the general vscode style applies `opacity:1!important` to textareas */
	opacity: 0 !important;
}

.monaco-workbench .terminal-tab:not(.terminal-uri-icon)::before {
	background-image: none !important;
}

.monaco-workbench .terminal-editor .terminal-wrapper {
	background-color: var(--vscode-terminal-background, var(--vscode-editorPane-background));
}
.monaco-workbench .terminal-editor .terminal-wrapper,
.monaco-workbench .pane-body.integrated-terminal .terminal-wrapper {
	display: block;
	height: 100%;
	box-sizing: border-box;
}

.monaco-workbench .xterm {
	/* All terminals have at least 20px left padding for the gutter */
	padding-left: 20px;
}

.monaco-workbench .xterm .xterm-scrollable-element {
	/* Offset the scrollable element such that:
	 * - The terminal grid will be positioned to the right of the gutter
	 * - Elements are not hidden in the gutter */
	margin-left: -20px;
	padding-left: 20px;
}

.monaco-workbench .terminal-editor .xterm,
.monaco-workbench .pane-body.integrated-terminal .xterm {
	/* Bottom align the terminal within the split pane */
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
}

.terminal-side-view .terminal.xterm {
	/* Top align the terminal within the split pane when the panel is vertical */
	top: 0;
}

.monaco-workbench .terminal-editor .terminal-wrapper.fixed-dims .xterm,
.monaco-workbench .pane-body.integrated-terminal .terminal-wrapper.fixed-dims .xterm {
	position: static;
}

.monaco-workbench .terminal-editor .xterm-viewport,
.monaco-workbench .pane-body.integrated-terminal .xterm-viewport {
	z-index: 30;
}

.monaco-workbench .terminal-editor .xterm-decoration-overview-ruler,
.monaco-workbench .pane-body.integrated-terminal .xterm-decoration-overview-ruler {
	z-index: 31; /* Must be higher than .xterm-viewport */
	pointer-events: none;
}

.monaco-workbench .terminal-editor .xterm-screen,
.monaco-workbench .pane-body.integrated-terminal .xterm-screen {
	z-index: 31;
}

.xterm .xterm-screen {
	cursor: text;
}

.monaco-workbench .simple-find-part-wrapper.result-count {
	z-index: 33 !important;
}

.xterm .xterm-accessibility {
	z-index: 32 !important;
	pointer-events: none;
}

/* Apply cursor styles to xterm-screen as well due to how .xterm-viewport/.xterm are positioned */
.xterm.enable-mouse-events .xterm-screen  { cursor: default; }
.xterm.xterm-cursor-pointer .xterm-screen { cursor: pointer; }
.xterm.column-select.focus .xterm-screen  { cursor: crosshair; }

.monaco-workbench .terminal-editor .xterm a:not(.xterm-invalid-link),
.monaco-workbench .pane-body.integrated-terminal .xterm a:not(.xterm-invalid-link) {
	/* To support message box sizing */
	position: relative;
}

.monaco-workbench .terminal-editor .terminal-wrapper > div,
.monaco-workbench .pane-body.integrated-terminal .terminal-wrapper > div {
	height: 100%;
}

.monaco-workbench .terminal-editor .xterm-viewport,
.monaco-workbench .pane-body.integrated-terminal .xterm-viewport {
	box-sizing: border-box;
}

.monaco-workbench .terminal-editor .terminal-wrapper.fixed-dims,
.monaco-workbench .pane-body.integrated-terminal .terminal-wrapper.fixed-dims {
	/* The viewport should be positioned against this so it does't conflict with a fixed dimensions terminal horizontal scroll bar*/
	position: relative;
}

.monaco-workbench .terminal-editor .terminal-wrapper:not(.fixed-dims) .xterm-viewport,
.monaco-workbench .pane-body.integrated-terminal .terminal-wrapper:not(.fixed-dims) .xterm-viewport {
	/* Override xterm.js' position so the area that accepts mouse events extends to the edge of the scroll bar */
	right: 14px;
}

.monaco-workbench .pane-body.integrated-terminal .split-view-view {
	box-sizing: border-box;
	overflow: hidden;
}

.monaco-workbench .pane-body.integrated-terminal .split-view-view:first-child .tabs-container {
	border-right-width: 1px;
	border-right-style: solid;
}
.monaco-workbench .pane-body.integrated-terminal .split-view-view:last-child .tabs-container {
	border-left-width: 1px;
	border-left-style: solid;
}

/* border-color is set by theme key terminal.border */
.monaco-workbench .pane-body.integrated-terminal .terminal-group .monaco-split-view2.horizontal .split-view-view:not(:first-child) {
	border-left-width: 1px;
	border-left-style: solid;
}
.monaco-workbench .pane-body.integrated-terminal .terminal-group .monaco-split-view2.vertical .split-view-view:not(:first-child) {
	border-top-width: 1px;
	border-top-style: solid;
}

/* Use the default cursor when alt is active to help with clicking to move cursor */
.monaco-workbench .pane-body.integrated-terminal .terminal-groups-container.alt-active .xterm {
	cursor: default;
}

.monaco-workbench .pane-body.integrated-terminal .xterm {
	user-select: none;
	-webkit-user-select: none;
}
.monaco-workbench .pane-body.integrated-terminal .monaco-split-view2.vertical .split-view-view:not(:last-child) .xterm {
	/* When vertical and NOT the bottom terminal, align to the top instead to prevent the output jumping around erratically */
	top: 0;
	bottom: auto;
}

.monaco-workbench .pane-body.integrated-terminal .xterm:focus {
	/* Hide outline when focus jumps from xterm to the text area */
	outline: none;
}

.monaco-workbench.hc-black .pane-body.integrated-terminal .xterm.focus::before,
.monaco-workbench.hc-black .pane-body.integrated-terminal .xterm:focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .xterm:focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .xterm.focus::before {
	display: block;
	content: "";
	border: 1px solid;
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 32;
	pointer-events: none;
}

.monaco-workbench.hc-black .pane-body.integrated-terminal .monaco-split-view2.horizontal .split-view-view:not(:only-child) .xterm.focus::before,
.monaco-workbench.hc-black .pane-body.integrated-terminal .monaco-split-view2.horizontal .split-view-view:not(:only-child) .xterm:focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .monaco-split-view2.horizontal .split-view-view:not(:only-child) .xterm.focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .monaco-split-view2.horizontal .split-view-view:not(:only-child) .xterm:focus::before {
	right: 0;
}

.monaco-workbench .pane-body.integrated-terminal .xterm .xterm-helpers {
	position: absolute;
	top: 0;
}

.monaco-workbench.vs-dark.mac .pane-body.integrated-terminal .terminal-groups-container:not(.alt-active) .terminal:not(.enable-mouse-events),
.monaco-workbench.hc-black.mac .pane-body.integrated-terminal .terminal-groups-container:not(.alt-active) .terminal:not(.enable-mouse-events) {
	cursor: -webkit-image-set(url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAAL0lEQVQoz2NgCD3x//9/BhBYBWdhgFVAiVW4JBFKGIa4AqD0//9D3pt4I4tAdAMAHTQ/j5Zom30AAAAASUVORK5CYII=') 1x, url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAAz0lEQVRIx2NgYGBY/R8I/vx5eelX3n82IJ9FxGf6tksvf/8FiTMQAcAGQMDvSwu09abffY8QYSAScNk45G198eX//yev73/4///701eh//kZSARckrNBRvz//+8+6ZohwCzjGNjdgQxkAg7B9WADeBjIBqtJCbhRA0YNoIkBSNmaPEMoNmA0FkYNoFKhapJ6FGyAH3nauaSmPfwI0v/3OukVi0CIZ+F25KrtYcx/CTIy0e+rC7R1Z4KMICVTQQ14feVXIbR695u14+Ir4gwAAD49E54wc1kWAAAAAElFTkSuQmCC') 2x) 5 8, text;
}

/* Override default xterm style to make !important so it takes precedence over custom mac cursor */
.xterm.xterm-cursor-pointer,
.xterm .xterm-cursor-pointer {
	cursor: pointer!important;
}

.monaco-workbench .part.sidebar > .title > .title-actions .switch-terminal,
.monaco-pane-view .pane > .pane-header .monaco-action-bar .switch-terminal {
	border-width: 1px;
	border-style: solid;
}

.part.panel > .title > .title-actions .switch-terminal > .monaco-select-box {
	border-width: 1px;
	border-style: solid;
}

.monaco-workbench .part.sidebar > .title > .title-actions .switch-terminal {
	display: flex;
	align-items: center;
	font-size: 11px;
	margin-right: 0.3em;
	height: 20px;
	flex-shrink: 1;
	margin-top: 7px;
}

.monaco-workbench.mac .part.sidebar > .title > .title-actions .switch-terminal {
	border-radius: 4px;
}

.monaco-workbench .part.sidebar > .title > .title-actions .switch-terminal > .monaco-select-box {
	border: none !important;
	display: block !important;
	background-color: unset !important;
}

.monaco-pane-view .pane > .pane-header .monaco-action-bar .switch-terminal.action-item.select-container {
	border: none !important;
}

.monaco-workbench .part.sidebar > .title > .title-actions .switch-terminal > .monaco-select-box {
	padding: 0 22px 0 6px;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.monaco-workbench .terminal-overflow-guard {
	overflow: hidden;
	position: relative;
	height: 100%;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list-container {
	height: 100%;
	overflow: hidden;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container > .monaco-toolbar {
	padding: 4px 0 2px;
	margin: auto;
}
.monaco-workbench .pane-body.integrated-terminal .terminal-tabs-entry.is-active::before {
	display: block;
	position: absolute;
	content: "";
	left: 0;
	top: 0;
	bottom: 0;
	width: 1px;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container.has-text > .monaco-toolbar {
	padding: 4px 7px 2px;
	margin: 0;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container.has-text > .monaco-toolbar {
	text-align: left;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list {
	height: 100%;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .terminal-tabs-entry {
	text-align: center;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container.has-text .tabs-list .terminal-tabs-entry {
	padding-left: 10px;
	padding-right: 10px;
	text-align: left;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container.has-text .tabs-list .terminal-tabs-entry .monaco-icon-label::after {
	margin-right: 0;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container:not(.has-text) .terminal-tabs-entry .codicon {
	/* Force inherit when no text is showing as the regular icon gets replaced by the status icon */
	color: inherit;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container:not(.has-text) .monaco-icon-description-container {
	/* The description element doesn't go away once created sometimes so force hide */
	display: none;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .codicon {
	vertical-align: text-bottom;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .actions {
	display: none;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .editable-tab .monaco-icon-name-container {
	display: none;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .actions .action-label {
	padding: 2px;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-list .monaco-list-row:hover .actions,
.monaco-workbench .pane-body.integrated-terminal .tabs-list:focus-within .monaco-list-row.selected .actions,
.monaco-workbench .pane-body.integrated-terminal .tabs-list:focus-within .monaco-list-row.focused .actions {
	display: block;
}

.monaco-action-bar .action-item .single-terminal-tab {
	display: flex !important;
	align-items: center;
}

.monaco-action-bar .action-item .single-terminal-tab .codicon:first-child {
	margin-right: 4px;
}

.monaco-action-bar .action-item .single-terminal-tab .codicon:nth-child(2) {
	margin-left: 4px;
	color: inherit;
}

.monaco-workbench .pane-body.integrated-terminal .tabs-container.has-text .tabs-list .terminal-tabs-entry .uri-icon {
	background-repeat: no-repeat;
	background-size: contain;
	margin-right: 4px;
	height: 100%;
}

.monaco-workbench .terminal-uri-icon .monaco-highlighted-label .codicon,
.monaco-action-bar .terminal-uri-icon.single-terminal-tab.action-label .codicon {
	background-size: 16px;
}
.monaco-workbench .terminal-uri-icon .monaco-highlighted-label .codicon::before,
.monaco-action-bar .terminal-uri-icon.single-terminal-tab.action-label:not(.alt-command) .codicon::before {
	content: '';
	display: inline-block;
	width: 16px;
	height: 16px;
}

.monaco-workbench .pane-body.integrated-terminal .terminal-drop-overlay {
	display: block;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	pointer-events: none;
	opacity: 0; /* hidden initially */
	z-index: 34;
}
.monaco-workbench:not(.reduce-motion) .pane-body.integrated-terminal .terminal-drop-overlay {
	transition: left 70ms ease-out, right 70ms ease-out, top 70ms ease-out, bottom 70ms ease-out, opacity 150ms ease-out;
}
.monaco-workbench .pane-body.integrated-terminal .terminal-group > .monaco-split-view2.horizontal .terminal-drop-overlay.drop-before {
	right: 50%;
}
.monaco-workbench .pane-body.integrated-terminal .terminal-group > .monaco-split-view2.horizontal .terminal-drop-overlay.drop-after {
	left: 50%
}
.monaco-workbench .pane-body.integrated-terminal .terminal-group > .monaco-split-view2.vertical .terminal-drop-overlay.drop-before {
	bottom: 50%;
}
.monaco-workbench .pane-body.integrated-terminal .terminal-group > .monaco-split-view2.vertical .terminal-drop-overlay.drop-after {
	top: 50%;
}

.monaco-workbench .terminal .terminal-command-decoration:not(.default):hover {
	cursor: pointer;
}
.monaco-workbench .terminal .terminal-command-decoration:not(.default):hover::before {
	border-radius: 5px;
	background-color: var(--vscode-toolbar-hoverBackground);
}
.monaco-workbench .terminal .terminal-command-decoration {
	color: var(--vscode-terminalCommandDecoration-successBackground);
}
.monaco-workbench .terminal .terminal-command-decoration.error {
	color: var(--vscode-terminalCommandDecoration-errorBackground);
}
.monaco-workbench .terminal .terminal-command-decoration.default {
	pointer-events: none;
	color: var(--vscode-terminalCommandDecoration-defaultBackground);
}

.terminal-scroll-highlight {
	left: 0;
	right: 0;
	border-left: 5px solid #ffffff;
	border-left-width: 5px !important;
	pointer-events: none;
}

.terminal-range-highlight {
	outline: 1px solid var(--vscode-focusBorder);
	pointer-events: none;
}

.terminal-command-guide {
	left: 0;
	border: 1.5px solid #ffffff;
	width: 0px !important;
	border-color: var(--vscode-terminalCommandGuide-foreground);
	box-sizing: border-box;
	transform: translateX(3px);
	pointer-events: none;
}
.terminal-command-guide.top {
	border-top-left-radius: 1px;
	border-top-right-radius: 1px;
}
.terminal-command-guide.bottom {
	border-bottom-left-radius: 1px;
	border-bottom-right-radius: 1px;
}

.terminal-scroll-highlight-outline {
	border-left: 1px solid #ffffff;
	border-right: 1px solid #ffffff;
	pointer-events: none;
}
.terminal-scroll-highlight-outline.top {
	border-top: 1px solid #ffffff;
}
.terminal-scroll-highlight-outline.bottom {
	border-bottom: 1px solid #ffffff;
}

.terminal-scroll-highlight,
.terminal-scroll-highlight.terminal-scroll-highlight-outline {
	border-color: var(--vscode-focusBorder);
}

.monaco-workbench.hc-black .editor-instance .xterm.focus::before,
.monaco-workbench.hc-black .pane-body.integrated-terminal .xterm.focus::before,
.monaco-workbench.hc-black .editor-instance .xterm:focus::before,
.monaco-workbench.hc-black .pane-body.integrated-terminal .xterm:focus::before,
.monaco-workbench.hc-light .editor-instance .xterm.focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .xterm.focus::before,
.monaco-workbench.hc-light .editor-instance .xterm:focus::before,
.monaco-workbench.hc-light .pane-body.integrated-terminal .xterm:focus::before {
	border-color: var(--vscode-contrastActiveBorder);
}

.monaco-workbench .integrated-terminal .hoverHighlight {
	background-color: var(--vscode-terminal-hoverHighlightBackground);
}

.monaco-workbench .xterm.terminal.hide {
	visibility: hidden;
}
