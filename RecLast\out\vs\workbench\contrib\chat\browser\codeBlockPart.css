/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/


.interactive-result-code-block {
	position: relative;
}

.interactive-result-code-block .interactive-result-code-block-toolbar {
	opacity: 0;
	pointer-events: none;
}

.interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-action-bar,
.interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-toolbar {
	position: absolute;
	top: -15px;
	height: 26px;
	line-height: 26px;
	background-color: var(--vscode-interactive-result-editor-background-color, var(--vscode-editor-background));
	border: 1px solid var(--vscode-chat-requestBorder);
	z-index: 100;
	max-width: 70%;
	text-overflow: ellipsis;
	overflow: hidden;
}

.interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-action-bar {
	left: 0px
}

.interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-toolbar {
	border-radius: 3px;
	right: 10px;
}

.interactive-result-code-block .monaco-toolbar .action-item {
	height: 24px;
	width: 24px;
	margin: 1px 2px;
}

.interactive-result-code-block .monaco-toolbar .action-item .codicon {
	margin: 1px;
}

.interactive-result-code-block:hover .interactive-result-code-block-toolbar,
.interactive-result-code-block .interactive-result-code-block-toolbar:focus-within,
.interactive-result-code-block.focused .interactive-result-code-block-toolbar {
	opacity: 1;
	border-radius: 2px;
	pointer-events: auto;
}

.interactive-result-code-block .interactive-result-code-block-toolbar.force-visibility {
	opacity: 1 !important;
	pointer-events: auto !important;
}

.interactive-item-container .value .rendered-markdown [data-code] {
	margin: 0 0 16px 0;
}

.interactive-session .interactive-request .interactive-result-code-block {
	border: 1px solid var(--vscode-chat-requestCodeBorder);
}

.interactive-session .interactive-response .interactive-result-code-block {
	border: 1px solid var(--vscode-input-border, transparent);
}

.interactive-result-code-block:has(.monaco-editor.focused) {
	border-color: var(--vscode-focusBorder, transparent);
}

.interactive-result-code-block,
.interactive-result-code-block .monaco-editor,
.interactive-result-code-block .monaco-editor .overflow-guard {
	border-radius: 4px;
}

.interactive-result-code-block .interactive-result-vulns {
	font-size: 0.9em;
	padding: 0px 8px 2px 8px;
}

.interactive-result-code-block .interactive-result-vulns-header {
	display: flex;
	height: 22px;
}

.interactive-result-code-block .interactive-result-vulns-header,
.interactive-result-code-block .interactive-result-vulns-list {
	opacity: 0.8;
}

.interactive-result-code-block .interactive-result-vulns-list {
	margin: 0px;
	padding-bottom: 3px;
	padding-left: 16px !important; /* Override markdown styles */
}

.interactive-result-code-block.chat-vulnerabilities-collapsed .interactive-result-vulns-list {
	display: none;
}

.interactive-result-code-block .interactive-result-vulns-list .chat-vuln-title {
	font-weight: bold;
}

.interactive-result-code-block.no-vulns .interactive-result-vulns {
	display: none;
}

.interactive-result-code-block .interactive-result-vulns-header .monaco-button {
	/* unset Button styles */
	display: inline-flex;
	width: 100%;
	border: none;
	padding: 0;
	text-align: initial;
	justify-content: initial;
	color: var(--vscode-foreground) !important; /* This is inside .rendered-markdown */
	user-select: none;
}

.interactive-result-code-block .interactive-result-vulns-header .monaco-text-button:focus {
	outline: none;
}

.interactive-result-code-block .interactive-result-vulns-header .monaco-text-button:focus-visible {
	outline: 1px solid var(--vscode-focusBorder);
}

/* compare code block */

.interactive-result-code-block.compare.no-diff .message {
	display: inherit;
}

.interactive-result-code-block.compare .message {
	display: none;
	padding: 6px;
}


.interactive-result-code-block.compare .message A {
	color: var(--vscode-textLink-foreground);
	cursor: pointer;
}

.interactive-result-code-block.compare .message A > CODE {
	color: var(--vscode-textLink-foreground);
}

.interactive-result-code-block.compare .interactive-result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 3px;
	box-sizing: border-box;
	border-bottom: solid 1px var(--vscode-chat-requestBorder);
}

.interactive-result-code-block.compare.no-diff .interactive-result-header,
.interactive-result-code-block.compare.no-diff .interactive-result-editor {
	display: none;
}
