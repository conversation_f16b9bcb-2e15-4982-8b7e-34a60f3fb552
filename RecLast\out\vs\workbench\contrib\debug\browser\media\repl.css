/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Debug repl */

.monaco-workbench .repl {
	height: 100%;
	box-sizing: border-box;
	overflow: hidden;
}

.monaco-workbench .repl .repl-tree .monaco-tl-contents {
	user-select: text;
	-webkit-user-select: text;
	white-space: pre;
}

.monaco-workbench .repl .repl-tree .monaco-tl-contents .expression {
	font-family: var(--vscode-repl-font-family);
	font-size: var(--vscode-repl-font-size);
	line-height: var(--vscode-repl-line-height);
}

.monaco-workbench .repl .repl-tree .monaco-tl-contents .expression .lazy-button {
	cursor: pointer;
}

.monaco-workbench .repl .repl-tree .monaco-tl-twistie {
	background-position-y: calc(100% - (var(--vscode-repl-font-size-for-twistie)));
}

.monaco-workbench .repl .repl-tree.word-wrap .monaco-tl-contents {
	/* Wrap words but also do not trim whitespace #6275 */
	word-wrap: break-word;
	white-space: pre-wrap;
	/* Break on all #7533 */
	word-break: break-all;
}

.monaco-workbench .repl .repl-tree.word-wrap .monaco-tl-contents .expression.nested-variable {
	white-space: pre; /* Preserve whitespace but don't wrap */
}

.monaco-workbench .repl .repl-tree .monaco-tl-twistie.collapsible + .monaco-tl-contents,
.monaco-workbench .repl .repl-tree .monaco-tl-twistie {
	cursor: pointer;
}

.monaco-workbench .repl .repl-tree .output.expression.value-and-source {
	display: flex;
}

.monaco-workbench .repl .repl-tree .output.expression.value-and-source .label {
	margin-right: 4px;
}

.monaco-workbench .repl .repl-tree .output.expression.value-and-source .count-badge-wrapper {
	margin-right: 4px;
}

.monaco-workbench .repl .repl-tree .output.expression.value-and-source .count-badge-wrapper .monaco-count-badge {
	/* Allow the badge to be a bit shorter so it does not look cut off */
	min-height: 16px;
	word-break: keep-all;
}

.monaco-workbench .repl .repl-tree .monaco-tl-contents .arrow {
	position:absolute;
	left: 2px;
}

.monaco-workbench .repl .repl-tree .output.expression.value-and-source .source,
.monaco-workbench .repl .repl-tree .group .source {
	margin-left: auto;
	margin-right: 8px;
	cursor: pointer;
	text-decoration: underline;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: right;
	/*Use direction so the source shows elipses on the left*/
	direction: rtl;
	max-width: 400px;
}

.monaco-workbench .repl .repl-tree .output.expression > .value,
.monaco-workbench .repl .repl-tree .evaluation-result.expression > .value {
	margin-left: 0px;
}

.monaco-workbench .repl .repl-tree .output.expression .name:not(:empty) {
	margin-right: 6px;
}

.monaco-workbench .repl .repl-input-wrapper {
	display: flex;
	align-items: center;
}

/* Do not render show more in REPL suggest widget status bar */
.monaco-workbench .repl .repl-input-wrapper .suggest-status-bar .monaco-action-bar.right {
	display: none;
}

.monaco-workbench .repl .repl-input-wrapper .repl-input-chevron {
	padding: 0 6px 0 8px;
	width: 16px;
	height: 100%;
	display: flex;
	flex-shrink: 0;
	justify-content: center;
	font-weight: 600;
}

/* Output coloring  and styling */
.monaco-workbench .repl .repl-tree .output.expression > .ignore {
	font-style: italic;
}

/* ANSI Codes */
.monaco-workbench .repl .repl-tree .output.expression .code-bold	{ font-weight: bold; }
.monaco-workbench .repl .repl-tree .output.expression .code-italic	{ font-style: italic; }
.monaco-workbench .repl .repl-tree .output.expression .code-underline { text-decoration: underline;  text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-double-underline { text-decoration: underline;  text-decoration-style:double; }
.monaco-workbench .repl .repl-tree .output.expression .code-strike-through { text-decoration:line-through;  text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-overline { text-decoration:overline;  text-decoration-style:solid; }
/* because they can exist at same time we need all the possible underline(or double-underline),overline and strike-through combinations */
.monaco-workbench .repl .repl-tree .output.expression .code-overline.code-underline.code-strike-through { text-decoration: overline underline line-through; text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-overline.code-underline { text-decoration: overline underline; text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-overline.code-strike-through { text-decoration: overline line-through; text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-underline.code-strike-through { text-decoration: underline line-through; text-decoration-style:solid; }
.monaco-workbench .repl .repl-tree .output.expression .code-overline.code-double-underline.code-strike-through { text-decoration: overline underline line-through; text-decoration-style:double; }
.monaco-workbench .repl .repl-tree .output.expression .code-overline.code-double-underline { text-decoration: overline underline; text-decoration-style:double; }
.monaco-workbench .repl .repl-tree .output.expression .code-double-underline.code-strike-through { text-decoration: underline line-through; text-decoration-style:double; }
.monaco-workbench .repl .repl-tree .output.expression .code-dim	{ opacity: 0.4; }
.monaco-workbench .repl .repl-tree .output.expression .code-hidden { opacity: 0; }
.monaco-workbench .repl .repl-tree .output.expression .code-blink { animation: code-blink-key 1s cubic-bezier(1, 0, 0, 1) infinite alternate; }
.monaco-workbench .repl .repl-tree .output.expression .code-rapid-blink { animation: code-blink-key 0.3s cubic-bezier(1, 0, 0, 1) infinite alternate; }
@keyframes code-blink-key {
	to { opacity: 0.4; }
}
.monaco-workbench .repl .repl-tree .output.expression .code-subscript { vertical-align: sub; font-size: smaller; line-height: normal; }
.monaco-workbench .repl .repl-tree .output.expression .code-superscript { vertical-align: super; font-size: smaller; line-height: normal; }
