/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.chat-inline-anchor-widget {
	border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
	border-radius: 4px;
	margin: 0 1px;
	padding: 1px 3px;
	text-wrap: nowrap;
	width: fit-content;
	font-weight: normal;
	text-decoration: none;
}

.chat-inline-anchor-widget .icon-label {
	padding: 0 3px;
}

.interactive-item-container .value .rendered-markdown .chat-inline-anchor-widget {
	color: inherit;
}

.chat-inline-anchor-widget:hover {
	background-color: var(--vscode-list-hoverBackground);
}

.chat-inline-anchor-widget .icon {
	vertical-align: middle;
	line-height: 1em;
	font-size: 90% !important;
	overflow: hidden;

	top: 0 !important;
}

.show-file-icons.chat-inline-anchor-widget .icon::before {
	display: inline-block;
	line-height: 100%;
	overflow: hidden;
	font-size: 100% !important;

	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;

	padding: 0 !important;
	margin-bottom: 1px;

	flex-shrink: 0;
}

.chat-inline-anchor-widget .icon-label {
	text-wrap: wrap;
}
