export interface RecLastConfig {
    language: 'tr' | 'en';
    mode: 'chat' | 'manual' | 'agent';
    modelPath: string;
    webLookupEnabled: boolean;
}
export declare class ConfigManager {
    private config;
    constructor();
    getConfig(): RecLastConfig;
    setLanguage(language: 'tr' | 'en'): Promise<void>;
    setMode(mode: 'chat' | 'manual' | 'agent'): Promise<void>;
    setModelPath(path: string): Promise<void>;
    setWebLookupEnabled(enabled: boolean): Promise<void>;
    toggleLanguage(): Promise<void>;
    getLanguageStrings(): {
        chatMode: string;
        manualMode: string;
        agentMode: string;
        askQuestion: string;
        send: string;
        clear: string;
        createSnapshot: string;
        loading: string;
        error: string;
        success: string;
        webLookup: string;
        settings: string;
    };
}
//# sourceMappingURL=ConfigManager.d.ts.map