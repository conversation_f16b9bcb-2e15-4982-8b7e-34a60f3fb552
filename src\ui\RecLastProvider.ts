import * as vscode from 'vscode';
import { ModelManager } from '../models/ModelManager';
import { MemoryManager } from '../memory/MemoryManager';
import { RAGManager } from '../rag/RAGManager';
import { ConfigManager } from '../config/ConfigManager';

export class RecLastProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'reclast.mainView';
    
    private _view?: vscode.WebviewView;
    private currentMode: 'chat' | 'manual' | 'agent' = 'chat';

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly modelManager: ModelManager,
        private readonly memoryManager: MemoryManager,
        private readonly ragManager: RAGManager,
        private readonly configManager: ConfigManager
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this.context.extensionUri
            ]
        };

        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(
            message => this.handleMessage(message),
            undefined,
            this.context.subscriptions
        );

        // Update webview when configuration changes
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('reclast')) {
                this.updateWebview();
            }
        });
    }

    private async handleMessage(message: any) {
        switch (message.type) {
            case 'sendMessage':
                await this.handleSendMessage(message.text, message.mode);
                break;
            case 'clearChat':
                this.handleClearChat(message.mode);
                break;
            case 'changeMode':
                this.setMode(message.mode);
                break;
            case 'createSnapshot':
                await this.handleCreateSnapshot(message.name);
                break;
            case 'loadSnapshot':
                await this.handleLoadSnapshot(message.id);
                break;
            case 'getSnapshots':
                this.handleGetSnapshots();
                break;
        }
    }

    private async handleSendMessage(text: string, mode: string) {
        if (!text.trim()) {
            return;
        }

        try {
            // Add user message to memory
            this.memoryManager.addChatMessage('user', text, mode as any);

            // Send user message to webview
            this._view?.webview.postMessage({
                type: 'userMessage',
                text: text,
                mode: mode,
                timestamp: Date.now()
            });

            // Show loading state
            this._view?.webview.postMessage({
                type: 'loading',
                isLoading: true
            });

            // Get relevant context from RAG if needed
            let context = '';
            if (mode === 'chat' || mode === 'manual') {
                context = await this.ragManager.getRelevantContext(text);
            }

            // Prepare prompt with context
            let fullPrompt = text;
            if (context) {
                fullPrompt = `${context}\n\nUser Question: ${text}`;
            }

            // Get response from model
            const config = this.configManager.getConfig();
            const response = await this.modelManager.generateResponse(
                fullPrompt, 
                config.language === 'tr'
            );

            // Add assistant response to memory
            this.memoryManager.addChatMessage('assistant', response.text, mode as any);

            // Send response to webview
            this._view?.webview.postMessage({
                type: 'assistantMessage',
                text: response.text,
                mode: mode,
                timestamp: Date.now(),
                tokens: response.tokens,
                processingTime: response.processingTime
            });

        } catch (error) {
            console.error('Error handling message:', error);
            
            this._view?.webview.postMessage({
                type: 'error',
                message: `Error: ${error}`
            });
        } finally {
            // Hide loading state
            this._view?.webview.postMessage({
                type: 'loading',
                isLoading: false
            });
        }
    }

    private handleClearChat(mode: string) {
        this.memoryManager.clearChatHistory(mode as any);
        this._view?.webview.postMessage({
            type: 'chatCleared',
            mode: mode
        });
    }

    private async handleCreateSnapshot(name: string) {
        try {
            const snapshotId = await this.memoryManager.createSnapshot(name);
            this._view?.webview.postMessage({
                type: 'snapshotCreated',
                id: snapshotId,
                name: name
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'error',
                message: `Snapshot creation failed: ${error}`
            });
        }
    }

    private async handleLoadSnapshot(id: string) {
        try {
            await this.memoryManager.loadSnapshot(id);
            this.updateWebview();
            this._view?.webview.postMessage({
                type: 'snapshotLoaded',
                id: id
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'error',
                message: `Snapshot loading failed: ${error}`
            });
        }
    }

    private handleGetSnapshots() {
        const snapshots = this.memoryManager.listSnapshots();
        this._view?.webview.postMessage({
            type: 'snapshotsList',
            snapshots: snapshots
        });
    }

    public setMode(mode: 'chat' | 'manual' | 'agent') {
        this.currentMode = mode;
        this.configManager.setMode(mode);
        this.updateWebview();
    }

    public updateLanguage() {
        this.updateWebview();
    }

    private updateWebview() {
        if (this._view) {
            this._view.webview.html = this.getHtmlForWebview(this._view.webview);
        }
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        const config = this.configManager.getConfig();
        const strings = this.configManager.getLanguageStrings();
        const chatHistory = this.memoryManager.getChatHistory(this.currentMode);

        return `<!DOCTYPE html>
        <html lang="${config.language}">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>RecLast IDE</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                }
                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .mode-selector {
                    display: flex;
                    gap: 5px;
                }
                .mode-btn {
                    padding: 5px 10px;
                    border: 1px solid var(--vscode-button-border);
                    background: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    cursor: pointer;
                    border-radius: 3px;
                    font-size: 12px;
                }
                .mode-btn.active {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                .chat-container {
                    height: 400px;
                    overflow-y: auto;
                    border: 1px solid var(--vscode-panel-border);
                    padding: 10px;
                    margin-bottom: 10px;
                    border-radius: 3px;
                }
                .message {
                    margin-bottom: 15px;
                    padding: 8px;
                    border-radius: 5px;
                }
                .user-message {
                    background: var(--vscode-inputValidation-infoBorder);
                    margin-left: 20px;
                }
                .assistant-message {
                    background: var(--vscode-editor-selectionBackground);
                    margin-right: 20px;
                }
                .input-container {
                    display: flex;
                    gap: 5px;
                }
                .input-field {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 3px;
                }
                .send-btn, .clear-btn {
                    padding: 8px 15px;
                    border: none;
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    cursor: pointer;
                    border-radius: 3px;
                }
                .loading {
                    text-align: center;
                    color: var(--vscode-descriptionForeground);
                    font-style: italic;
                }
                pre {
                    background: var(--vscode-textCodeBlock-background);
                    padding: 10px;
                    border-radius: 3px;
                    overflow-x: auto;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h3>🧩 RecLast IDE</h3>
                <div class="mode-selector">
                    <button class="mode-btn ${this.currentMode === 'chat' ? 'active' : ''}" onclick="changeMode('chat')">${strings.chatMode}</button>
                    <button class="mode-btn ${this.currentMode === 'manual' ? 'active' : ''}" onclick="changeMode('manual')">${strings.manualMode}</button>
                    <button class="mode-btn ${this.currentMode === 'agent' ? 'active' : ''}" onclick="changeMode('agent')">${strings.agentMode}</button>
                </div>
            </div>
            
            <div class="chat-container" id="chatContainer">
                ${this.renderChatHistory(chatHistory)}
            </div>
            
            <div class="input-container">
                <input type="text" class="input-field" id="messageInput" placeholder="${strings.askQuestion}" onkeypress="handleKeyPress(event)">
                <button class="send-btn" onclick="sendMessage()">${strings.send}</button>
                <button class="clear-btn" onclick="clearChat()">${strings.clear}</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const text = input.value.trim();
                    if (text) {
                        vscode.postMessage({
                            type: 'sendMessage',
                            text: text,
                            mode: '${this.currentMode}'
                        });
                        input.value = '';
                    }
                }
                
                function clearChat() {
                    vscode.postMessage({
                        type: 'clearChat',
                        mode: '${this.currentMode}'
                    });
                }
                
                function changeMode(mode) {
                    vscode.postMessage({
                        type: 'changeMode',
                        mode: mode
                    });
                }
                
                function handleKeyPress(event) {
                    if (event.key === 'Enter') {
                        sendMessage();
                    }
                }
                
                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    const chatContainer = document.getElementById('chatContainer');
                    
                    switch (message.type) {
                        case 'userMessage':
                            chatContainer.innerHTML += \`<div class="message user-message"><strong>You:</strong> \${message.text}</div>\`;
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                            break;
                        case 'assistantMessage':
                            chatContainer.innerHTML += \`<div class="message assistant-message"><strong>RecLast:</strong> \${message.text}</div>\`;
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                            break;
                        case 'loading':
                            if (message.isLoading) {
                                chatContainer.innerHTML += \`<div class="loading" id="loadingMsg">${strings.loading}</div>\`;
                            } else {
                                const loadingMsg = document.getElementById('loadingMsg');
                                if (loadingMsg) loadingMsg.remove();
                            }
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                            break;
                        case 'chatCleared':
                            chatContainer.innerHTML = '';
                            break;
                        case 'error':
                            chatContainer.innerHTML += \`<div class="message assistant-message" style="color: var(--vscode-errorForeground);"><strong>Error:</strong> \${message.message}</div>\`;
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }

    private renderChatHistory(history: any[]): string {
        return history.map(msg => {
            const className = msg.role === 'user' ? 'user-message' : 'assistant-message';
            const label = msg.role === 'user' ? 'You' : 'RecLast';
            return `<div class="message ${className}"><strong>${label}:</strong> ${msg.content}</div>`;
        }).join('');
    }
}
