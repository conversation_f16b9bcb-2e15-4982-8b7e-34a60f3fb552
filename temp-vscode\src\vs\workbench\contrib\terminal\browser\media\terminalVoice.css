/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.terminal-voice {
	background-color: var(--vscode-terminal-background, var(--vscode-panel-background));
	padding: 2px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	white-space: nowrap;
	z-index: 1000;
}

.terminal-voice.codicon.codicon-mic-filled {
	display: flex;
	align-items: center;
	width: 16px;
	height: 16px;
}

.terminal-voice.recording.codicon.codicon-mic-filled {
	color: var(--vscode-activityBarBadge-background);
	animation: ani-terminal-speech 1s infinite;
}

@keyframes ani-terminal-speech {
	0% {
		color: var(--vscode-terminalCursor-background);
	}

	50% {
		color: var(--vscode-activityBarBadge-background);
	}

	100% {
		color: var(--vscode-terminalCursor-background);
	}
}

.terminal-voice-progress-text {
	font-style: italic;
	color: var(--vscode-editorGhostText-foreground) !important;
	border: 1px solid var(--vscode-editorGhostText-border);
	z-index: 1000;
}
