"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const RecLastProvider_1 = require("./ui/RecLastProvider");
const ModelManager_1 = require("./models/ModelManager");
const MemoryManager_1 = require("./memory/MemoryManager");
const RAGManager_1 = require("./rag/RAGManager");
const ConfigManager_1 = require("./config/ConfigManager");
let recLastProvider;
let modelManager;
let memoryManager;
let ragManager;
let configManager;
async function activate(context) {
    console.log('RecLast IDE is now active!');
    // Initialize managers
    configManager = new ConfigManager_1.ConfigManager();
    modelManager = new ModelManager_1.ModelManager(context);
    memoryManager = new MemoryManager_1.MemoryManager(context);
    ragManager = new RAGManager_1.RAGManager(context);
    // Initialize UI provider
    recLastProvider = new RecLastProvider_1.RecLastProvider(context, modelManager, memoryManager, ragManager, configManager);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('reclast.mainView', recLastProvider, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));
    // Register commands
    registerCommands(context);
    // Initialize AI model
    try {
        await modelManager.initialize();
        vscode.window.showInformationMessage('RecLast IDE başarıyla yüklendi! 🚀');
    }
    catch (error) {
        vscode.window.showErrorMessage(`RecLast IDE yüklenirken hata: ${error}`);
    }
}
exports.activate = activate;
function registerCommands(context) {
    // Chat mode command
    const openChatCommand = vscode.commands.registerCommand('reclast.openChat', () => {
        recLastProvider.setMode('chat');
        vscode.commands.executeCommand('reclast.mainView.focus');
    });
    // Manual mode command
    const openManualCommand = vscode.commands.registerCommand('reclast.openManual', () => {
        recLastProvider.setMode('manual');
        vscode.commands.executeCommand('reclast.mainView.focus');
    });
    // Agent mode command
    const openAgentCommand = vscode.commands.registerCommand('reclast.openAgent', () => {
        recLastProvider.setMode('agent');
        vscode.commands.executeCommand('reclast.mainView.focus');
    });
    // Language toggle command
    const toggleLanguageCommand = vscode.commands.registerCommand('reclast.toggleLanguage', () => {
        configManager.toggleLanguage();
        recLastProvider.updateLanguage();
    });
    // Create snapshot command
    const createSnapshotCommand = vscode.commands.registerCommand('reclast.createSnapshot', async () => {
        try {
            const snapshotName = await vscode.window.showInputBox({
                prompt: 'Snapshot adı girin',
                placeHolder: 'my-snapshot'
            });
            if (snapshotName) {
                await memoryManager.createSnapshot(snapshotName);
                vscode.window.showInformationMessage(`Snapshot "${snapshotName}" oluşturuldu!`);
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Snapshot oluşturulurken hata: ${error}`);
        }
    });
    // Add all commands to subscriptions
    context.subscriptions.push(openChatCommand, openManualCommand, openAgentCommand, toggleLanguageCommand, createSnapshotCommand);
}
function deactivate() {
    if (modelManager) {
        modelManager.dispose();
    }
    if (memoryManager) {
        memoryManager.dispose();
    }
    if (ragManager) {
        ragManager.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map