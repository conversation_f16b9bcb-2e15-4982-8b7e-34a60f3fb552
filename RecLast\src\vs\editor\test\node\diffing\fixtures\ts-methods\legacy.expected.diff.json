{"original": {"content": "interface Test {\n    getDecorationsInViewport(visibleRange: Range): ViewModelDecoration[];\n\tgetViewLineRenderingData(visibleRange: Range, lineNumber: number): ViewLineRenderingData;\n    getViewLineData(lineNumber: number): ViewLineData;\n}", "fileName": "./1.tst"}, "modified": {"content": "interface Test {\n    getDecorationsInViewport(visibleRange: Range): ViewModelDecoration[];\n    getViewportViewLineRenderingData(visibleRange: Range, lineNumber: number): ViewLineRenderingData;\n    getViewLineRenderingData(lineNumber: number): ViewLineRenderingData;\n    getViewLineData(lineNumber: number): ViewLineData;\n}", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[3,4)", "modifiedRange": "[3,5)", "innerChanges": [{"originalRange": "[3,1 -> 3,2]", "modifiedRange": "[3,1 -> 3,5]"}, {"originalRange": "[3,9 -> 3,9]", "modifiedRange": "[3,12 -> 3,20]"}, {"originalRange": "[3,91 -> 3,91 EOL]", "modifiedRange": "[3,102 -> 4,73 EOL]"}]}]}