{"original": {"content": "    \"@babel/types\" \"^7.22.15\"\n\n\"@babel/traverse@^7.23.9\":\n  version \"7.23.9\"\n  resolved \"https://registry.yarnpkg.com/@babel/traverse/-/traverse-7.23.7.tgz#9a7bf285c928cb99b5ead19c3b1ce5b310c9c305\"\n  integrity sha512-tY3mM8rH9jM0YHFGyfC0/xf+SB5eKUu7HPj7/k3fpi9dAlsMc5YbQvDi0Sh2QTPXqMhyaAtzAr807TIyfQrmyg==\n  dependencies:\n    \"@babel/code-frame\" \"^7.23.5\"\n    \"@babel/generator\" \"^7.23.6\"", "fileName": "./1.txt"}, "modified": {"content": "    \"@babel/types\" \"^7.22.15\"\n\n\"@babel/traverse@^7.23.9\":\n  version \"7.23.9\"\n  resolved \"https://registry.yarnpkg.com/@babel/traverse/-/traverse-7.23.7.tgz#9a7bf285c928cb99b5ead19c3b1ce5b310c9c305\"\n  integrity sha512-I/4UJ9vs90OkBtY6iiiTORVMyIhJ4kAVmsKo9KFc8UOxMeUfi2hvtIBsET5u9GizXE6/GFSuKCTNfgCswuEjRg==\n  dependencies:\n    \"@babel/code-frame\" \"^7.23.5\"\n    \"@babel/generator\" \"^7.23.6\"", "fileName": "./2.txt"}, "diffs": [{"originalRange": "[6,7)", "modifiedRange": "[6,7)", "innerChanges": [{"originalRange": "[6,20 -> 7,1]", "modifiedRange": "[6,20 -> 7,1]"}]}]}