/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
define(function() { return { data:
["AI_ClearCaptureImportanceBonus","AI_ClearImportance","AI_CreateObjective","AI_DebugAttackEncounterPositionScoringEnable","AI_DebugAttackEncounterPositionScoringIsEnabled","AI_DebugLuaEnable","AI_DebugLuaIsEnabled","AI_DebugRatingEnable","AI_DebugRatingIsEnabled","AI_DebugRenderAllTaskChildrenEnable","AI_DebugRenderAllTaskChildrenIsEnabled","AI_DebugSkirmishCaptureEnable","AI_DebugSkirmishCaptureIsEnabled","AI_DebugSkirmishCombatTargetEnable","AI_DebugSkirmishCombatTargetIsEnabled","AI_DebugSkirmishObjectiveEnable","AI_DebugSkirmishObjectiveIsEnabled","AI_DisableAllEconomyOverrides","AI_Enable","AI_EnableAll","AI_EnableEconomyOverride","AI_GetDifficulty","AI_GetPersonality","AI_GetPersonalityLuaFileName","AI_IsAIPlayer","AI_IsEnabled","AI_LockEntity","AI_LockSquad","AI_LockSquads","AI_RestoreDefaultPersonalitySettings","AI_SetCaptureImportanceBonus","AI_SetDifficulty","AI_SetImportance","AI_SetPersonality","AI_UnlockAll","AI_UnlockEntity","AI_UnlockSquad","AI_UnlockSquads","AI_UpdateStatics","AIAbilityObjective_AbilityGuidance_SetAbilityPBG","AIObjective_Cancel","AIObjective_CombatGuidance_EnableCombatGarrison","AIObjective_CombatGuidance_EnableRetaliateAttacks","AIObjective_CombatGuidance_SetRetaliateAttackTargetAreaRadius","AIObjective_DefenseGuidance_AddFacingPosition","AIObjective_DefenseGuidance_EnableIdleGarrison","AIObjective_DefenseGuidance_ResetFacingPositions","AIObjective_EngagementGuidance_EnableAggressiveEngagementMove","AIObjective_EngagementGuidance_SetAllowReturnToPreviousStages","AIObjective_EngagementGuidance_SetCoordinatedSetup","AIObjective_EngagementGuidance_SetMaxEngagementTime","AIObjective_EngagementGuidance_SetMaxIdleTime","AIObjective_FallbackGuidance_EnableRetreatOnPinned","AIObjective_FallbackGuidance_EnableRetreatOnSuppression","AIObjective_FallbackGuidance_SetEntitiesRemainingThreshold","AIObjective_FallbackGuidance_SetFallbackCapacityPercentage","AIObjective_FallbackGuidance_SetFallbackCombatRatingPercentage","AIObjective_FallbackGuidance_SetFallbackSquadHealthPercentage","AIObjective_FallbackGuidance_SetFallbackVehicleHealthPercentage","AIObjective_FallbackGuidance_SetGlobalFallbackPercentage","AIObjective_FallbackGuidance_SetGlobalFallbackRetreat","AIObjective_FallbackGuidance_SetRetreatCapacityPercentage","AIObjective_FallbackGuidance_SetRetreatCombatRatingPercentage","AIObjective_FallbackGuidance_SetRetreatHealthPercentage","AIObjective_FallbackGuidance_SetTargetPosition","AIObjective_IsValid","AIObjective_MoveGuidance_EnableAggressiveMove","AIObjective_MoveGuidance_ResetPathingLengthFactor","AIObjective_MoveGuidance_ResetSafePathingWeight","AIObjective_MoveGuidance_SetPathingLengthFactor","AIObjective_MoveGuidance_SetSafePathingWeight","AIObjective_MoveGuidance_SetSquadCoherenceRadius","AIObjective_Notify_ClearCallbacks","AIObjective_Notify_SetPlayerEventObjectiveID","AIObjective_ResourceGuidance_ClearSquads","AIObjective_ResourceGuidance_SquadGroup","AIObjective_SetName","AIObjective_TacticFilter_DisableAbility","AIObjective_TacticFilter_DisableAbilityForSquadGroup","AIObjective_TacticFilter_EnableCloseGround","AIObjective_TacticFilter_Reset","AIObjective_TacticFilter_ResetAbilityGuidance","AIObjective_TacticFilter_ResetPriority","AIObjective_TacticFilter_ResetTacticGuidance","AIObjective_TacticFilter_ResetTargetGuidance","AIObjective_TacticFilter_SetAbilityGuidance","AIObjective_TacticFilter_SetDefaultAbilityGuidance","AIObjective_TacticFilter_SetDefaultTacticGuidance","AIObjective_TacticFilter_SetDefaultTargetGuidance","AIObjective_TacticFilter_SetPriority","AIObjective_TacticFilter_SetPriorityForSquadGroup","AIObjective_TacticFilter_SetTacticGuidance","AIObjective_TacticFilter_SetTargetPolicy","AIObjective_TargetGuidance_SetTargetArea","AIObjective_TargetGuidance_SetTargetEntity","AIObjective_TargetGuidance_SetTargetLeash","AIObjective_TargetGuidance_SetTargetPathByName","AIObjective_TargetGuidance_SetTargetPathWander","AIObjective_TargetGuidance_SetTargetPosition","AIObjective_TargetGuidance_SetTargetSquad","BeginnerHint_AddOpportunity","BeginnerHint_RemoveAllOpportunities","BeginnerHint_RemoveOpportunity","BP_GetAbilityBlueprint","BP_GetCamouflageStanceBlueprint","BP_GetCriticalBlueprint","BP_GetEntityBlueprint","BP_GetID","BP_GetMoveTypeBlueprint","BP_GetName","BP_GetPropertyBagGroupCount","BP_GetPropertyBagGroupPathName","BP_GetSlotItemBlueprint","BP_GetSquadBlueprint","BP_GetUpgradeBlueprint","BP_GetWeaponBlueprint","EBP_Exists","SBP_Exists","Camera_CyclePositions","Camera_Follow","Camera_MoveTo","Camera_MoveToIfClose","Camera_SetDefault","Cmd_AbandonTeamWeapon","Cmd_Ability","Cmd_AttachSquads","Cmd_Attack","Cmd_AttackMove","Cmd_AttackMoveThenCapture","Cmd_CaptureTeamWeapon","Cmd_Construct","Cmd_CriticalHit","Cmd_DetonateDemolitions","Cmd_EjectOccupants","Cmd_Garrison","Cmd_InstantReinforceUnit","Cmd_InstantReinforceUnitPos","Cmd_InstantSetupTeamWeapon","Cmd_InstantUpgrade","Cmd_Move","Cmd_MoveAwayFromPos","Cmd_MoveToAndDespawn","Cmd_MoveToClosestMarker","Cmd_MoveToThenCapture","Cmd_RecrewVehicle","Cmd_ReinforceUnit","Cmd_ReinforceUnitPos","Cmd_Retreat","Cmd_RevertOccupiedBuilding","Cmd_SetDemolitions","Cmd_SquadCamouflageStance","Cmd_SquadPath","Cmd_SquadPatrolMarker","Cmd_StaggeredRetreat","Cmd_Stop","Cmd_Surrender","Cmd_UngarrisonSquad","Cmd_Upgrade","Command_Entity","Command_EntityAbility","Command_EntityBuildSquad","Command_EntityEntity","Command_EntityExt","Command_EntityPos","Command_EntityPosAbility","Command_EntityPosDirAbility","Command_EntityPosSquad","Command_EntitySquad","Command_EntityTargetEntityAbility","Command_EntityTargetSquadAbility","Command_EntityUpgrade","Command_Player","Command_PlayerAbility","Command_PlayerEntity","Command_PlayerEntityCriticalHit","Command_PlayerExt","Command_PlayerPos","Command_PlayerPosAbility","Command_PlayerPosDirAbility","Command_PlayerPosExt","Command_PlayerSquadConstructBuilding","Command_PlayerSquadConstructFence","Command_PlayerSquadConstructField","Command_PlayerSquadCriticalHit","Command_PlayerUpgrade","Command_Squad","Command_SquadAbility","Command_SquadAttackMovePos","Command_SquadDoCustomPlan","Command_SquadDoCustomPlanTarget","Command_SquadEntity","Command_SquadEntityAbility","Command_SquadEntityAttack","Command_SquadEntityBool","Command_SquadEntityExt","Command_SquadEntityLoad","Command_SquadExt","Command_SquadMovePos","Command_SquadMovePosFacing","Command_SquadPos","Command_SquadPosAbility","Command_SquadPosExt","Command_SquadPositionAttack","Command_SquadSquad","Command_SquadSquadAbility","Command_SquadSquadAttack","Command_SquadSquadExt","Command_SquadSquadLoad","Command_SquadUpgrade","AutoCinematic","AutoReinforce_AddSGroup","AutoReinforce_RemoveAll","AutoReinforce_RemoveSGroup","AutoRetreat_AddSGroup","AutoRetreat_RemoveAll","AutoRetreat_RemoveSGroup","BridgeTerritory_Add","Ceasefire_AddSGroup","Ceasefire_RemoveSGroup","FireTargettingArtillery","Game_DefaultGameRestore","Game_GetGameRestoreCallbackExists","Game_RemoveGameRestoreCallback","Game_SetGameRestoreCallback","Resources_Disable","Resources_Enable","ShootTheSky_AddSyncWeapon","ShootTheSky_RemoveAll","ShootTheSky_RemoveSyncWeapon","SmokeEntrance_Do","Table_Contains","Table_Copy","Table_GetRandomItem","TeamWeapon_AddGroup","TeamWeapon_RemoveDirections","TeamWeapon_RemoveGroup","EGroup_Add","EGroup_AddEGroup","EGroup_CanSeeEGroup","EGroup_CanSeeSGroup","EGroup_Clear","EGroup_Compare","EGroup_ContainsBlueprints","EGroup_ContainsEGroup","EGroup_ContainsEntity","EGroup_Count","EGroup_CountAlive","EGroup_CountDeSpawned","EGroup_CountSpawned","EGroup_Create","EGroup_CreateIfNotFound","EGroup_CreateKickerMessage","EGroup_DeSpawn","EGroup_Destroy","EGroup_DestroyAllEntities","EGroup_Duplicate","EGroup_EnableMinimapIndicator","EGroup_EnableUIDecorator","EGroup_Exists","EGroup_Filter","EGroup_FilterUnderConstruction","EGroup_ForEach","EGroup_ForEachAllOrAny","EGroup_ForEachAllOrAnyEx","EGroup_ForEachEx","EGroup_FromName","EGroup_GetAvgHealth","EGroup_GetDeSpawnedEntityAt","EGroup_GetInvulnerable","EGroup_GetLastAttacker","EGroup_GetName","EGroup_GetOffsetPosition","EGroup_GetPosition","EGroup_GetRandomSpawnedEntity","EGroup_GetSequence","EGroup_GetSpawnedEntityAt","EGroup_GetSpawnedEntityFilter","EGroup_GetSpread","EGroup_GetSquadsHeld","EGroup_HasUpgrade","EGroup_Hide","EGroup_InstantCaptureStrategicPoint","EGroup_InstantRevertOccupiedBuilding","EGroup_Intersection","EGroup_IsBurning","EGroup_IsCapturedByPlayer","EGroup_IsCapturedByTeam","EGroup_IsDoingAttack","EGroup_IsEmpty","EGroup_IsHoldingAny","EGroup_IsInCover","EGroup_IsMoving","EGroup_IsOnScreen","EGroup_IsProducingSquads","EGroup_IsSpawned","EGroup_IsUnderAttack","EGroup_IsUnderAttackByPlayer","EGroup_IsUnderAttackFromDirection","EGroup_IsUsingAbility","EGroup_Kill","EGroup_NotifyOnPlayerDemolition","EGroup_Remove","EGroup_RemoveDemolitions","EGroup_RemoveGroup","EGroup_RemoveUpgrade","EGroup_ReSpawn","EGroup_SetAnimatorAction","EGroup_SetAnimatorEvent","EGroup_SetAnimatorState","EGroup_SetAnimatorVariable","EGroup_SetAutoTargetting","EGroup_SetAvgHealth","EGroup_SetCrushable","EGroup_SetDemolitions","EGroup_SetHealthMinCap","EGroup_SetInvulnerable","EGroup_SetPlayerOwner","EGroup_SetRallyPoint","EGroup_SetRecrewable","EGroup_SetSelectable","EGroup_SetSharedProductionQueue","EGroup_SetStrategicPointNeutral","EGroup_SetWorldOwned","EGroup_Single","SGroup_HasEntityUpgrade","Ai\\:GetEncountersBySGroup","Ai\\:GetEncountersBySquad","AI_DisableAllEncounters","AI_EnableAllEncounters","AI_GetActiveEncounters","AI_GetNumEncounters","AI_IsMatchingDifficulty","AI_OverrideDifficulty","AI_RemoveAllEncounters","AI_SetDebugLevel","AI_SetStaggeredSpawnDelay","AI_ToggleDebugData","AI_ToggleDebugPrint","AIAbilityGoal_AdjustDefaultGoalData","AIAbilityGoal_SetDefaultGoalData","AIAbilityGoal_SetModifyGoalData","AIAbilityGoal_SetOverrideGoalData","AIAttackGoal_AdjustDefaultGoalData","AIAttackGoal_SetDefaultGoalData","AIAttackGoal_SetModifyGoalData","AIAttackGoal_SetOverrideGoalData","AIBaseGoal_AdjustDefaultGoalData","AIBaseGoal_SetDefaultGoalData","AIBaseGoal_SetModifyGoalData","AIBaseGoal_SetOverrideGoalData","AIDefendGoal_AdjustDefaultGoalData","AIDefendGoal_SetDefaultGoalData","AIDefendGoal_SetModifyGoalData","AIDefendGoal_SetOverrideGoalData","AIMoveGoal_AdjustDefaultGoalData","AIMoveGoal_SetDefaultGoalData","AIMoveGoal_SetModifyGoalData","AIMoveGoal_SetOverrideGoalData","Encounter\\:AddSgroup","Encounter\\:ClearGoal","Encounter\\:ConvertSgroup","Encounter\\:Create","Encounter\\:CreateAbility","Encounter\\:CreateAttack","Encounter\\:CreateBasic","Encounter\\:CreateDefend","Encounter\\:CreateMove","Encounter\\:CreatePatrol","Encounter\\:Disable","Encounter\\:Enable","Encounter\\:GetGoalData","Encounter\\:GetSgroup","Encounter\\:RemoveOnDeath","Encounter\\:RestartGoal","Encounter\\:SetGoal","Encounter\\:SetGoalOnSuccess","Encounter\\:SetOnDeath","Encounter\\:Spawn","Encounter\\:UpdateGoal","MergeClone","Entity_ApplyCritical","Entity_BuildingPanelInfo","Entity_CanAttackNow","Entity_CancelProductionQueueItem","Entity_CanLoadSquad","Entity_CanLoadSquadAndAttackCurrentTarget","Entity_CanSeeEntity","Entity_CanSeeSquad","Entity_ClearPostureSuggestion","Entity_ClearTagDebug","Entity_CompleteUpgrade","Entity_Create","Entity_CreateENV","Entity_DeSpawn","Entity_Destroy","Entity_DisableBuildingDeath","Entity_DoBuildingDamageRay","Entity_EnableAttention","Entity_EnableProductionQueue","Entity_EnableStrategicPoint","Entity_ForceConstruct","Entity_FromWorldID","Entity_GetActiveCommand","Entity_GetBlueprint","Entity_GetBuildingProgress","Entity_GetCoverValue","Entity_GetGameID","Entity_GetHeading","Entity_GetHealth","Entity_GetHealthMax","Entity_GetHealthPercentage","Entity_GetInvulnerable","Entity_GetInvulnerableMinCap","Entity_GetInvulnerableToCritical","Entity_GetLastAttacker","Entity_GetLastAttackers","Entity_GetMaxCaptureCrewSize","Entity_GetOffsetPosition","Entity_GetPlayerOwner","Entity_GetPosition","Entity_GetProductionQueueItem","Entity_GetProductionQueueItemType","Entity_GetProductionQueueSize","Entity_GetResourceType","Entity_GetSightInnerHeight","Entity_GetSightInnerRadius","Entity_GetSightOuterHeight","Entity_GetSightOuterRadius","Entity_GetSquad","Entity_GetSquadsHeld","Entity_GetTotalPanelCount","Entity_GetUndestroyedPanelCount","Entity_GetWeaponBlueprint","Entity_GetWeaponHardpointCount","Entity_HasAnyCritical","Entity_HasCritical","Entity_HasProductionQueue","Entity_HasUpgrade","Entity_InstantCaptureStrategicPoint","Entity_InstantRevertOccupiedBuilding","Entity_IsAlive","Entity_IsAttacking","Entity_IsBuilding","Entity_IsBurning","Entity_IsCamouflaged","Entity_IsCapturableBuilding","Entity_IsCasualty","Entity_IsCuttable","Entity_IsDemolitionReady","Entity_IsEBPBuilding","Entity_IsEBPObjCover","Entity_IsHardpointActive","Entity_IsHoldingAny","Entity_IsInCover","Entity_IsMoving","Entity_IsOfType","Entity_IsPartOfSquad","Entity_IsPlane","Entity_IsSlotItem","Entity_IsSoldier","Entity_IsSpawned","Entity_IsStartingPosition","Entity_IsStrategicPoint","Entity_IsStrategicPointCapturedBy","Entity_IsSyncWeapon","Entity_IsUnderAttack","Entity_IsUnderAttackByPlayer","Entity_IsUnderAttackFromDirection","Entity_IsValid","Entity_IsVaultable","Entity_IsVehicle","Entity_IsVictoryPoint","Entity_Kill","Entity_NotifyOnPlayerDemolition","Entity_RemoveBoobyTraps","Entity_RemoveCritical","Entity_RemoveDemolitions","Entity_RemoveUpgrade","Entity_SetAnimatorAction","Entity_SetAnimatorActionParameter","Entity_SetAnimatorEvent","Entity_SetAnimatorState","Entity_SetAnimatorVariable","Entity_SetBuildingVisualFireState","Entity_SetCrushable","Entity_SetCrushMode","Entity_SetDemolitions","Entity_SetEnableCasualty","Entity_SetHeading","Entity_SetHealth","Entity_SetInvulnerable","Entity_SetInvulnerableMinCap","Entity_SetInvulnerableToCritical","Entity_SetOnFire","Entity_SetPlayerOwner","Entity_SetPosition","Entity_SetProjectileCanExplode","Entity_SetRecrewable","Entity_SetSharedProductionQueue","Entity_SetStrategicPointNeutral","Entity_SetWorldOwned","Entity_SimHide","Entity_Spawn","Entity_StopAbility","Entity_SuggestPosture","Entity_SupportsDemolition","Entity_TagDebug","Entity_VisHide","Misc_DoWeaponHitEffectOnPosition","Misc_GetTerrainHeight","Misc_ToggleEntities","ModMisc_MakeCasualtyAction","ModMisc_MakeWreckAction","ModMisc_OOCAction","UI_EnableEntityDecorator","UI_EnableEntityMinimapIndicator","UI_EnableEntitySelectionVisuals","UI_EnableSquadDecorator","UI_EnableSquadMinimapIndicator","UI_GetAbilityIconName","Event_CreateAND","Event_CreateOR","Event_ElementOnScreen","Event_EncounterIsDead","Event_Exists","Event_GroupBurning","Event_GroupIsDead","Event_GroupIsNotPinned","Event_GroupIsNotSuppressed","Event_GroupIsPinned","Event_GroupIsSuppressed","Event_GroupLeftAlive","Event_IsDoingAttack","Event_IsEngaged","Event_IsHoldingAny","Event_IsInHold","Event_IsSelected","Event_IsUnderAttack","Event_NarrativeEventsNotRunning","Event_NarrativeEventsRunning","Event_OnHealth","Event_PlayerBuildingCount","Event_PlayerCanNotSeeElement","Event_PlayerCanSeeElement","Event_PlayerDoesntOwnTerritory","Event_PlayerOwnsElement","Event_PlayerOwnsTerritory","Event_PlayerResourceLevel","Event_PlayerSquadCount","Event_Proximity","Event_Remove","Event_RemoveAll","Event_TeamBuildingCount","Event_TeamCanNotSeeElement","Event_TeamCanSeeElement","Event_TeamDoesntOwnTerritory","Event_TeamOwnsElement","Event_TeamOwnsTerritory","Event_TeamResourceLevel","Event_TeamSquadCount","Event_Timer","Event_ToggleDebug","Event_View","EventHandler_AssignEncounterGoal","EventHandler_ObjectiveComplete","EventHandler_ObjectiveStart","EventHandler_RemoveHint","EventHandler_RemoveMinimapBlip","EventHandler_RemoveObjectiveUI","EventHandler_Retreat","EventHandler_StaggeredRetreat","EventHandler_StartIntel","EventHandler_StartNislet","EventHandler_StopFlashing","FOW_PlayerExploreAll","FOW_PlayerRevealAll","FOW_PlayerRevealArea","FOW_PlayerUnExploreAll","FOW_PlayerUnRevealAll","FOW_PlayerUnRevealArea","FOW_RevealAll","FOW_RevealArea","FOW_RevealEGroup","FOW_RevealEGroupOnly","FOW_RevealEntity","FOW_RevealMarker","FOW_RevealSGroup","FOW_RevealSGroupOnly","FOW_RevealSquad","FOW_RevealTerritory","FOW_UnRevealAll","FOW_UnRevealArea","FOW_UnRevealMarker","FOW_UnRevealTerritory","EGroup_CreateTable","EGroup_GetWBTable","Marker_GetNonSequentialTable","Marker_GetTable","SGroup_CreateTable","SGroup_GetWBTable","Marker_DoesNumberAttributeExist","Marker_DoesStringAttributeExist","Marker_Exists","Marker_FromName","Marker_GetDirection","Marker_GetName","Marker_GetNumberAttribute","Marker_GetPosition","Marker_GetProximityRadius","Marker_GetProximityType","Marker_GetSequence","Marker_GetStringAttribute","Marker_GetType","Marker_InProximity","Modifier_IsEnabledOnEGroup","Modifier_Remove","Modifier_RemoveAllFromEGroup","Modifier_RemoveAllFromSGroup","Modify_AbilityDelayTime","Modify_AbilityDurationTime","Modify_AbilityManpowerCost","Modify_AbilityMaxCastRange","Modify_AbilityMinCastRange","Modify_AbilityMunitionsCost","Modify_AbilityRechargeTime","Modify_Armor","Modify_CaptureTime","Modify_DisableHold","Modify_Enable_ParadropReinforcements","Modify_EntityBuildTime","Modify_EntityCost","Modify_PlayerExperienceReceived","Modify_PlayerProductionRate","Modify_PlayerResourceCap","Modify_PlayerResourceGift","Modify_PlayerResourceRate","Modify_PlayerSightRadius","Modify_ProductionRate","Modify_ProjectileDelayTime","Modify_ReceivedAccuracy","Modify_ReceivedDamage","Modify_ReceivedSuppression","Modify_SetUpgradeCost","Modify_SightRadius","Modify_SquadAvailability","Modify_SquadCaptureRate","Modify_SquadTypeSightRadius","Modify_TargetPriority","Modify_TeamWeapon","Modify_TerritoryRadius","Modify_UnitSpeed","Modify_UnitVeterancyValue","Modify_UpgradeBuildTime","Modify_Upkeep","Modify_VehicleRepairRate","Modify_VehicleRotationSpeed","Modify_VehicleTurretRotationSpeed","Modify_Vulnerability","Modify_WeaponAccuracy","Modify_WeaponBurstLength","Modify_WeaponBurstRateOfFire","Modify_WeaponCooldown","Modify_WeaponDamage","Modify_WeaponEnabled","Modify_WeaponPenetration","Modify_WeaponRange","Modify_WeaponReload","Modify_WeaponScatter","Modify_WeaponSuppression","MP_BlizzardInit","Objective_AddPing","Objective_AddUIElements","Objective_AreAllPrimaryObjectivesComplete","Objective_Complete","Objective_Fail","Objective_GetCounter","Objective_GetTimerSeconds","Objective_IncreaseCounter","Objective_IsComplete","Objective_IsCounterSet","Objective_IsFailed","Objective_IsStarted","Objective_IsTimerSet","Objective_IsVisible","Objective_PauseTimer","Objective_Register","Objective_RemovePing","Objective_RemoveUIElements","Objective_ResumeTimer","Objective_SetAlwaysShowDetails","Objective_SetCounter","Objective_Show","Objective_Start","Objective_StartTimer","Objective_StopCounter","Objective_StopTimer","Objective_TogglePings","Objective_UpdateText","Cmd_StopSquadsOnly","OpGameSetup","OpNPC_AddSupportGroup","OpNPC_AddSyncWpnGroup","OpNPC_AddTeamWpnGroup","OpNPC_IsGroupActive","OpNPC_Name","OpNPC_RemoveGroup","OpNPC_RetreatGroup","OpNPC_SetGroupActive","OpPlayer_Action","OpUtil_AddModifier","OpUtil_AddResourcesToTeam","OpUtil_AssignSquadSameTypeControlGroup","OpUtil_AssignSquadUnusedControlGroup","OpUtil_ClearPlayZone","OpUtil_EgroupIsCapturedByTeam","OpUtil_EnemyEGroupArrowManager","OpUtil_FindNearestCapturePoint","OpUtil_InvulnerableAdd","OpUtil_InvulnerableRemove","OpUtil_LogSyncWpn","OpUtil_ReturnEnemyNPC","OpUtil_ReturnHumanPlayer","OpUtil_ReturnNPCPlayer","OpUtil_ReturnRace","OpUtil_ReturnTeam","OpUtil_SetPlayZone","OpUtil_TeamOwnsEntity","OpVP_AddPenaltyGroup","OpVP_Name","OpVP_RegisterCaptureablePoints","OpVP_RegisterPointDefense","OpVP_RemoveGroup","UI_PopUpMessage","Util_ProductionRestriction","Util_TutorialIntel","Player_AddAbility","Player_AddAbilityLockoutZone","Player_AddResource","Player_AddSquadsToSGroup","Player_AddUnspentCommandPoints","Player_AreSquadsNearMarker","Player_CanCastAbilityOnEntity","Player_CanCastAbilityOnPlayer","Player_CanCastAbilityOnPosition","Player_CanCastAbilityOnSquad","Player_CanSeeEGroup","Player_CanSeeEntity","Player_CanSeePosition","Player_CanSeeSGroup","Player_CanSeeSquad","Player_ClearArea","Player_ClearAvailabilities","Player_ClearPopCapOverride","Player_CompleteUpgrade","Player_DoParadrop","Player_FindFirstEnemyPlayer","Player_FromId","Player_GetAIType","Player_GetAll","Player_GetAllEntitiesNearMarker","Player_GetAllSquadsNearMarker","Player_GetBuildingID","Player_GetBuildingsCount","Player_GetBuildingsCountExcept","Player_GetBuildingsCountOnly","Player_GetCurrentPopulation","Player_GetDisplayName","Player_GetEntities","Player_GetEntitiesFromType","Player_GetEntityConcentration","Player_GetEntityCount","Player_GetEntityName","Player_GetID","Player_GetMaxPopulation","Player_GetNumStrategicPoints","Player_GetNumVictoryPoints","Player_GetPopulationPercentage","Player_GetRace","Player_GetRaceName","Player_GetRelationship","Player_GetResource","Player_GetResourceRate","Player_GetSquadConcentration","Player_GetSquadCount","Player_GetSquads","Player_GetStartingPosition","Player_GetStrategicPointCaptureProgress","Player_GetTeam","Player_GetUnitCount","Player_GetUpgradeCost","Player_HasAbility","Player_HasBuilding","Player_HasBuildingsExcept","Player_HasBuildingUnderConstruction","Player_HasCapturingSquadNearStrategicPoint","Player_HasLost","Player_HasMapEntryPosition","Player_HasUpgrade","Player_IsAlive","Player_IsAllied","Player_IsHuman","Player_NumUpgradeComplete","Player_OwnsEGroup","Player_OwnsEntity","Player_OwnsSGroup","Player_OwnsSquad","Player_RemoveAbilityLockoutZone","Player_RemoveUpgrade","Player_ResetResource","Player_RestrictAddOnList","Player_RestrictBuildingList","Player_RestrictResearchList","Player_SetAbilityAvailability","Player_SetAllCommandAvailabilityInternal","Player_SetCommandAvailability","Player_SetConstructionMenuAvailability","Player_SetDefaultSquadMoodMode","Player_SetEntityProductionAvailability","Player_SetHeatGainRate","Player_SetHeatLossRate","Player_SetMaxCapPopulation","Player_SetMaxPopulation","Player_SetPopCapOverride","Player_SetResource","Player_SetSquadProductionAvailability","Player_SetUpgradeAvailability","Player_SetUpgradeCost","Player_SpawnGlider","Player_StopAbility","Player_StopEarningActionPoints","Player_Triangulate","Actor_Clear","Actor_PlaySpeech","Actor_PlaySpeechWithoutPortrait","Actor_SetFromSGroup","Actor_SetFromSquad","Prox_AreEntitiesNearMarker","Prox_ArePlayerMembersNearMarker","Prox_ArePlayersNearMarker","Prox_AreSquadMembersNearMarker","Prox_AreSquadsNearMarker","Prox_AreTeamsNearMarker","Prox_EGroupEGroup","Prox_EGroupSGroup","Prox_EntitiesInProximityOfEntities","Prox_GetRandomPosition","Prox_MarkerEGroup","Prox_MarkerSGroup","Prox_PlayerEntitiesInProximityOfEntities","Prox_PlayerEntitiesInProximityOfPlayerSquads","Prox_PlayerEntitiesInProximityOfSquads","Prox_PlayerSquadsInProximityOfEntities","Prox_PlayerSquadsInProximityOfPlayerEntities","Prox_PlayerSquadsInProximityOfPlayerSquads","Prox_PlayerSquadsInProximityOfSquads","Prox_SGroupSGroup","Prox_SquadsInProximityOfEntities","Prox_SquadsInProximityOfSquads","Rule_Add","Rule_AddDelayedInterval","Rule_AddDelayedIntervalEx","Rule_AddEGroupEvent","Rule_AddEntityEvent","Rule_AddGlobalEvent","Rule_AddInterval","Rule_AddIntervalEx","Rule_AddOneShot","Rule_AddPlayerEvent","Rule_AddSGroupEvent","Rule_AddSquadEvent","Rule_ChangeInterval","Rule_Exists","Rule_Remove","Rule_RemoveAll","Rule_RemoveEGroupEvent","Rule_RemoveEntityEvent","Rule_RemoveGlobalEvent","Rule_RemoveIfExist","Rule_RemoveMe","Rule_RemovePlayerEvent","Rule_RemoveSGroupEvent","Rule_RemoveSquadEvent","Setup_Player","Cmd_StopSquadsExcept","Misc_IsEGroupOnScreen","Misc_IsSGroupOnScreen","SGroup_Add","SGroup_AddAbility","SGroup_AddGroup","SGroup_AddGroups","SGroup_AddLeaders","SGroup_AddSlotItemToDropOnDeath","SGroup_CanCastAbilityOnEntity","SGroup_CanCastAbilityOnPosition","SGroup_CanCastAbilityOnSquad","SGroup_CanInstantReinforceNow","SGroup_CanSeeSGroup","SGroup_Clear","SGroup_ClearPostureSuggestion","SGroup_Compare","SGroup_CompleteEntityUpgrade","SGroup_ContainsBlueprints","SGroup_ContainsSGroup","SGroup_ContainsSquad","SGroup_Count","SGroup_CountDeSpawned","SGroup_CountSpawned","SGroup_Create","SGroup_CreateIfNotFound","SGroup_CreateKickerMessage","SGroup_DeSpawn","SGroup_Destroy","SGroup_DestroyAllInMarker","SGroup_DestroyAllSquads","SGroup_DisableCombatPlans","SGroup_Duplicate","SGroup_EnableAttention","SGroup_EnableMinimapIndicator","SGroup_EnableSurprise","SGroup_EnableUIDecorator","SGroup_Exists","SGroup_FaceEachOther","SGroup_FaceMarker","SGroup_Filter","SGroup_FilterCount","SGroup_FilterThreat","SGroup_ForEach","SGroup_ForEachAllOrAny","SGroup_ForEachAllOrAnyEx","SGroup_ForEachEx","SGroup_FromName","SGroup_GetAvgHealth","SGroup_GetAvgLoadout","SGroup_GetDeSpawnedSquadAt","SGroup_GetGarrisonedBuildingEntity","SGroup_GetHoldEGroup","SGroup_GetHoldSGroup","SGroup_GetInvulnerable","SGroup_GetLastAttacker","SGroup_GetLoadedVehicleSquad","SGroup_GetName","SGroup_GetNumSlotItem","SGroup_GetOffsetPosition","SGroup_GetPosition","SGroup_GetRandomSpawnedSquad","SGroup_GetSequence","SGroup_GetSpawnedSquadAt","SGroup_GetSpread","SGroup_GetSquadsHeld","SGroup_GetSuppression","SGroup_GetVeterancyExperience","SGroup_GetVeterancyRank","SGroup_HasCritical","SGroup_HasLeader","SGroup_HasSquadBlueprint","SGroup_HasTeamWeapon","SGroup_HasUpgrade","SGroup_Hide","SGroup_IncreaseVeterancyExperience","SGroup_IncreaseVeterancyRank","SGroup_Intersection","SGroup_IsAlive","SGroup_IsAttackMoving","SGroup_IsCamouflaged","SGroup_IsCapturing","SGroup_IsConstructingBuilding","SGroup_IsDoingAbility","SGroup_IsDoingAttack","SGroup_IsDugIn","SGroup_IsEmpty","SGroup_IsFemale","SGroup_IsHoldingAny","SGroup_IsIdle","SGroup_IsInCover","SGroup_IsInfiltrated","SGroup_IsInHoldEntity","SGroup_IsInHoldSquad","SGroup_IsMoving","SGroup_IsOnScreen","SGroup_IsPinned","SGroup_IsReinforcing","SGroup_IsRetreating","SGroup_IsSettingDemolitions","SGroup_IsSuppressed","SGroup_IsUnderAttack","SGroup_IsUnderAttackByPlayer","SGroup_IsUnderAttackFromDirection","SGroup_IsUpgrading","SGroup_IsUsingAbility","SGroup_Kill","SGroup_Remove","SGroup_RemoveGroup","SGroup_RemoveUpgrade","SGroup_ReSpawn","SGroup_RestoreCombatPlans","SGroup_RewardActionPoints","SGroup_SetAnimatorState","SGroup_SetAutoTargetting","SGroup_SetAvgHealth","SGroup_SetAvgMorale","SGroup_SetCrushable","SGroup_SetInvulnerable","SGroup_SetInvulnerableToCritical","SGroup_SetMoodMode","SGroup_SetMoveType","SGroup_SetPlayerOwner","SGroup_SetRecrewable","SGroup_SetSelectable","SGroup_SetSharedProductionQueue","SGroup_SetSuppression","SGroup_SetTeamWeaponCapturable","SGroup_SetVeterancyDisplayVisibility","SGroup_SetWorldOwned","SGroup_Single","SGroup_SnapFaceEachOther","SGroup_SuggestPosture","SGroup_TotalMembersCount","SGroup_WarpToMarker","SGroup_WarpToPos","Util_Grab","SGroup_FacePosition","SGroup_SnapFacePosition","Squad_AddAbility","Squad_AddSlotItemToDropOnDeath","Squad_CanCaptureStrategicPoint","Squad_CanCaptureTeamWeapon","Squad_CanCastAbilityOnEGroup","Squad_CanCastAbilityOnEntity","Squad_CanCastAbilityOnPosition","Squad_CanCastAbilityOnSGroup","Squad_CanCastAbilityOnSquad","Squad_CancelProductionQueueItem","Squad_CanHold","Squad_CanInstantReinforceNow","Squad_CanLoadSquad","Squad_CanPickupSlotItem","Squad_CanRecrew","Squad_CanSeeEntity","Squad_CanSeeSquad","Squad_ClearPostureSuggestion","Squad_CompleteUpgrade","Squad_Count","Squad_CreateAndSpawnToward","Squad_DeSpawn","Squad_Destroy","Squad_EnableProductionQueue","Squad_EnableSurprise","Squad_EntityAt","Squad_FacePosition","Squad_FaceSquad","Squad_FindCover","Squad_FindCoverCompareCurrent","Squad_FromWorldID","Squad_GetActiveCommand","Squad_GetAttackPlan","Squad_GetAttackTargets","Squad_GetBlueprint","Squad_GetDestination","Squad_GetGameID","Squad_GetHeading","Squad_GetHealth","Squad_GetHealthMax","Squad_GetHealthPercentage","Squad_GetHoldEntity","Squad_GetHoldSquad","Squad_GetInvulnerable","Squad_GetInvulnerableEntityCount","Squad_GetInvulnerableMinCap","Squad_GetLastAttacker","Squad_GetLastAttackers","Squad_GetLastEntityAttacker","Squad_GetMax","Squad_GetNumSlotItem","Squad_GetOffsetPosition","Squad_GetPinnedPlan","Squad_GetPlayerOwner","Squad_GetPosition","Squad_GetPositionDeSpawned","Squad_GetProductionQueueItem","Squad_GetProductionQueueItemType","Squad_GetProductionQueueSize","Squad_GetReactionPlan","Squad_GetRetaliationPlan","Squad_GetSlotItemAt","Squad_GetSlotItemCount","Squad_GetSlotItemsTable","Squad_GetSquadsHeld","Squad_GetSuppression","Squad_GetVeterancyExperience","Squad_GetVeterancyRank","Squad_GiveSlotItem","Squad_GiveSlotItemsFromTable","Squad_HasActiveCommand","Squad_HasAnyCritical","Squad_HasCritical","Squad_HasDestination","Squad_HasProductionQueue","Squad_HasSlotItem","Squad_HasTeamWeapon","Squad_HasUpgrade","Squad_IncreaseVeterancyExperience","Squad_IncreaseVeterancyRank","Squad_InstantSetupTeamWeapon","Squad_IsAttacking","Squad_IsCamouflaged","Squad_IsDoingAbility","Squad_IsFemale","Squad_IsHoldingAny","Squad_IsInCover","Squad_IsInHoldEntity","Squad_IsInHoldSquad","Squad_IsMoving","Squad_IsPinned","Squad_IsReinforcing","Squad_IsRetreating","Squad_IsSuppressed","Squad_IsUnderAttack","Squad_IsUnderAttackByPlayer","Squad_IsUnderAttackFromDirection","Squad_IsUpgrading","Squad_IsUpgradingAny","Squad_IsValid","Squad_Kill","Squad_RemoveAbility","Squad_RemoveUpgrade","Squad_RewardActionPoints","Squad_SetAnimatorState","Squad_SetAttackPlan","Squad_SetHealth","Squad_SetInvulnerable","Squad_SetInvulnerableEntityCount","Squad_SetInvulnerableMinCap","Squad_SetInvulnerableToCritical","Squad_SetMoodMode","Squad_SetMoveType","Squad_SetPinnedPlan","Squad_SetPlayerOwner","Squad_SetPosition","Squad_SetReactionPlan","Squad_SetRecrewable","Squad_SetRetaliationPlan","Squad_SetSharedProductionQueue","Squad_SetSuppression","Squad_SetVeterancyDisplayVisibility","Squad_SetWorldOwned","Squad_Spawn","Squad_SpawnToward","Squad_Split","Squad_StopAbility","Squad_SuggestPosture","Squad_WarpToPos","Stats_BuildingsLost","Stats_InfantryLost","Stats_KillsTotal","Stats_PlayerAt","Stats_PlayerCount","Stats_ResGathered","Stats_ResSpent","Stats_SoldiersKilled","Stats_StructuresKilled","Stats_TeamTally","Stats_TotalDuration","Stats_TotalSquadsLost","Stats_UnitSoldierKills","Stats_UnitStructureKills","Stats_UnitTotalKills","Stats_UnitVehicleKills","Stats_VehiclesKilled","Stats_VehiclesLost","Stinger_AddEvent","Stinger_AddFunction","Stinger_Remove","Team_AddResource","Team_AddSquadsToSGroup","Team_AreSquadsNearMarker","Team_CanSee","Team_ClearArea","Team_DefineAllies","Team_DefineEnemies","Team_FindByRace","Team_ForEachAllOrAny","Team_GetAll","Team_GetAllEntitiesNearMarker","Team_GetAllSquadsNearMarker","Team_GetBuildingID","Team_GetBuildingsCount","Team_GetBuildingsCountExcept","Team_GetBuildingsCountOnly","Team_GetEnemyTeam","Team_GetEntitiesFromType","Team_HasBuilding","Team_HasBuildingsExcept","Team_HasBuildingUnderConstruction","Team_IsAlive","Team_OwnsEGroup","Team_OwnsEntity","Team_OwnsSGroup","Team_OwnsSquad","Team_RestrictAddOnList","Team_RestrictBuildingList","Team_RestrictResearchList","Team_SetAbilityAvailability","Team_SetCommandAvailability","Team_SetConstructionMenuAvailability","Team_SetEntityProductionAvailability","Team_SetMaxCapPopulation","Team_SetMaxPopulation","Team_SetSquadProductionAvailability","Team_SetTechTreeByYear","Team_SetUpgradeAvailability","Team_SetUpgradeCost","ToW_DefenseCreateWave","ToW_SetStandardResources","ToW_SetUpBattleObjectives","ToW_SetUpTechTreeByYear","Timer_Add","Timer_Advance","Timer_Display","Timer_DisplayOnScreen","Timer_End","Timer_Exists","Timer_GetElapsed","Timer_GetMinutesAndSeconds","Timer_GetRemaining","Timer_IsPaused","Timer_Pause","Timer_Resume","Timer_Start","EventCue_Create","FOW_Enable","Game_SubTextFade","HintMouseover_Add","HintMouseover_Remove","HintPoint_Add","HintPoint_Remove","HintPoint_SetDisplayOffset","HintPoint_SetVisible","Misc_IsEGroupSelected","Misc_IsSGroupSelected","ThreatArrow_Add","ThreatArrow_CreateGroup","ThreatArrow_DestroyAllGroups","ThreatArrow_DestroyGroup","ThreatArrow_Remove","UI_AddHintAndFlashAbility","UI_CreateEventCue","UI_CreateMinimapBlip","UI_CreateSGroupKickerMessage","UI_DeleteMinimapBlip","UI_HighlightSGroup","UI_SetAllowLoadAndSave","UI_SetSGroupSpecialLevel","WinWarning_PublishLoseReminder","WinWarning_SetMaxTickers","WinWarning_SetTickers","WinWarning_ShowLoseWarning","Clone","Event_IsAnyRunning","Game_EndSP","Game_FadeToBlack","Import_Once","Loc_FormatText","Sound_PlayOnSquad","Team_GetEntityConcentration","Team_GetSquadConcentration","Util_AddMouseoverSquadToSGroup","Util_ApplyModifier","Util_AutoAmbient","Util_AutoIntel","Util_AutoNISlet","Util_Autosave","Util_ClearWrecksFromMarker","Util_DespawnAll","Util_DifVar","Util_ElementCanSee","Util_EntityLimit","Util_FallBackToGarrisonBuilding","Util_FindHiddenSpawn","Util_ForceRetreatAll","Util_GarrisonNearbyBuilding","Util_GarrisonNearbyVehicle","Util_GetClosestMarker","Util_GetEntitiesByBP","Util_GetHealth","Util_GetMouseoverSGroup","Util_GetPosition","Util_GetPositionAwayFromPlayer","Util_GetPositionFromAtoB","Util_GetRandomPosition","Util_GetSquadsByBP","Util_GetTrailingNumber","Util_HasPosition","Util_HidePlayerForNIS","Util_IsSequenceSkipped","Util_Kill","Util_LogSyncWpn","Util_MarkerFX","Util_MissionTitle","Util_MuteAmbientSound","Util_NewHUDFeatureEvent","Util_PlayMovie","Util_PlayMusic","Util_PrintObject","Util_ReinforceEvent","Util_ReloadScript","Util_RestoreMusic","Util_SetPlayerCanSkipSequence","Util_SetPlayerUnableToSkipSequence","Util_SortPositionsByClosest","Util_StartAmbient","Util_StartIntel","Util_StartNislet","Util_StartQuickIntel","Util_TableContains","Util_ToggleAllowIntelEvents","Util_TriggerEvent","Util_UnitCounts","World_KillAllNeutralEntitesNearMarker","Anim_PlayEntityAnim","bug","Camera_AutoRotate","Camera_ClampToMarker","Camera_FocusOnPosition","Camera_FollowEntity","Camera_FollowSelection","Camera_FollowSquad","Camera_GetCurrentTargetPos","Camera_GetDeclination","Camera_GetOrbit","Camera_GetTargetPos","Camera_GetTuningValue","Camera_GetZoomDist","Camera_IsInputEnabled","Camera_Reload","Camera_ResetFocus","Camera_ResetToDefault","Camera_SetDeclination","Camera_SetInputEnabled","Camera_SetOrbit","Camera_SetSlideTargetRate","Camera_SetTuningValue","Camera_SetZoomDist","Camera_StopAutoRotating","Camera_Unclamp","EGroup_CallEntityFunction","EGroup_CallEntityFunctionAllOrAny","fatal","Game_EnableInput","Game_EndSubTextFade","Game_EndTextTitleFade","Game_GetLocalPlayer","Game_GetMode","Game_GetSPDifficulty","Game_HasLocalPlayer","Game_IsLetterboxed","Game_IsPerformanceTest","Game_IsRTM","Game_Letterbox","Game_LoadAtmosphere","Game_LockRandom","Game_ProfileDumpFrames","Game_QuitApp","Game_ScreenFade","Game_SetLocalPlayer","Game_SetMode","Game_ShowPauseMenu","Game_SkipAllEvents","Game_SkipEvent","Game_StartMuted","Game_TextTitleFade","Game_TriggerLightning","Game_UnlockInputOnLetterBox","Game_UnLockRandom","Ghost_DisableSpotting","Ghost_EnableSpotting","HintPoint_AddToEGroup","HintPoint_AddToEntity","HintPoint_AddToPosition","HintPoint_AddToSGroup","HintPoint_AddToSquad","HintPoint_ClearFacing","HintPoint_RemoveAll","HintPoint_SetDisplayOffsetInternal","HintPoint_SetFacingEntity","HintPoint_SetFacingPosition","HintPoint_SetFacingSquad","HintPoint_SetVisibleInternal","inv_dump","IsOfType","IsSecuringStructure","IsStructure","License_CanPlayRace","LOC","Loc_ConvertNumber","Loc_Empty","Loc_FormatTime","Misc_AbortToFE","Misc_AddRestrictCommandsMarker","Misc_AIControlLocalPlayer","Misc_AreDefaultCommandsEnabled","Misc_DetectKeyboardInput","Misc_DetectMouseInput","Misc_DoWeaponHitEffectOnEntity","Misc_EnablePerformanceTest","Misc_GetCommandLineString","Misc_GetControlGroupContents","Misc_GetEntityControlGroup","Misc_GetHiddenPositionOnPath","Misc_GetMouseOnTerrain","Misc_GetMouseOverEntity","Misc_GetSelectedEntities","Misc_GetSelectedSquads","Misc_GetSquadControlGroup","Misc_IsCommandLineOptionSet","Misc_IsDevMode","Misc_IsEntityOnScreen","Misc_IsEntitySelected","Misc_IsMouseOverEntity","Misc_IsPosOnScreen","Misc_IsSelectionInputEnabled","Misc_IsSquadOnScreen","Misc_IsSquadSelected","Misc_RemoveCommandRestriction","Misc_RestrictCommandsToMarker","Misc_Screenshot","Misc_ScreenshotExt","Misc_SelectEntity","Misc_SelectSquad","Misc_SetDefaultCommandsEnabled","Misc_SetDesignerSplatsVisibility","Misc_SetEntityControlGroup","Misc_SetEntitySelectable","Misc_SetSelectionInputEnabled","Misc_SetSquadControlGroup","Misc_SetSquadSelectable","Mission_Complete","Mission_Fail","Mission_GetSecondaryObjective","Mission_StartBonusObjective","Mission_Win","Modifier_ApplyToEntity","Modifier_ApplyToPlayer","Modifier_ApplyToSquad","Modifier_Create","Modifier_Destroy","Modifier_IsEnabled","nis_setintransitiontime","nis_setouttransitionnis","nis_setouttransitiontime","Obj_Create","Obj_Delete","Obj_DeleteAll","Obj_GetState","Obj_GetVisible","Obj_HideProgress","Obj_SetDescription","Obj_SetIcon","Obj_SetObjectiveFunction","Obj_SetProgressBlinking","Obj_SetState","Obj_SetTitle","Obj_SetVisible","Obj_ShowProgress","Obj_ShowProgress2","Obj_ShowProgressTimer","OpBounty_AddRewardGroup","OpBounty_AddRewardTable","Order227_Init","PrintOnScreen","PrintOnScreen_Add","PrintOnScreen_Remove","PrintOnScreen_RemoveFromScreen","ResourceAmount_Add","ResourceAmount_ClampToZero","ResourceAmount_Has","ResourceAmount_Mult","ResourceAmount_Subtract","ResourceAmount_Sum","ResourceAmount_Zero","Scar_Autosave","Scar_CompleteIntelBulletinTask","Scar_DebugConsoleExecute","Scar_PlayNIS","Scar_PlayNIS2","Scar_ReloadAIScripts","Setup_GetVictoryPointTickerOption","Setup_SetPlayerName","Setup_SetPlayerRace","Setup_SetPlayerTeam","SGroup_CallEntityFunction","SGroup_CallSquadFunction","SGroup_CallSquadFunctionAllOrAny","SitRep_PlayMovie","SitRep_PlaySpeech","SitRep_StopMovie","Sound_ContainerDebug","Sound_DisableSpeechEvent","Sound_IsPlaying","Sound_PerfTest_Play2D","Sound_Play2D","Sound_Play3D","Sound_PlayMusic","Sound_PlayStreamed","Sound_PreCacheSinglePlayerSpeech","Sound_PreCacheSound","Sound_PreCacheSoundFolder","Sound_SetGlobalControlSource","Sound_SetMusicCombatValue","Sound_SetVolume","Sound_SetVolumeDefault","Sound_SetVolumeInv","Sound_StartRecording","Sound_Stop","Sound_StopAll","Sound_StopMusic","Sound_StopRecording","Speech_SetGlobalStealthRead","statgraph","statgraph_channel","statgraph_channel_get_enabled","statgraph_channel_set_enabled","statgraph_clear","statgraph_list","statgraph_pause","Subtitle_EndAllSpeech","Subtitle_EndCurrentSpeech","Subtitle_PlaySpeech","Subtitle_UnstickCurrentSpeech","SyncWeapon_CanAttackNow","SyncWeapon_Exists","SyncWeapon_GetEntity","SyncWeapon_GetFromEGroup","SyncWeapon_GetFromSGroup","SyncWeapon_GetPosition","SyncWeapon_IsAttacking","SyncWeapon_IsOwnedByPlayer","SyncWeapon_SetAutoTargetting","Taskbar_IsVisible","Taskbar_SetVisibility","TaskCountActivePBG","TaskCountPBG","UI_AutosaveMessageHide","UI_AutosaveMessageShow","UI_ClearEventCues","UI_ClearModalAbilityPhaseCallback","UI_ClearNISEndCallback","UI_CoverPreviewHide","UI_CoverPreviewShow","UI_CreateColouredEntityKickerMessage","UI_CreateColouredPositionKickerMessage","UI_CreateColouredSquadKickerMessage","UI_CreateEntityKickerMessage","UI_CreatePositionKickerMessage","UI_CreateSquadKickerMessage","UI_EnableGameEventCueType","UI_EnableResourceTypeKicker","UI_EnableUIEventCueType","UI_FlashAbilityButton","UI_FlashConstructionButton","UI_FlashConstructionMenu","UI_FlashEntity","UI_FlashEntityCommandButton","UI_FlashEventCue","UI_FlashObjectiveCounter","UI_FlashObjectiveIcon","UI_FlashProductionBuildingButton","UI_FlashProductionButton","UI_FlashSquadCommandButton","UI_GetDecoratorsEnabled","UI_HideTacticalMap","UI_HighlightSquad","UI_IsTacticalMapShown","UI_MessageBoxHide","UI_MessageBoxSetButton","UI_MessageBoxSetText","UI_NewHUDFeature","UI_OutOfBoundsLinesHide","UI_OutOfBoundsLinesShow","UI_RestrictBuildingPlacement","UI_ScreenFade","UI_SetAbilityCardVisibility","UI_SetAlliedBandBoxSelection","UI_SetCPMeterVisibility","UI_SetDecoratorsEnabled","UI_SetForceShowSubtitles","UI_SetModalAbilityPhaseCallback","UI_SetNISEndCallback","UI_SetSoviet227Blinking","UI_SetSoviet227Visibility","UI_ShowTacticalMap","UI_StopFlashing","UI_SystemMessageHide","UI_SystemMessageShow","UI_TerritoryHide","UI_TerritoryShow","UI_TitleDestroy","UI_ToggleDecorators","UI_UnrestrictBuildingPlacement","UIWarning_Show","Util_AddProxCheck","Util_ClearProxChecks","Util_CreateEntities","Util_CreateSquads","Util_GetDistance","Util_GetOffsetPosition","Util_GetPlayerOwner","Util_GetRelationship","Util_GetRelativeOffset","Util_MonitorTerritory","Util_RemoveProxCheck","Util_RemoveProxCheckByID","Util_ScarPos","Util_SetPlayerOwner","Util_SpawnDemoCharge","Util_StartNIS","VIS_OccCullToggleOBB","Marker_CleanUpTheDead","Weather_SetType","World_AddPilferLockArea","World_CleanUpTheDead","World_ClearCasualties","World_DamageIce","World_DestroyWallsNearMarker","World_DistanceEGroupToPoint","World_DistancePointToPoint","World_DistanceSGroupToPoint","World_DistanceSquaredPointToPoint","World_EnableReplacementObjectForEmptyPlayers","World_EnableSharedLineOfSight","World_EndSP","World_GetClosest","World_GetCurrentInteractionStage","World_GetEntitiesNearMarker","World_GetEntitiesNearPoint","World_GetEntitiesWithinTerritorySector","World_GetEntity","World_GetFurthest","World_GetGameTime","World_GetHeightAt","World_GetHiddenPositionOnPath","World_GetLength","World_GetNearestInteractablePoint","World_GetNeutralEntitiesNearMarker","World_GetNeutralEntitiesNearPoint","World_GetNeutralEntitiesWithinTerritorySector","World_GetNumEntities","World_GetNumEntitiesNearPoint","World_GetNumStrategicPoints","World_GetNumVictoryPoints","World_GetOffsetPosition","World_GetPlayerAt","World_GetPlayerCount","World_GetPlayerIndex","World_GetPossibleSquadsBlueprint","World_GetPossibleSquadsCount","World_GetRaceIndex","World_GetRand","World_GetSpawnablePosition","World_GetSquadsNearMarker","World_GetSquadsNearPoint","World_GetSquadsWithinTerritorySector","World_GetStrategyPoints","World_GetTeamTerritoryGaps","World_GetTeamVictoryTicker","World_GetTerritorySectorID","World_GetTerritorySectorPosition","World_GetWidth","World_IncreaseInteractionStage","World_IsGameOver","World_IsInSupply","World_IsPointInPlayerTerritory","World_IsTerritorySectorOwnedByPlayer","World_IsWinterMap","World_OwnsEGroup","World_OwnsEntity","World_OwnsSGroup","World_OwnsSquad","World_PointPointProx","World_Pos","World_RemoveAllResourcePoints","World_RemovePilferLockArea","World_SetDesignerSupply","World_SetGameOver","World_SetIceHealingRate","World_SetPlayerCustomSkin","World_SetPlayerLose","World_SetPlayerWin","World_SetSnowHealingRate","World_SetTeamWin","World_SpawnDemolitionCharge","World_TeamTerritoryPointsConnected","Scar_AddInit","scartype","scartype_tostring","import","UI_GetViewportWidth","UI_GetViewportHeight","UI_ButtonAdd","UI_ButtonSetCallback","UI_ButtonSetEnabled","UI_ButtonSetIcon","UI_ButtonSetTag","UI_ButtonSetText","UI_LabelAdd","UI_LabelSetText","UI_IconAdd","UI_IconSetIcon","UI_PanelAdd","UI_StatusIndicatorAdd","UI_StatusIndicatorSetValue","UI_ControlSetColour","UI_ControlSetPosition","UI_ControlSetRect","UI_ControlRemove","UI_ControlClear","BS_NearBase","BS_Defend","BS_Secure","BS_Mines","BS_OuterBase","CPT_VictoryPoint","CPT_MunitionPoint","CPT_NullPoint","CPT_TacticalPoint","CPT_INVALID","CPT_FuelPoint","COMBAT_Default","COMBAT_Defend","COMBAT_Attack","MPT_VictoryPoint","MPT_NullPoint","MPT_NONE","MPT_MunitionPoint","MPT_COUNT","MPT_SupportStructure","MPT_Defence","MPT_Spawner","MPT_HQ","MPT_TacticalPoint","MPT_FuelPoint","MTARGET_Attack","MTARGET_Defend","AI_ProductionQueue","AI_CapturePoint","AI_Squad","AITacticTargetPreference_HighDamage","AITacticTargetPreference_LowHealth","AITacticTargetPreference_None","AITacticTargetPreference_Support","AITacticTargetPreference_Near","AITacticTargetPreference_NearAndBest","AITacticTargetPreference_Best","TACTIC_CapturePoint","TACTIC_Ability","TACTIC_Pickup","TACTIC_ForceAttack","TACTIC_Hold","TACTIC_MinRange","TACTIC_CaptureTeamWeapon","TACTIC_WarmUp","TACTIC_ProvideReinforcementPoint","TACTIC_RushAtTarget","TACTIC_Recrew","TACTIC_Vehicle","TACTIC_Avoid","TACTIC_Cover","TACTIC_FinishHealing","TASK_Leader","TASK_Production","TASK_Ability","TASK_PlayerAbility","TASK_Combat","TASK_Construction","TASK_Capture","TASK_ImmobileCombat","AII_LocalHumanTakeover","AII_RemoteAITakeover","AII_None","AII_RemoteHumanTakeover","AII_Normal","ITEM_REMOVED","ITEM_DEFAULT","ITEM_UNLOCKED","ITEM_LOCKED","BT_AttackHere","BT_SectorArtillery","BT_ObjectivePrimary","BT_Reveal","BT_Combat","BT_General","BT_CaptureHere","BT_DefendHere","BT_ObjectiveSecondary","BT_RallyPoint","BFS_Smoking","BFS_Burning","BFS_NotOnFire","TV_DeclinationEnabled","TV_DistMaxDead","TV_DistRateMouse","TV_NISletDistMin","TV_SlideOrbitRate","TV_PanScaleKeyboardDefZ","TV_PanScaleMouseDefZ","TV_SlideDeclThreshold","TV_PanStartSpeedScalar","TV_EntityMinViewAngle","TV_SlideTargetBase","TV_NearPlaneShifter","TV_DistMin","TV_PanScaleScreenDefZ","TV_NISletDistGroundMin","TV_DeclBelow","TV_SlideTargetThreshold","TV_DeclAbove","TV_DistScale","TV_NISletDistMax","TV_PanMaxSpeedScalar","TV_NISletDeclAbove","TV_NISletDistMinGround","TV_ZoomLocked","TV_CameraMode","TV_DefaultAngle","TV_PanScaleKeyboardMinZ","TV_PanScaleMouseMinZ","TV_DeclBelowClose","TV_TrackElastic","TV_DistExpWheel","TV_DistExpMouse","TV_DistMinGround","TV_DistGroundTargetHeight","TV_ClipFar","TV_DistGroundMin","TV_DistMinDead","TV_DistMax","TV_SlideDeclBase","TV_SlideOrbitThreshold","TV_SlideOrbitBase","TV_SlideDistThreshold","TV_SlideDistBase","TV_SlideTargetRate","TV_ClipNear","TV_PanScaleScreenMinZ","TV_DistRateWheelZoomIn","TV_SlideDistRate","TV_DistRateWheelZoomOut","TV_TrackBoundScale","TV_DefaultDeclination","TV_PanAccelerate","TV_DeclRateMouse","TV_DistExp","TV_DefaultHeight","TV_SlideDeclRate","TV_RotationEnabled","TV_OrbitRateMouse","TV_FieldOfView","TV_NISletDeclBelow","CANPRODUCE_PrerequisitesProducer","CANPRODUCE_Error","CANPRODUCE_ProductionQueueFull","CANPRODUCE_ProductionItemFull","CANPRODUCE_OutOfReinforceRadius","CANPRODUCE_Ok","CANPRODUCE_Disabled","CANPRODUCE_OutOfTerritory","CANPRODUCE_UpgradeItemFull","CANPRODUCE_PopulationCapFull","CANPRODUCE_NoResources","CANPRODUCE_PrerequisitesItem","CANPRODUCE_NoItem","CT_Medic","CT_Vehicle","CT_Personnel","CHECK_BOTH","CHECK_OFFCAMERA","CHECK_IN_FOW","CT_VehicleOpticsDamaged","CT_VehicleExhaustDamaged","CT_VehicleKillCommander","CT_VehicleDriverInjured","CT_VehicleEngineYellow","CT_VehicleBack","CT_VehicleLeft","CT_VehicleRight","CT_VehicleGunnerInjured","CT_VehicleEngineGreen","CT_VehicleCrewShocked","CT_VehicleFront","CT_VehicleEngineBurning","CT_VehicleEngineRed","CT_VehicleSecondaryWeapon","CT_VehicleLoseTreadsOrWheels","CT_VehicleOutOfControl","CT_VehiclePrimaryWeapon","Crush_Heavy","Crush_Off","Crush_Light","Crush_Medium","DB_Button3","DB_Button1","DB_Close","DB_Button2","CMD_InstantBuildSquad","CMD_InstantDeath","CMD_AttackStop","CMD_BuildStructure","CMD_Face","CMD_CancelProduction","CMD_RescueCasualty","CMD_SetHoldHeading","CMD_DefuseMine","CMD_AttackMove","CMD_Fidget","CMD_Stop","CMD_PlaceCharge","CMD_Paradrop","CMD_Destroy","CMD_Load","CMD_Ability","CMD_Move","CMD_InstantUpgrade","CMD_UnloadSquads","CMD_Casualty","CMD_BuildSquad","CMD_Halt","CMD_Attack","CMD_Capture","CMD_AttackForced","CMD_Death","CMD_Unload","CMD_Evacuate","CMD_BuildEntity","CMD_Vault","CMD_AttackFromHold","CMD_RallyPoint","CMD_DefaultAction","CMD_Upgrade","CMD_ChooseResource","CMD_Projectile","STATEID_Capture","STATEID_Idle","STATEID_Evacuate","STATEID_StructureBuilding","STATEID_RepairEngineer","STATEID_Move","STATEID_Dead","STATEID_DefuseMine","GE_ProjectileFired","GE_AIPlayer_Migrated","GE_EntityKilled","GE_TerritoryEntered","GE_ConstructionComplete","GE_NonGlobalCamoDetected","GE_SquadPinned","GE_BuildItemComplete","GE_PlayerKilled","GE_EntityCommandIssued","GE_StrategicPointChanged","GE_PlayerDonation","GE_AbilityExecuted","GE_PlayerDropped","GE_PlayerBeingAttacked","GE_UpgradeComplete","GE_PlayerSkipNIS","GE_AIPlayer_ObjectiveNotification","GE_ResourceDepleted","GE_CustomUIEvent","GE_SquadKilled","GE_PlayerSurrendered","GE_SquadCommandIssued","GE_EntityParadropComplete","GE_PlayerCheat","GE_InfoPointActivated","GE_SpawnActionComplete","GE_PlayerCommandIssued","GE_PlayerHostMigrated","GE_SquadParadropComplete","GE_PlayerPhaseUp","HPAT_Hint","HPAT_MovementLooping","HPAT_Bonus","HPAT_Vaulting","HPAT_Detonation","HPAT_CoverRed","HPAT_CoverYellow","HPAT_Artillery","HPAT_FormationSetup","HPAT_Movement","HPAT_Critical","HPAT_Objective","HPAT_AttackLooping","HPAT_DeepSnow","HPAT_CoverGreen","HPAT_Attack","HPAT_RallyPoint","HUDF_None","HUDF_AbilityCard","HUDF_Upgrades","HUDF_CommandCard","HUDF_MiniMap","LOOP_NORMAL","LOOP_TOGGLE_DIRECTION","LOOP_NONE","MAP_Confirmed","MAP_Placing","MAP_Facing","MAT_Entity","MAT_Player","MAT_Weapon","MAT_Upgrade","MAT_EntityType","MAT_Ability","MAT_Squad","MAT_WeaponType","MAT_SquadType","MUT_Multiplication","MUT_MultiplyAdd","MUT_Addition","MUT_Enable","PBG_Weapon","PBG_MoveType","PBG_SlotItem","PBG_UITacticalMap","PBG_HitMaterial","PBG_PassType","PBG_Race","PBG_UISelection","PBG_Critical","PBG_CamouflageStance","PBG_Material","PBG_Tuning","PBG_Ability","PBG_Upgrade","PBG_Posture","PBG_UITerritory","MM_ForceTense","MM_ForceCalm","MM_Auto","FN_OnShow","FN_OnCounterDisplay","FN_OnActivate","FN_LuaTableQuery","FN_OnSelect","OS_Complete","OS_Incomplete","OS_Off","OS_Failed","OT_Secondary","OT_Primary","OT_Ally","OT_Neutral","OT_Player","OT_Enemy","PCMD_MunitionDonation","PCMD_SlotItemRemove","PCMD_CriticalHit","PCMD_CheatBuildTime","PCMD_Ability","PCMD_SetCommander","PCMD_CheatRevealAll","PCMD_ManpowerDonation","PCMD_UpgradeRemove","PCMD_ConstructField","PCMD_CancelProduction","PCMD_CheatKillSelf","PCMD_Upgrade","PCMD_ConstructFence","PCMD_FuelDonation","PCMD_DetonateCharges","PCMD_CheatResources","PCMD_AIPlayer","PCMD_AIPlayer_ObjectiveNotification","PCMD_ConstructStructure","PCMD_InstantUpgrade","PITEM_SquadUpgrade","PITEM_SquadReinforce","PITEM_Spawn","PITEM_Upgrade","PT_Rectangle","PT_Circle","R_NEUTRAL","R_ENEMY","R_UNDEFINED","R_ALLY","RT_SovietOrder227","RT_Command","RT_SovietProgression","RT_Popcap","RT_Manpower","RT_Munition","RT_Fuel","RT_Action","RUIITEM_Population","RUIITEM_ResourceBar","RUIITEM_Munitions","RUIITEM_Manpower","RUIITEM_Fuel","ST_MARKER","ST_PBG","ST_SCARPOS","ST_AIPLAYER","ST_TABLE","ST_EGROUP","ST_AISTATSMILITARYPOINT","ST_AISQUAD","ST_ENTITY","ST_NUMBER","ST_FUNCTION","ST_SQUAD","ST_PLAYER","ST_BOOLEAN","ST_NIL","ST_CONSTPLAYER","ST_UNKNOWN","ST_SGROUP","ST_STRING","ST_AICAPTUREPOINT","PBG_TurnPlan","PBG_EntityProperties","PBG_SquadFormation","PBG_SquadProperties","PBG_Formation","DEBUG_SELECTOR","DEBUG_COMBATZONES","SCMD_Attack","SCMD_Upgrade","SCMD_StationaryAttack","SCMD_SlotItemRemove","SCMD_Pilfer","SCMD_SetMoveType","SCMD_Ability","SCMD_Move","SCMD_BuildStructure","SCMD_InstantLoad","SCMD_Merge","SCMD_UnloadSquads","SCMD_Retreat","SCMD_DefaultAction","SCMD_RescueCasualty","SCMD_Stop","SCMD_SetCamouflageStance","SCMD_AttackMove","SCMD_RevertFieldSupport","SCMD_CancelProduction","SCMD_Capture","SCMD_Surprise","SCMD_ReinforceUnit","SCMD_CaptureTeamWeapon","SCMD_Patrol","SCMD_Face","SCMD_Recrew","SCMD_DoPlan","SCMD_DefuseCharge","SCMD_PickUpSlotItem","SCMD_BuildSquad","SCMD_InstantReinforceUnit","SCMD_Load","SCMD_InstantSetupTeamWeapon","SCMD_RallyPoint","SCMD_AbandonTeamWeapon","SCMD_Unload","SCMD_DefuseMine","SCMD_Destroy","SCMD_PlaceCharge","SCMD_InstantUpgrade","SQUADSTATEID_Capture","SQUADSTATEID_CaptureTeamWeapon","SQUADSTATEID_Move","SQUADSTATEID_Retreat","SQUADSTATEID_Plan","SQUADSTATEID_AttackMove","SQUADSTATEID_Load","SQUADSTATEID_Defuse","SQUADSTATEID_DefuseMine","SQUADSTATEID_Stop","SQUADSTATEID_Patrol","SQUADSTATEID_Ability","SQUADSTATEID_CombatStance","SQUADSTATEID_RevertFieldSupport","SQUADSTATEID_Unload","SQUADSTATEID_HoldUnload","SQUADSTATEID_PickUpSlotItem","SQUADSTATEID_Construction","SQUADSTATEID_Idle","SQUADSTATEID_WeaponTransition","SQUADSTATEID_Recrew","SQUADSTATEID_PlaceCharges","SQUADSTATEID_Combat","UIE_UpgradeComplete","UIE_PlayerPingOfShameLocal","UIE_EnemyReveal","UIE_InfoPointActivated","UIE_AITakeOver","UIE_VehicleComplete","UIE_AllyAttacked","UIE_CommanderAbilityUnlocked","UIE_CommandersUnlocked","UIE_CommandPointGained","UIE_SquadFreezing","UIE_SquadCold","UIE_CasualtySquadSpawned","UIE_SquadVeterancy","UIE_VehicleReplaced","UIE_InfantryReplaced","UIE_Sniped","UIE_BoobyTrap","UIE_MineDetected","UIE_AbilityExectued","UIE_StrategicPointCaptured","UIE_StrategicPointReverting","UIE_EnemyTerritoryEntered","UIE_TerritoryEntered","UIE_PlayerSurrendered","UIE_PlayerAttacked","UIE_VehicleAttacked","UIE_PlayerKilled","UIE_PlayerKicked","UIE_PlayerLagComplaint","UIE_PlayerPingOfShame","UIE_PlayerDropped","UIE_ConstructionComplete","UIE_StrategicPointSecured","UIE_ResourceDepleted","UIE_SquadPinned","UIE_InfantryAttacked","UIE_InfantryComplete","UIE_PlayerCheated","UIE_PhaseUp","UIE_HostMigrated","UIE_Default","UI_Cinematic","UI_Fullscreen","UI_Normal","UOT_Player","UOT_Self","UOT_None","BIS_Icon","BIS_IconState","LAH_Justify","LAH_Left","LAH_Center","LAH_Right","LAV_None","LAV_Top","LAV_Center","LAV_Bottom","assert","collectgarbage","dofile","error","getmetatable","ipairs","load","loadfile","next","pairs","pcall","print","rawequal","rawget","rawlen","rawset","select","setmetatable","tonumber","tostring","type","xpcall","string.byte","string.char","string.dump","string.find","and","break","do","else","elseif","end","false","for","function","if","in","local","nil","not","or","repeat","return","then","true","until","while","math.huge","math.maxinteger","math.mininteger","math.pi","EBP.WRECKED_VEHICLES.FRONT_HULL01","EBP.WRECKED_VEHICLES.FROZEN_PANZER_IV","EBP.WRECKED_VEHICLES.FROZEN_STUG_III","EBP.WRECKED_VEHICLES.HORSA_COCKPIT","EBP.WRECKED_VEHICLES.HORSA_FRONT_HULL","EBP.WRECKED_VEHICLES.HORSA_LEFT_WING","EBP.WRECKED_VEHICLES.HORSA_LEFT_WING_TIP","EBP.WRECKED_VEHICLES.HORSA_MID_HULL","EBP.WRECKED_VEHICLES.HORSA_REAR_HULL","EBP.WRECKED_VEHICLES.HORSA_RIGHT_WING","EBP.WRECKED_VEHICLES.HORSA_RIGHT_WING_TIP","EBP.WRECKED_VEHICLES.HORSA_TAIL","EBP.WRECKED_VEHICLES.LEFT_WING","EBP.WRECKED_VEHICLES.MAP_OBJECT_M4SHERMAN_105MM","EBP.WRECKED_VEHICLES.MAP_OBJECT_M4SHERMAN_76MM","EBP.WRECKED_VEHICLES.MAP_OBJECT_M4SHERMAN_DOZER","EBP.WRECKED_VEHICLES.MAP_OBJECT_OPELBLITZ","EBP.WRECKED_VEHICLES.MAP_OBJECT_PAK38","EBP.WRECKED_VEHICLES.MAP_OBJECT_PANZERIV","EBP.WRECKED_VEHICLES.MAP_OBJECT_STUGIII_LONG","EBP.WRECKED_VEHICLES.MAP_OBJECT_STUGIII_SHORT","EBP.WRECKED_VEHICLES.PROPELLER","EBP.WRECKED_VEHICLES.RIGHT_WING","EBP.WRECKED_VEHICLES.STUKA_BODY","EBP.WRECKED_VEHICLES.STUKA_DEBRIS","EBP.WRECKED_VEHICLES.STUKA_TAIL","EBP.WRECKED_VEHICLES.STUKA_WING_LEFT","EBP.WRECKED_VEHICLES.STUKA_WING_RIGHT","EBP.WRECKED_VEHICLES.TAIL","EBP.WRECKED_VEHICLES.TAIL_SECTION_01","EBP.WRECKED_VEHICLES.WRECKED_50MM_PAK38_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_PUMA_MP","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_SDKFZ_222","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_SDKFZ_222_MP","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_SDKFZ_234","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_SDKFZ_234_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_ARMORED_CAR_SDKFZ_234_PUMA_MP","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_17_POUNDER","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_45MM","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_75MM_PAK","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_B4_200MM","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_M1_57MM","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_ML20","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_PAK43","EBP.WRECKED_VEHICLES.WRECKED_ATGUN_ZIS3","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING01","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING01_SELF_DESTRUCT","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING02","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING02_SELF_DESTRUCT","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING03","EBP.WRECKED_VEHICLES.WRECKED_BASE_BUILDING03_SELF_DESTRUCT","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_AEC","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_AEC_ARMOURED_CAR_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_ATGUN_6_POUNDER","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_BOFORS","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CENTAUR","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL_AVRE","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL_AVRE_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL_CROCODILE","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CHURCHILL_CROCODILE_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_COMET","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_COMET_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CROMWELL","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_CROMWELL_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_GLIDER_HQ_MP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_GLIDER_MP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_SEXTON","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_SHERMAN_FIREFLY","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_SHERMAN_FIREFLY_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_UNIVERSAL_CARRIER","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_VALENTINE_COMMAND","EBP.WRECKED_VEHICLES.WRECKED_BRITISH_VALENTINE_COMMAND_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_BRUMMBAR_02","EBP.WRECKED_VEHICLES.WRECKED_BRUMMBAR_STURMPANZER_IV_SDKFZ_166","EBP.WRECKED_VEHICLES.WRECKED_EARLY_WAR_TANK_01","EBP.WRECKED_VEHICLES.WRECKED_ELEFANT_SDKFZ_184","EBP.WRECKED_VEHICLES.WRECKED_FN63_4RM","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_250","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_250_MORTAR","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_251","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_251_17_FLAK","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_251_INFRARED","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_251_MP","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SDKFZ_251_WALKING_STUKA","EBP.WRECKED_VEHICLES.WRECKED_HALFTRACK_SWS","EBP.WRECKED_VEHICLES.WRECKED_HETZER","EBP.WRECKED_VEHICLES.WRECKED_HETZER_BREWUP","EBP.WRECKED_VEHICLES.WRECKED_HOWITZER_105MM_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_IG18_SUPPORT_GUN","EBP.WRECKED_VEHICLES.WRECKED_IS_2_HEAVY_TANK","EBP.WRECKED_VEHICLES.WRECKED_ISU_152_SPG","EBP.WRECKED_VEHICLES.WRECKED_JAGDPANZER_IV","EBP.WRECKED_VEHICLES.WRECKED_JAGDPANZER_IV_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_JAGDTIGER_TD","EBP.WRECKED_VEHICLES.WRECKED_JAGDTIGER_TD_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_KATYUSHA_BM_13N","EBP.WRECKED_VEHICLES.WRECKED_KATYUSHA_BM_13N_MP","EBP.WRECKED_VEHICLES.WRECKED_KING_TIGER","EBP.WRECKED_VEHICLES.WRECKED_KING_TIGER_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_KUBELWAGEN","EBP.WRECKED_VEHICLES.WRECKED_KV_1","EBP.WRECKED_VEHICLES.WRECKED_KV_1_MP","EBP.WRECKED_VEHICLES.WRECKED_KV_2","EBP.WRECKED_VEHICLES.WRECKED_KV_8","EBP.WRECKED_VEHICLES.WRECKED_LAND_MATTRESS","EBP.WRECKED_VEHICLES.WRECKED_M10","EBP.WRECKED_VEHICLES.WRECKED_M10_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_M15A1_AA_HALFTRACK","EBP.WRECKED_VEHICLES.WRECKED_M15A1_AA_HALFTRACK_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_M20_UTILITY_CAR","EBP.WRECKED_VEHICLES.WRECKED_M21_MORTAR_HALFTRACK","EBP.WRECKED_VEHICLES.WRECKED_M26_PERSHING","EBP.WRECKED_VEHICLES.WRECKED_M3_HALFTRACK","EBP.WRECKED_VEHICLES.WRECKED_M36","EBP.WRECKED_VEHICLES.WRECKED_M36_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_M3A1_SCOUT_CAR","EBP.WRECKED_VEHICLES.WRECKED_M3A1_SCOUT_CAR_MP","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_BULLDOZER","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_BULLDOZER_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_EASY_EIGHT","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_EASY_EIGHT_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_M4A3_SHERMAN_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_M5_HALFTRACK","EBP.WRECKED_VEHICLES.WRECKED_M5_HALFTRACK_MP","EBP.WRECKED_VEHICLES.WRECKED_M5A1_STUART","EBP.WRECKED_VEHICLES.WRECKED_M8_ARMORED_CAR","EBP.WRECKED_VEHICLES.WRECKED_M8_HMC","EBP.WRECKED_VEHICLES.WRECKED_OPEL_BLITZ_TRUCK","EBP.WRECKED_VEHICLES.WRECKED_OSTWIND_FLAK_PANZER","EBP.WRECKED_VEHICLES.WRECKED_PACK_HOWITZER","EBP.WRECKED_VEHICLES.WRECKED_PANTHER_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_PANTHER_SDKFZ_171","EBP.WRECKED_VEHICLES.WRECKED_PANTHER_SDKFZ_171_BREWUP","EBP.WRECKED_VEHICLES.WRECKED_PANZER_II_LUCHS","EBP.WRECKED_VEHICLES.WRECKED_PANZER_II_LUCHS_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_PANZER_III","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_FROZEN","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_SDKFZ_161","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_SDKFZ_161_COMMAND","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_SDKFZ_161_GAMEPLAY","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_SDKFZ_161_WEST_GERMAN","EBP.WRECKED_VEHICLES.WRECKED_PANZER_IV_SDKFZ_161_WEST_GERMAN_BREW_UP","EBP.WRECKED_VEHICLES.WRECKED_PANZERIV_MAP_OBJECT","EBP.WRECKED_VEHICLES.WRECKED_PANZERWERFER_SDKFZ_4_1","EBP.WRECKED_VEHICLES.WRECKED_PRIEST","EBP.WRECKED_VEHICLES.WRECKED_RAKETENWERFER","EBP.WRECKED_VEHICLES.WRECKED_SOVIET_76MM_SHERMAN","EBP.WRECKED_VEHICLES.WRECKED_STUG_III_E_SDKFZ_141_1","EBP.WRECKED_VEHICLES.WRECKED_STUG_III_FROZEN","EBP.WRECKED_VEHICLES.WRECKED_STUG_III_G_SDKFZ_141_1","EBP.WRECKED_VEHICLES.WRECKED_STUG_III_G_SDKFZ_141_1_GAMEPLAY","EBP.WRECKED_VEHICLES.WRECKED_STURMTIGER","EBP.WRECKED_VEHICLES.WRECKED_SU_76M","EBP.WRECKED_VEHICLES.WRECKED_SU_85","EBP.WRECKED_VEHICLES.WRECKED_T_34_76","EBP.WRECKED_VEHICLES.WRECKED_T_34_76_02","EBP.WRECKED_VEHICLES.WRECKED_T_34_76_MP","EBP.WRECKED_VEHICLES.WRECKED_T_34_85_RED_BANNER","EBP.WRECKED_VEHICLES.WRECKED_T_34_85_RED_BANNER_MP","EBP.WRECKED_VEHICLES.WRECKED_T_34_85_RED_BANNER_TOW","EBP.WRECKED_VEHICLES.WRECKED_T34_CALLIOPE","EBP.WRECKED_VEHICLES.WRECKED_T70","EBP.WRECKED_VEHICLES.WRECKED_T70_MP","EBP.WRECKED_VEHICLES.WRECKED_TIGER_SDKFZ_181","EBP.WRECKED_VEHICLES.WRECKED_TIGER_SDKFZ_181_SINGLEPLAYER_MISSION","EBP.WRECKED_VEHICLES.WRECKED_WC51","EBP.WRECKED_VEHICLES.WRECKED_WC54_AMBULANCE","EBP.AEF.AEF_AIRDROPPED_MINE_CONTACT_MP","EBP.AEF.AEF_AIRDROPPED_MINE_MP","EBP.AEF.AEF_ALLIEDSUPPLY_STACK_L_01_MP","EBP.AEF.AEF_ATTACK_PLANE","EBP.AEF.AEF_BARBED_WIRE_FENCE_MP","EBP.AEF.AEF_BARRACKS","EBP.AEF.AEF_BASE_STAMPER","EBP.AEF.AEF_GARRISON","EBP.AEF.AEF_MG_NEST","EBP.AEF.AEF_MG_NEST_AEF_BASE","EBP.AEF.AEF_MG_NEST_PERIMETER_MP","EBP.AEF.AEF_MINE_MP","EBP.AEF.AEF_MINE_RIFLEMEN_MP","EBP.AEF.AEF_SANDBAG_DIRTWALL_01","EBP.AEF.AEF_SANDBAG_FENCE","EBP.AEF.AEF_SANDBAGS","EBP.AEF.AEF_SANDBAGWALL","EBP.AEF.AEF_SANDBAGWALL_COVER_SPECIALIZATION","EBP.AEF.AEF_STORAGEBUNKER","EBP.AEF.AEF_SUPPLYTENT","EBP.AEF.AEF_TANK_TRAP_IMPASSABLE_MP","EBP.AEF.AEF_TANK_TRAP_MP","EBP.AEF.AEF_WEAPON_RACK_BAZOOKA_MP","EBP.AEF.AEF_WEAPON_RACK_BROWNING_AUTOMATIC_RIFLE_MP","EBP.AEF.AEF_WEAPON_RACK_DEFAULT_MP","EBP.AEF.AEF_WEAPON_RACK_M1919_LMG","EBP.AEF.AEF_WEAPON_RACK_M1C_GARAND","EBP.AEF.AEF_WEAPON_RACK_M9_BAZOOKA_MP","EBP.AEF.AIRBORNE_BEACON_MP","EBP.AEF.ARMOR_COMMAND_MP","EBP.AEF.ARMOR_COMMAND_SP","EBP.AEF.ARMOR_COMMAND_WRECK_MP","EBP.AEF.ARMORED_RIFLE_COMMAND_MP","EBP.AEF.ARMORED_RIFLE_COMMAND_SP","EBP.AEF.ARMORED_RIFLE_COMMAND_WRECK_MP","EBP.AEF.ASSAULT_ENGINEER_MP","EBP.AEF.ASSAULT_ENGINEER_VEHICLE_CREW_MP","EBP.AEF.AT_TEAM_WEAPON_CREW_MP","EBP.AEF.CAPTAIN_MP","EBP.AEF.CAPTAIN_UNLOCK_MP","EBP.AEF.COMPANY_WEAPONS_POOL_MP","EBP.AEF.COMPANY_WEAPONS_POOL_SP","EBP.AEF.COMPANY_WEAPONS_POOL_WRECK_MP","EBP.AEF.DODGE_WC51_50CAL_MP","EBP.AEF.DODGE_WC51_50CAL_PARADROP","EBP.AEF.DODGE_WC51_AMBULANCE_MP","EBP.AEF.DODGE_WC51_MP","EBP.AEF.DODGE_WC51_MP_PATHFINDERS","EBP.AEF.FIGHTING_POSITION_MP","EBP.AEF.FIGHTING_POSITION_RIFLEMEN_MP","EBP.AEF.HMG_TEAM_WEAPON_CREW_MP","EBP.AEF.HOWITZER_TEAM_WEAPON_CREW_MP","EBP.AEF.INVISI_HEAL_STATION_MP","EBP.AEF.INVISI_REPAIR_STATION_MP","EBP.AEF.JACKSON","EBP.AEF.LIEUTENANT_MP","EBP.AEF.LIEUTENANT_UNLOCK_MP","EBP.AEF.M1_57MM_ANTITANK_GUN_MP","EBP.AEF.M1_75MM_PACK_HOWITZER_MP","EBP.AEF.M1_81MM_MORTAR_MP","EBP.AEF.M10_TANK_DESTROYER_MP","EBP.AEF.M15A1_AA_HALFTRACK_MP","EBP.AEF.M1919A4_30CAL_MACHINE_GUN_MP","EBP.AEF.M1919A4_TEAM_WEAPON_CREW_MP","EBP.AEF.M2_60MM_MORTAR_MP","EBP.AEF.M20_M6_AT_MINE_MP","EBP.AEF.M20_UTILITY_CAR_MP","EBP.AEF.M21_MORTAR_HALFTRACK_MP","EBP.AEF.M26_PERSHING_MP","EBP.AEF.M2HB_50CAL_MACHINE_GUN_MP","EBP.AEF.M3_HALFTRACK_ASSAULT_MP","EBP.AEF.M3_HALFTRACK_MP","EBP.AEF.M36_TANK_DESTROYER_MP","EBP.AEF.M4A3_76MM_SHERMAN_MP","EBP.AEF.M4A3_SHERMAN_BULLDOZER_MP","EBP.AEF.M4A3_SHERMAN_DEMO_BURNOUT","EBP.AEF.M4A3_SHERMAN_MP","EBP.AEF.M4A3E8_SHERMAN_EASY_8_MP","EBP.AEF.M5_HALFTRACK_USF_MP","EBP.AEF.M5A1_STUART_MP","EBP.AEF.M7B1_PRIEST_MP","EBP.AEF.M8_GREYHOUND_MP","EBP.AEF.M8A1_HMC_MP","EBP.AEF.MAJOR_MP","EBP.AEF.MAJOR_RETREAT_POINT_MP","EBP.AEF.MAJOR_UNLOCK_MP","EBP.AEF.MORTAR_TEAM_WEAPON_CREW_MP","EBP.AEF.OBSERVATION_POST_FUEL_AEF_MP","EBP.AEF.OBSERVATION_POST_MUNITION_AEF_MP","EBP.AEF.P47_RECON","EBP.AEF.P47_RECON_PLANE_SWEEP","EBP.AEF.P47_RECON_TRACKING","EBP.AEF.P47_ROCKETS","EBP.AEF.P47_STRAFE","EBP.AEF.PARATROOPER_MP","EBP.AEF.PARATROOPERS_COMBAT_GROUP_PLANE","EBP.AEF.PARATROOPERS_PLANE","EBP.AEF.PARATROOPERS_PLANE_ATGUN","EBP.AEF.PARATROOPERS_PLANE_HMG","EBP.AEF.PARATROOPERS_PLANE_MINES","EBP.AEF.PARATROOPERS_PLANE_PARAS","EBP.AEF.PATHFINDER_IR_MP","EBP.AEF.PATHFINDER_RECON_MP","EBP.AEF.PM_AEF_AIR_SUPPORT_RECON","EBP.AEF.PM_AEF_AIR_SUPPORT_ROCKET","EBP.AEF.PM_AEF_AIR_SUPPORT_ROCKET_ELITE","EBP.AEF.PM_AEF_AIR_SUPPORT_STRAFE","EBP.AEF.PM_AEF_AIR_SUPPORT_STRAFE_ELITE","EBP.AEF.PM_AEF_AIRBORNE_PARATROOPERS_PLANE_PARAS","EBP.AEF.PM_AEF_AIRBORNE_PARATROOPERS_PLANE_STRAFE","EBP.AEF.PM_AEF_AIRBORNE_PARATROOPERS_SPAWNER","EBP.AEF.PM_AEF_AIRBORNE_SUPPLY_DROP_PLANE","EBP.AEF.PM_AEF_FIGHTING_POSITION_TEAMWEAPONS","EBP.AEF.PM_AEF_PINPOINT_ARTY_MARKER_MP","EBP.AEF.PM_AEF_PINPOINT_ARTY_THREE_MARKER_MP","EBP.AEF.PM_ARMOR_COMMAND_BAZOOKA_RACK","EBP.AEF.PM_ARMOR_COMMAND_LMG_RACK","EBP.AEF.PM_ATTACHED_MEDIC","EBP.AEF.PM_ATTACHED_SEARGENT","EBP.AEF.PM_P47_FLYBY","EBP.AEF.PM_P47_MG_STRAFE","EBP.AEF.PM_P47_ROCKET_STRAFE","EBP.AEF.RANGER_COMMANDER_MP","EBP.AEF.RANGER_MP","EBP.AEF.REAR_ECHELON_RADIOMAN_MP","EBP.AEF.REAR_ECHELON_RESERVE_TROOP_MP","EBP.AEF.REAR_ECHELON_TROOP_CAPT_MP","EBP.AEF.REAR_ECHELON_TROOP_MP","EBP.AEF.REPLACEMENT_ARMOR_COMMAND_MP","EBP.AEF.REPLACEMENT_ARMORED_RIFLE_COMMAND_MP","EBP.AEF.REPLACEMENT_COMPANY_WEAPONS_POOL_MP","EBP.AEF.RIFLE_COMMAND_MP","EBP.AEF.RIFLE_COMMAND_SP","EBP.AEF.RIFLE_COMMAND_WRECK_MP","EBP.AEF.RIFLEMAN_SOLDIER_CAPTAIN_MP","EBP.AEF.RIFLEMAN_SOLDIER_GROUP_MP","EBP.AEF.RIFLEMAN_SOLDIER_LIEUTENANT_MP","EBP.AEF.RIFLEMAN_SOLDIER_MP","EBP.AEF.SHERMAN_BARRIER_DEFORM_MP","EBP.AEF.SHERMAN_BARRIER_DIRT_MP","EBP.AEF.SHERMAN_BARRIER_MUD_MP","EBP.AEF.SHERMAN_BARRIER_RUBBLE_MP","EBP.AEF.SHERMAN_BARRIER_SNOW_MP","EBP.AEF.T34_CALLIOPE_MP","EBP.AEF.TEMP_ACTIVE_STRUCTURE_SEARCHLIGHT","EBP.AEF.USF_MEDIC_MP","EBP.AEF.VEHICLE_CREW_BAZOOKA_MP","EBP.AEF.VEHICLE_CREW_TROOP_MP","EBP.AEF.VEHICLE_CREW_TROOP_REPAIR_STATION_MP","SBP.AEF.AEF_AIR_SUPPORT_RECON","SBP.AEF.AEF_AIR_SUPPORT_ROCKET","SBP.AEF.AEF_AIR_SUPPORT_ROCKET_ELITE","SBP.AEF.AEF_AIR_SUPPORT_STRAFE","SBP.AEF.AEF_AIR_SUPPORT_STRAFE_ELITE","SBP.AEF.AEF_ATTACK_PLANE_SQUAD","SBP.AEF.AEF_HALFTRACK_SQUAD_MP","SBP.AEF.ASSAULT_ENGINEER_SQUAD_5_MAN_MP","SBP.AEF.ASSAULT_ENGINEER_SQUAD_MP","SBP.AEF.CAPTAIN_SQUAD_MP","SBP.AEF.DODGE_WC51_50CAL_SQUAD_MP","SBP.AEF.DODGE_WC51_AMBULANCE_SQUAD_MP","SBP.AEF.DODGE_WC51_PATHFINDER_SQUAD_MP","SBP.AEF.DODGE_WC51_SQUAD_MP","SBP.AEF.JACKSON_SQUAD","SBP.AEF.LIEUTENANT_SQUAD_MP","SBP.AEF.M1_57MM_AT_GUN_SQUAD_BOB","SBP.AEF.M1_57MM_AT_GUN_SQUAD_MP","SBP.AEF.M1_75MM_PACK_HOWITZER_SQUAD_MP","SBP.AEF.M1_81MM_MORTAR_SQUAD_MP","SBP.AEF.M10_TANK_DESTROYER_SQUAD_MP","SBP.AEF.M15A1_AA_HALFTRACK_SQUAD_MP","SBP.AEF.M1919A4_HMG_SQUAD_MP","SBP.AEF.M2_60MM_MORTAR_CORE_SQUAD_MP","SBP.AEF.M2_60MM_MORTAR_SQUAD_MP","SBP.AEF.M2_60MM_MORTAR_SQUAD_MP_CLONE","SBP.AEF.M20_ASSAULT_ENGY_ANTITANK_SQUAD_MP","SBP.AEF.M20_UTILITY_CAR_SQUAD_MP","SBP.AEF.M21_MORTAR_HALFTRACK_SQUAD_MP","SBP.AEF.M26_PERSHING_MP","SBP.AEF.M2HB_50CAL_HMG_SQUAD_MP","SBP.AEF.M3_HALFTRACK_SQUAD_ASSAULT_MP","SBP.AEF.M3_HALFTRACK_SQUAD_MP","SBP.AEF.M36_TANK_DESTROYER_SQUAD_MP","SBP.AEF.M4A3_76MM_SHERMAN_BULLDOZER_SQUAD_MP","SBP.AEF.M4A3_76MM_SHERMAN_SQUAD_MP","SBP.AEF.M4A3_SHERMAN_SQUAD_DEMO_BURNOUT","SBP.AEF.M4A3_SHERMAN_SQUAD_MP","SBP.AEF.M4A3E8_SHERMAN_EASY_8_SQUAD_MP","SBP.AEF.M5A1_STUART_SQUAD_MP","SBP.AEF.M7B1_PRIEST_SQUAD_MP","SBP.AEF.M8_GREYHOUND_SQUAD_MP","SBP.AEF.M8A1_HMC_SQUAD_MP","SBP.AEF.MAJOR_SQUAD_MP","SBP.AEF.P47_FLYBY","SBP.AEF.P47_MG_STRAFE","SBP.AEF.P47_RECON","SBP.AEF.P47_RECON_PLANE_SWEEP","SBP.AEF.P47_RECON_TRACKING","SBP.AEF.P47_ROCKETS","SBP.AEF.P47_ROCKETS_STRAFE","SBP.AEF.P47_STRAFES","SBP.AEF.PARATROOPER_COMBAT_GROUP_SQUAD_MP","SBP.AEF.PARATROOPER_SQUAD_MP","SBP.AEF.PARATROOPER_SQUAD_SUPPORT_MP","SBP.AEF.PARATROOPERS_COMBAT_GROUP_PLANE","SBP.AEF.PARATROOPERS_PLANE","SBP.AEF.PARATROOPERS_PLANE_ATGUN","SBP.AEF.PARATROOPERS_PLANE_HMG","SBP.AEF.PARATROOPERS_PLANE_MINES","SBP.AEF.PARATROOPERS_PLANE_PARAS","SBP.AEF.PATHFINDER_SQUAD_MP","SBP.AEF.PATHFINDER_SQUAD_RECON_MP","SBP.AEF.PM_AEF_AIRBORNE_PARATROOPERS_PLANE_PARAS","SBP.AEF.PM_AEF_AIRBORNE_PARATROOPERS_PLANE_STRAFE","SBP.AEF.PM_AEF_AIRBORNE_SUPPLY_DROP_PLANE","SBP.AEF.PM_M3_HALFTRACK_SQUAD_OMCG","SBP.AEF.PM_RIFLEMEN_SQUAD_OMCG","SBP.AEF.RANGER_SQUAD_COMMANDER_MP","SBP.AEF.RANGER_SQUAD_MP","SBP.AEF.REAR_ECHELON_SQUAD_MP","SBP.AEF.RIFLEMEN_SQUAD_MP","SBP.AEF.RIFLEMEN_SQUAD_VETERAN_MP","SBP.AEF.T34_CALLIOPE_SQUAD_MP","SBP.AEF.USF_MEDIC_SQUAD_MP","SBP.AEF.VEHICLE_CREW_BAZOOKA_SQUAD_MP","SBP.AEF.VEHICLE_CREW_SQUAD_MP","ABILITY.AEF.ACTIVATE_REPAIR_STATION_MP","ABILITY.AEF.AEF_BARBED_WIRE_CUTTING_ABILITY_ASSUALT_ENGINEERS_MP","ABILITY.AEF.AEF_BARBED_WIRE_CUTTING_ABILITY_MP","ABILITY.AEF.AEF_BARBED_WIRE_CUTTING_ABILITY_NO_REQUIREMENT_MP","ABILITY.AEF.AEF_HQ_ENGINEER_CALL_IN","ABILITY.AEF.AEF_REPAIR_ABILITY_REAR_ECHELON_MP","ABILITY.AEF.AEF_REPAIR_ABILITY_VEHICLE_CREW_MP","ABILITY.AEF.AEF_REPAIR_CRITICAL_MP","ABILITY.AEF.AIR_DROP_COMBAT_GROUP","ABILITY.AEF.AMBULANCE_HEAL_AREA","ABILITY.AEF.ARTILLERY_155MM","ABILITY.AEF.ARTILLERY_SMOKE_BARRAGE","ABILITY.AEF.ASSAULT_ENGINEER_DISPATCH","ABILITY.AEF.BAR_SUPPRESSION_ABILITY","ABILITY.AEF.BAZOOKA_DEPLOY_MP","ABILITY.AEF.BEACON_DISABLE","ABILITY.AEF.CALLIOPE_ROCKET_BARRAGE_MP","ABILITY.AEF.CAPTAIN_SUPERVISE","ABILITY.AEF.CMD_PARATROOPERS_FROM_PATHFINDERS","ABILITY.AEF.COMBAT_ENGINEER_TIMED_DEMO_MP","ABILITY.AEF.COMBINED_ARMS","ABILITY.AEF.DODGE_WC51_DISPATCH","ABILITY.AEF.ELITE_RIFLEMEN","ABILITY.AEF.ELITE_VEHICLE_CREWS","ABILITY.AEF.FATALITY_P47_ROCKET_ATTACK","ABILITY.AEF.FATALITY_PARATROOPERS_PARADROP","ABILITY.AEF.FATALITY_SMOKE_FLARES","ABILITY.AEF.FATALITY_WHITE_PHOSPHOROUS_BARRAGE","ABILITY.AEF.FLANKING_SPEED","ABILITY.AEF.FORWARD_OBSERVERS_ALWAYS_ON","ABILITY.AEF.FORWARD_OBSERVERS_UNLOCK_2","ABILITY.AEF.GREYHOUND_RECON_DISPATCH","ABILITY.AEF.LIEUTENANT_CAPTAIN_ON_ME_AURA_MP","ABILITY.AEF.M1_81MM_MORTAR_TEAM_MORTAR_BARRAGE_MP","ABILITY.AEF.M1_81MM_MORTAR_TEAM_SMOKE_BARRAGE_MP","ABILITY.AEF.M1_81MM_MORTAR_WHITE_PHOSPHOROUS_BARRAGE_ABILITY_MP","ABILITY.AEF.M1_ATGUN_PIERCING_ABILITY","ABILITY.AEF.M1_ATGUN_TAKE_AIM_ABILITY","ABILITY.AEF.M10_APCPC_SHELLS","ABILITY.AEF.M10_APCPC_SHELLS_VET","ABILITY.AEF.M10_DEPLOY","ABILITY.AEF.M15A1_AA_MODE_MP","ABILITY.AEF.M2_60MM_MORTAR_TEAM_MORTAR_BARRAGE_MP","ABILITY.AEF.M2_60MM_MORTAR_TEAM_SMOKE_BARRAGE_MP","ABILITY.AEF.M20_MARK_VEHICLE","ABILITY.AEF.M20_SMOKE","ABILITY.AEF.M21_HEAVY_HE_SHORT_DELAY_MORTAR_BARRAGE_MP","ABILITY.AEF.M21_MORTAR_BARRAGE_MP","ABILITY.AEF.M21_MORTAR_BARRAGE_VICTOR_TARGET_MP","ABILITY.AEF.M21_MORTAR_HALFTRACK_DISPATCH","ABILITY.AEF.M21_MORTAR_WHITE_PHOSPHOROUS_BARRAGE_MP","ABILITY.AEF.M23_SMOKE_STREAM_RIFLE_GRENADE_MP","ABILITY.AEF.M23_SMOKE_STREAM_RIFLE_GRENADE_VET_MP","ABILITY.AEF.M26_PERSHING_DISPATCH","ABILITY.AEF.M2HB_50CAL_AP_ROUNDS_MP","ABILITY.AEF.M2HB_HMG_SPRINT_MP","ABILITY.AEF.M3_HALFTRACK_GROUP","ABILITY.AEF.M3_HALFTRACK_SPEED_BOOST_MP","ABILITY.AEF.M36_M8_CONCEALING_SMOKE_VET","ABILITY.AEF.M5_QUAD_HALFTRACK_DISPATCH","ABILITY.AEF.M5_STUART_DAMAGE_ENGINE","ABILITY.AEF.M5_STUART_SHELL_SHOCK","ABILITY.AEF.M7B1_PRIEST_105MM_BARRAGE_ABILITY_MP","ABILITY.AEF.M7B1_PRIEST_105MM_BARRAGE_ABILITY_VICTOR_TARGET_MP","ABILITY.AEF.M7B1_PRIEST_105MM_SMOKE_BARRAGE_ABILITY_MP","ABILITY.AEF.M8_CANISTER_SHOT","ABILITY.AEF.M8_LAY_HEAVY_MINE","ABILITY.AEF.M8A1_HMC_75MM_BARRAGE_ABILITY_MP","ABILITY.AEF.M8A1_HMC_75MM_BARRAGE_ABILITY_VICTOR_TARGET_MP","ABILITY.AEF.M8A1_HMC_SMOKE_BARRAGE_MP","ABILITY.AEF.MAJOR_ARTILLERY","ABILITY.AEF.MAJOR_ARTILLERY_FAKE","ABILITY.AEF.MAJOR_QUICK_RECON_RUN","ABILITY.AEF.MAJOR_QUICK_RECON_RUN_IMPROVED","ABILITY.AEF.MEDIC_AUTO_HEAL","ABILITY.AEF.MK2_FRAGMENTATION_GRENADE_MP","ABILITY.AEF.OFF_MAP_SMOKE_ARTILLERY","ABILITY.AEF.OFFICER_RETREAT_POINT_MP","ABILITY.AEF.OFFICER_STOP_RETREAT_MP","ABILITY.AEF.OUT_OF_FUEL_SP","ABILITY.AEF.P47_RECON_MP","ABILITY.AEF.P47_ROCKET_ATTACK","ABILITY.AEF.PACK_HOWITZER_75MM_BARRAGE_ABILITY_HEAT_MP","ABILITY.AEF.PACK_HOWITZER_75MM_BARRAGE_ABILITY_MP","ABILITY.AEF.PACK_HOWITZER_75MM_BARRAGE_ABILITY_VET3_MP","ABILITY.AEF.PACK_HOWITZER_75MM_BARRAGE_ABILITY_VICTOR_TARGET_MP","ABILITY.AEF.PACK_HOWITZER_WHITE_PHOSPHOROUS_BARRAGE_ABILITY_MP","ABILITY.AEF.PARADROP_MACHINE_GUN","ABILITY.AEF.PARADROPS_ANTI_TANK_GUN","ABILITY.AEF.PARATROOPER_ASSAULT_MOVE_TEST_MP","ABILITY.AEF.PARATROOPER_MK2_FRAGMENTATION_GRENADE_MP","ABILITY.AEF.PARATROOPER_SUPPRESSING_FIRE_ABILITY_MP","ABILITY.AEF.PARATROOPER_TIMED_DEMO_MP","ABILITY.AEF.PARATROOPERS_PARADROP","ABILITY.AEF.PATHFINDER_ARTILLERY_UNLOCK","ABILITY.AEF.PATHFINDER_IN_COVER_STATIONARY_CAMOUFLAGE_IMPROVED_MP","ABILITY.AEF.PATHFINDER_IN_COVER_STATIONARY_CAMOUFLAGE_MP","ABILITY.AEF.PATHFINDER_PLANT_BEACON","ABILITY.AEF.PATHFINDERS_DISPATCH","ABILITY.AEF.PATHFINDERS_RECON_DISPATCH","ABILITY.AEF.PERSHING_HVAP_PIERCING_SHOT_ABILITY","ABILITY.AEF.PRIEST_ARTILLERY_BARRAGE_CREEPING_MP","ABILITY.AEF.PRIEST_DISPATCH","ABILITY.AEF.RANGER_BUNDLED_GRENADE_MP","ABILITY.AEF.RANGER_LIMITED_DEMO_MP","ABILITY.AEF.RANGER_MK2_FRAGMENTATION_GRENADE_MP","ABILITY.AEF.RANGER_SPRINT_MP","ABILITY.AEF.RANGERS_DISPATCH","ABILITY.AEF.REAR_ECHELON_VOLLEY_FIRE_ABILITY_MP","ABILITY.AEF.RECON_SWEEP","ABILITY.AEF.REFUEL_TANK_SP","ABILITY.AEF.RIFLEMAN_AT_RIFLE_GRENADE_VET","ABILITY.AEF.RIFLEMAN_FIRE_UP","ABILITY.AEF.RIFLEMAN_FIRE_UP_MP","ABILITY.AEF.RIFLEMEN_30_CALIBER_LMG","ABILITY.AEF.RIFLEMEN_DEFENSIVE","ABILITY.AEF.RIFLEMEN_DEFENSIVE_BUILDINGS","ABILITY.AEF.RIFLEMEN_FIRE_FLARES_ABILITY_MP","ABILITY.AEF.RIFLEMEN_FLAMETHROWERS","ABILITY.AEF.RIFLEMEN_FLARES","ABILITY.AEF.SHERMAN_AMMO_SWITCH_AP_SHELL_MP","ABILITY.AEF.SHERMAN_AMMO_SWITCH_HE_SHELL_MP","ABILITY.AEF.SHERMAN_BULLDOZER_CONSTRUCT_BARRIER_MP","ABILITY.AEF.SHERMAN_BULLDOZER_DESTROY_BARRIER_MP","ABILITY.AEF.SHERMAN_BULLDOZER_DISPATCH","ABILITY.AEF.SHERMAN_CALLIOPE_DISPATCH","ABILITY.AEF.SHERMAN_EASY8_DISPATCH","ABILITY.AEF.SIEGE_240MM_BARRAGE","ABILITY.AEF.SMOKE_SHERMAN_MORTAR_BARRAGE_BULLDOZER_MP","ABILITY.AEF.SMOKE_SHERMAN_MORTAR_BARRAGE_MP","ABILITY.AEF.SP_240MM_OFF_MAP_BARRAGE","ABILITY.AEF.SP_QUICK_RECON_RUN","ABILITY.AEF.SUPPORT_ARTILLERY","ABILITY.AEF.SUPPORT_ARTILLERY_DECOY","ABILITY.AEF.TANK_RIDERS_AUTO_UNLOAD_MP","ABILITY.AEF.TIME_ON_TARGET_ARTILLERY","ABILITY.AEF.USF_HOLD_FIRE_MP","ABILITY.AEF.USF_HOLD_FIRE_PACK_HOWITZER_MP","ABILITY.AEF.USF_MEDIC_HEAL_MP","ABILITY.AEF.USF_SHERMAN_BULLDOZER_HOLD_FIRE_MP","ABILITY.AEF.USF_STRAFING_RUN","ABILITY.AEF.USF_VEHICLE_HOLD_FIRE_MP","ABILITY.AEF.VEHICLE_CREW_AUTO_REPAIR","ABILITY.AEF.VEHICLE_DECREW_GENERIC_MP","ABILITY.AEF.VEHICLE_DECREW_M20_CREW_MP","ABILITY.AEF.VEHICLE_DECREW_MEDICS_MP","ABILITY.AEF.VEHICLE_DECREW_VEHICLE_CREW_MP","ABILITY.AEF.WC51_SPEED_BOOST_MP","ABILITY.AEF.WITHDRAW_AND_REFIT","UPG.AEF.ABILITY_LOCK_OUT_CAPTAIN_ABILITIES","UPG.AEF.ABILITY_LOCK_OUT_LIEUTENANT_ABILITIES","UPG.AEF.ABILITY_LOCK_OUT_PARATROOPERS_LANDED","UPG.AEF.ABILITY_REFUEL_LOCKOUT","UPG.AEF.ABILITY_TRANSFER_ORDERS_LOCK_OUT","UPG.AEF.ARTILLERY_155MM","UPG.AEF.ARTILLERY_155MM_BLIND","UPG.AEF.ARTILLERY_WHITE_PHOSPHOROUS","UPG.AEF.ASSAULT_ENGINEER_DISPATCH","UPG.AEF.ASSAULT_ENGINEER_FLAMETHROWER","UPG.AEF.BAR_UPGRADE_MP","UPG.AEF.BAZOOKA_UPGRADE_MP","UPG.AEF.CAPTAIN_BAZOOKA_UPGRADE_MP","UPG.AEF.CAPTAIN_DISPATCHED_UPGRADE_MP","UPG.AEF.COMBINED_ARMS_MP","UPG.AEF.DODGE_WC51_DISPATCH","UPG.AEF.ELITE_RIFLEMEN","UPG.AEF.ELITE_VEHICLE_CREWS","UPG.AEF.FIGHTING_POSITION_MG_ADDITION_MP","UPG.AEF.FIRE_UP_RIFLEMEN","UPG.AEF.FORWARD_OBSERVERS_UNLOCK","UPG.AEF.GREYHOUND_RECON_DISPATCH","UPG.AEF.LIEUTENANT_DISPATCHED_UPGRADE_MP","UPG.AEF.M10_DEPLOY","UPG.AEF.M20_SIDE_SKIRTS_MP","UPG.AEF.M21_MORTAR_HALFTRACK_DISPATCH","UPG.AEF.M26_PERSHING_DISPATCH","UPG.AEF.M3_HALFTRACK_GROUP","UPG.AEF.M3_REPAIR_STATION_MP","UPG.AEF.M5_HALFTRACK_DISPATCH","UPG.AEF.M8_GREYHOUND_SIDE_SKIRTS_MP","UPG.AEF.M8_TOP_GUNNER_MP","UPG.AEF.MAJOR_DISPATCHED_UPGRADE_MP","UPG.AEF.MEDIC_AUTO_HEAL_REFRESH","UPG.AEF.MINESWEEPER_UPGRADE_MP","UPG.AEF.NO_OFFICER_SPAWN_MP","UPG.AEF.OFF_SMOKE_BARRAGE","UPG.AEF.P47_RECON","UPG.AEF.P47_ROCKET_ATTACK","UPG.AEF.PARADROP_ANTI_TANK_GUN","UPG.AEF.PARADROP_MACHINE_GUN","UPG.AEF.PARADROPPED_SUPPORT_DROP","UPG.AEF.PARATROOPER_M1919A6_LMG_MP","UPG.AEF.PARATROOPER_THOMPSON_SUB_MACHINE_GUN_UPGRADE_MP","UPG.AEF.PARATROOPERS","UPG.AEF.PATHFINDERS","UPG.AEF.PATHFINDERS_RECON","UPG.AEF.PRIEST_DISPATCH","UPG.AEF.RANGER_DISPATCH","UPG.AEF.RANGER_THOMPSON_SUB_MACHINE_GUN_UPGRADE_MP","UPG.AEF.REAR_ECHELON_HACK_WITHDRAWING","UPG.AEF.RECON_SWEEP","UPG.AEF.RIFLE_COMMAND_GRENADE_MP","UPG.AEF.RIFLEMEN_30_CALIBER_LMG","UPG.AEF.RIFLEMEN_DEFENSIVE_BUILDINGS","UPG.AEF.RIFLEMEN_FLAMETHROWER","UPG.AEF.RIFLEMEN_FLAMETHROWER_UNLOCK","UPG.AEF.RIFLEMEN_FLARES","UPG.AEF.SHERMAN_BULLDOZER_DISPATCH","UPG.AEF.SHERMAN_EASY8_DISPATCH","UPG.AEF.SHERMAN_HE_ROUNDS","UPG.AEF.SHERMAN_TOP_GUNNER_MP","UPG.AEF.SIEGE_240MM_ARTILLERY","UPG.AEF.SMOKE_BARRAGE","UPG.AEF.T34_SHERMAN_CALLIOPE_DISPATCH","UPG.AEF.TECH_TREE_V1","UPG.AEF.TEMP_SPAWN_BASE_STAMP_MP","UPG.AEF.TIME_ON_TARGET_ARTILLERY","UPG.AEF.TOP_GUNNER_UPGRADED","UPG.AEF.USF_M5_HALFTRACK_72K_AA_GUN_PACKAGE_MP","UPG.AEF.USF_STRAFING_RUN","UPG.AEF.VEHICLE_CREW_THOMPSON_SUB_MACHINE_GUN_UPGRADE_MP","UPG.AEF.WEAPON_RACK_UPGRADE_MP","UPG.AEF.WITHDRAW_AND_REFIT","EBP.BRITISH.AEC_ARMOURED_CAR_MP","EBP.BRITISH.AIR_SUPPORT_OFFICER_MP","EBP.BRITISH.AVRE_VEHICLE_CREW_MP","EBP.BRITISH.BRIT_17_POUNDER_GUN_MP","EBP.BRITISH.BRIT_17_POUNDER_PIT_COMMANDER_MP","EBP.BRITISH.BRIT_17_POUNDER_PIT_MP","EBP.BRITISH.BRIT_25_POUNDER_HOWITZER_MP","EBP.BRITISH.BRIT_25_POUNDER_HOWITZER_TEMP_MP","EBP.BRITISH.BRIT_3_INCH_MORTAR_EMPLACEMENT","EBP.BRITISH.BRIT_3_INCH_MORTAR_EMPLACEMENT_COMMANDER_MP","EBP.BRITISH.BRIT_6_POUNDER_AT_GUN_MP","EBP.BRITISH.BRIT_BARBED_WIRE_FENCE_MP","EBP.BRITISH.BRIT_BOFORS_40MM_AUTOCANNON_COMMANDER_MP","EBP.BRITISH.BRIT_BOFORS_40MM_AUTOCANNON_MP","EBP.BRITISH.BRIT_EMPLACEMENT_SMALL","EBP.BRITISH.BRIT_FORWARD_HQ_COMMANDER_MP","EBP.BRITISH.BRIT_FORWARD_HQ_MP","EBP.BRITISH.BRIT_FWD_HQ_WEAPON_RACK_BREN_LMG_MP","EBP.BRITISH.BRIT_FWD_HQ_WEAPON_RACK_PIAT_LAUNCHER_MP","EBP.BRITISH.BRIT_LAND_MATTRESS_LAUNCHER_MP","EBP.BRITISH.BRIT_MEDIC_EXTRA_ENTITY_MP","EBP.BRITISH.BRIT_MEDIC_WITH_PISTOL_MP","EBP.BRITISH.BRIT_MINE_COMMANDER_MP","EBP.BRITISH.BRIT_MINE_MP","EBP.BRITISH.BRIT_RETREAT_POINT_MP","EBP.BRITISH.BRIT_SANDBAG_FENCE","EBP.BRITISH.BRIT_WEAPON_RACK_BREN_LMG_MP","EBP.BRITISH.BRIT_WEAPON_RACK_PIAT_LAUNCHER_MP","EBP.BRITISH.BRITISH_25LB_HOWITZER_GUN_CREW_MP","EBP.BRITISH.BRITISH_6LB_AT_GUN_CREW_MP","EBP.BRITISH.BRITISH_BASE_STAMPER","EBP.BRITISH.BRITISH_BUILDING_1_MP","EBP.BRITISH.BRITISH_BUILDING_1_UNBUILT_MP","EBP.BRITISH.BRITISH_BUILDING_1_WRECK_MP","EBP.BRITISH.BRITISH_BUILDING_2_MP","EBP.BRITISH.BRITISH_BUILDING_2_UNBUILT_MP","EBP.BRITISH.BRITISH_BUILDING_2_WRECK_MP","EBP.BRITISH.BRITISH_BUNKER_STARTING_POSITION_MP","EBP.BRITISH.BRITISH_HMG_PLANE","EBP.BRITISH.BRITISH_HMG_TEAM_CREW_MP","EBP.BRITISH.BRITISH_HQ_SANDBAGS_01_MP","EBP.BRITISH.BRITISH_HQ_TRUCK_MP","EBP.BRITISH.BRITISH_HQ_TRUCK_WRECK_MP","EBP.BRITISH.BRITISH_LAND_MATTRESS_TEAM_CREW_MP","EBP.BRITISH.BRITISH_MACHINE_GUN_MP","EBP.BRITISH.BRITISH_MORTAR_TEAM_CREW_MP","EBP.BRITISH.BRITISH_RADIO_BEACON","EBP.BRITISH.BRITISH_SANDBAG_FENCE_MP","EBP.BRITISH.CENTAUR_AA_MK2_MP","EBP.BRITISH.CHURCHILL_AVRE_MP","EBP.BRITISH.CHURCHILL_CROCODILE_MP","EBP.BRITISH.CHURCHILL_DEFAULT_MP","EBP.BRITISH.COMET_MP","EBP.BRITISH.COMMANDO_AIR_LANDING_MP","EBP.BRITISH.COMMANDO_MP","EBP.BRITISH.COMMANDO_PIAT_MP","EBP.BRITISH.CROMWELL_MK4_75MM_MP","EBP.BRITISH.FIELD_HOSPITAL_MEDIC_MP","EBP.BRITISH.FORWARD_OBSERVATION_OFFICER_MP","EBP.BRITISH.GLIDER_COMMANDOS_ONLY","EBP.BRITISH.GLIDER_HEADQUARTERS","EBP.BRITISH.HQ_FIELD_ARTILLERY_MP","EBP.BRITISH.INVISIBLE_FLAME_MORTAR_ICON","EBP.BRITISH.M3_HALFTRACK_RESUPPLY_MP","EBP.BRITISH.PARATROOPERS_PLANE_ATGUN_MATT_TEST_MP","EBP.BRITISH.PARATROOPERS_PLANE_VICKERS_MATT_TEST_MP","EBP.BRITISH.RECON_HAWKER_TYPHOON_ASSAULT_MP","EBP.BRITISH.RECON_HAWKER_TYPHOON_MP","EBP.BRITISH.REPAIR_SAPPER_MP","EBP.BRITISH.ROCKET_HAWKER_TYPHOON_MP","EBP.BRITISH.SAPPER_MP","EBP.BRITISH.SAPPER_RECOVERY_MP","EBP.BRITISH.SEXTON_SPG_MP","EBP.BRITISH.SHERMAN_FIREFLY_M4A2_MP","EBP.BRITISH.SLIT_TRENCH_MP","EBP.BRITISH.SNIPER_BRITISH_MP","EBP.BRITISH.SPITFIRE_RECON_PLANE","EBP.BRITISH.STRAFE_HAWKER_TYPHOON_MP","EBP.BRITISH.TOMMY_MP","EBP.BRITISH.TOMMY_RECON_MP","EBP.BRITISH.UNIVERSAL_CARRIER_MP","EBP.BRITISH.UNIVERSAL_CARRIER_RESUPPLY_MP","EBP.BRITISH.VALENTINE_MORTAR","EBP.BRITISH.VALENTINE_OBSERVATION_MP","EBP.BRITISH.VEHICLE_CREW_MP","SBP.BRITISH.AEC_ARMOURED_CAR_SQUAD_MP","SBP.BRITISH.AIR_SUPPORT_OFFICER_SQUAD_MP","SBP.BRITISH.AVRE_VEHICLE_CREW_SQUAD_MP","SBP.BRITISH.BRIT_17_POUNDER_AT_GUN_SQUAD_COMMANDER_MP","SBP.BRITISH.BRIT_17_POUNDER_AT_GUN_SQUAD_MP","SBP.BRITISH.BRIT_25_POUNDER_HOWITZER_SQUAD_MP","SBP.BRITISH.BRIT_25_POUNDER_HOWITZER_SQUAD_TEMP_MP","SBP.BRITISH.BRIT_3_INCH_MORTAR_TEAM_COMMANDER_MP","SBP.BRITISH.BRIT_3_INCH_MORTAR_TEAM_MP","SBP.BRITISH.BRIT_6_POUNDER_AT_GUN_SQUAD_MP","SBP.BRITISH.BRIT_BOFORS_40MM_AUTOCANNON_SQUAD_COMMANDER_MP","SBP.BRITISH.BRIT_BOFORS_40MM_AUTOCANNON_SQUAD_MP","SBP.BRITISH.BRIT_BREN_LMG_WEAPON_RACK_UI_FAKE_MP","SBP.BRITISH.BRIT_FORWARD_HQ_MP","SBP.BRITISH.BRIT_LAND_MATTRESS_LAUNCHER_SQUAD_MP","SBP.BRITISH.BRIT_MEDIC_SQUAD_MP","SBP.BRITISH.BRIT_PIAT_LAUNCHER_WEAPON_RACK_UI_FAKE_MP","SBP.BRITISH.BRITISH_CARGO_PLANE","SBP.BRITISH.BRITISH_HMG_PLANE","SBP.BRITISH.BRITISH_MACHINE_GUN_SQUAD_MP","SBP.BRITISH.CENTAUR_AA_MK2_SQUAD_MP","SBP.BRITISH.CHURCHILL_AVRE_SQUAD_MP","SBP.BRITISH.CHURCHILL_CROCODILE_MP","SBP.BRITISH.CHURCHILL_DEFAULT_SQUAD_MP","SBP.BRITISH.COMET_TANK_SQUAD_MP","SBP.BRITISH.COMMANDO_SQUAD_MP","SBP.BRITISH.COMMANDO_SQUAD_PIAT_MP","SBP.BRITISH.CROMWELL_MK4_75MM_SQUAD_MP","SBP.BRITISH.FORWARD_HQ_MP","SBP.BRITISH.FORWARD_OBSERVATION_SQUAD_MP","SBP.BRITISH.GLIDER_COMMANDOS_ONLY_MP","SBP.BRITISH.GLIDER_HEADQUARTERS_MP","SBP.BRITISH.INFILTRATION_COMMANDO_SQUAD_MP","SBP.BRITISH.M3_HALFTRACK_SQUAD__RESUPPLY_MP","SBP.BRITISH.PARATROOPERS_PLANE_ATGUN_BRITISH_MATT_TEST_MP","SBP.BRITISH.PARATROOPERS_PLANE_VICKERS_BRITISH_MATT_TEST_MP","SBP.BRITISH.RECON_HAWKER_TYPHOON_ASSAULT_MP","SBP.BRITISH.RECON_HAWKER_TYPHOON_MP","SBP.BRITISH.ROCKET_HAWKER_TYPHOON_MP","SBP.BRITISH.SAPPER_SQUAD_DEMOLITION_MP","SBP.BRITISH.SAPPER_SQUAD_MP","SBP.BRITISH.SAPPER_SQUAD_RECOVERY_MP","SBP.BRITISH.SEXTON_SPG_SQUAD_MP","SBP.BRITISH.SHERMAN_FIREFLY_SQUAD_MP","SBP.BRITISH.SNIPER_BRITISH_SQUAD_MP","SBP.BRITISH.SPITFIRE_RECON_PLANE","SBP.BRITISH.STRAFE_HAWKER_TYPHOON_MP","SBP.BRITISH.TOMMY_SQUAD_FLAME_MP","SBP.BRITISH.TOMMY_SQUAD_MP","SBP.BRITISH.TOMMY_SQUAD_RECON_MP","SBP.BRITISH.TOMMY_SQUAD_TANK_HUNTER_MP","SBP.BRITISH.UNIVERSAL_CARRIER_RESUPPLY","SBP.BRITISH.UNIVERSAL_CARRIER_SQUAD_MP","SBP.BRITISH.VALENTINE_MORTAR","SBP.BRITISH.VALENTINE_OBSERVATION_MP","SBP.BRITISH.VEHICLE_CREW_STANDARD_SQUAD_MP","ABILITY.BRITISH.ADVANCED_ASSEMBLY","ABILITY.BRITISH.ADVANCED_COVER_COMBAT","ABILITY.BRITISH.AEC_DEFENSIVE_SMOKE","ABILITY.BRITISH.AEC_TREAD_SHOTS_MP","ABILITY.BRITISH.ALLIED_STRATEGIC_BOMBING","ABILITY.BRITISH.ARTILLERY_COVER","ABILITY.BRITISH.ASSAULT","ABILITY.BRITISH.ASSAULT_GRENADES","ABILITY.BRITISH.AT_GUN_AIRDROP","ABILITY.BRITISH.AVRE_CREW_DEMOLITION_CHARGE_MP","ABILITY.BRITISH.AVRE_CREW_SHRAPNELL_GRENADE_MP","ABILITY.BRITISH.AVRE_SPIGOT_MORTAR_ATTACK_MP","ABILITY.BRITISH.AVRE_SPIGOT_MORTAR_ATTACK_VET_3_MP","ABILITY.BRITISH.AVRE_SPIGOT_MORTAR_RELOAD_MP","ABILITY.BRITISH.AVRE_VEHICLE_DECREW_VEHICLE_CREW_MP","ABILITY.BRITISH.BOFORS_SUPPRESSIVE_BARRAGE_ABILITY_MP","ABILITY.BRITISH.BOFORS_SUPPRESSIVE_BARRAGE_ABILITY_VICTOR_TARGET_MP","ABILITY.BRITISH.BREAKTHROUGH_OPERATION","ABILITY.BRITISH.BRIT_17_POUNDER_FACING_ORDER_MP","ABILITY.BRITISH.BRIT_17_POUNDER_FLARES_ABILITY_MP","ABILITY.BRITISH.BRIT_17_POUNDER_PIERCING_SHELL_ABILITY_MP","ABILITY.BRITISH.BRIT_17_POUNDER_PIERCING_SHELL_ABILITY_VICTOR_TARGET_MP","ABILITY.BRITISH.BRIT_3_INCH_MORTAR_EMPLACEMENT_BARRAGE_MP","ABILITY.BRITISH.BRIT_3_INCH_MORTAR_EMPLACEMENT_BARRAGE_VICTOR_TARGET_MP","ABILITY.BRITISH.BRIT_3_INCH_MORTAR_EMPLACEMENT_SMOKE_BARRAGE_MP","ABILITY.BRITISH.BRIT_6_POUNDER_CRITICAL_SHOT_MP","ABILITY.BRITISH.BRIT_6_POUNDER_RAPID_MANEUVER_MP","ABILITY.BRITISH.BRIT_BARBED_WIRE_CUTTING_ABILITY_MP","ABILITY.BRITISH.BRIT_BASE_BRACED_STATIC_MP","ABILITY.BRITISH.BRIT_BASE_BUILDING_BRACED_OFF_MP","ABILITY.BRITISH.BRIT_BASE_BUILDING_BRACED_ON_MP","ABILITY.BRITISH.BRIT_EMPLACEMENT_BRACED_MP","ABILITY.BRITISH.BRIT_HQ_ENGINEER_CALL_IN","ABILITY.BRITISH.BRIT_MEDIC_HEAL_MP","ABILITY.BRITISH.BRIT_MEDIC_SQUAD_AUTO_HEAL","ABILITY.BRITISH.BRIT_MEDIC_TOMMY_TIMED_AREA_HEAL_MP","ABILITY.BRITISH.BRIT_MORTAR_EMPLACEMENT_HOLD_FIRE","ABILITY.BRITISH.BRIT_RADAR_SWEEP","ABILITY.BRITISH.BRIT_REPAIR_ABILITY_SAPPERS_MP","ABILITY.BRITISH.BRIT_REPAIR_ABILITY_TOMMYS_MP","ABILITY.BRITISH.BRIT_REPAIR_EWS_ABILITY_SAPPERS_MP","ABILITY.BRITISH.BRIT_SNIPER_DELAYED_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.BRITISH.BRIT_TUNE_UP","ABILITY.BRITISH.BRIT_VEHICLE_HOLD_FIRE_MP","ABILITY.BRITISH.BRITISH_HOLD_THE_LINE","ABILITY.BRITISH.BRITISH_MORTAR_HOLD_FIRE_MP","ABILITY.BRITISH.CENTAUR_20MM_BARRAGE_MP","ABILITY.BRITISH.CENTAUR_AA_MODE_MP","ABILITY.BRITISH.CENTAUR_WEAPON_BURST_MP","ABILITY.BRITISH.CENTAUR_WEAPON_BURST_TEST_MP","ABILITY.BRITISH.CHURCHILL_AVRE","ABILITY.BRITISH.CHURCHILL_CREW_GRENADE_TARGETED","ABILITY.BRITISH.CHURCHILL_CROC_FLAME_BURST_MP","ABILITY.BRITISH.CHURCHILL_CROCODILE","ABILITY.BRITISH.CHURCHILL_INF_SUPPORT_SMOKE","ABILITY.BRITISH.COMET_CREW_GRENADE_TARGETED","ABILITY.BRITISH.COMET_SMOKE_SHELL_SHOT_MP","ABILITY.BRITISH.COMET_SMOKE_SHELL_SHOT_WP_MP","ABILITY.BRITISH.COMMAND_HQ","ABILITY.BRITISH.COMMAND_HQ_HE_ARTILLERY","ABILITY.BRITISH.COMMAND_HQ_RECON_PLANE","ABILITY.BRITISH.COMMAND_HQ_SMOKE_ARTILLERY","ABILITY.BRITISH.COMMAND_HQ_STRAFE_PLANE","ABILITY.BRITISH.COMMAND_VEHICLE","ABILITY.BRITISH.COMMAND_VEHICLE_PLANE","ABILITY.BRITISH.COMMANDO_ASSASSINATE_MP","ABILITY.BRITISH.COMMANDO_DEMO_MP","ABILITY.BRITISH.COMMANDO_INFILTRATION_CAMOUFLAGE_MP","ABILITY.BRITISH.COUNTER_BATTERY","ABILITY.BRITISH.COUNTER_BATTERYS","ABILITY.BRITISH.COVER_SMOKE_GRENADES","ABILITY.BRITISH.CREW_REPAIR","ABILITY.BRITISH.CREW_REPAIR_OPERATION","ABILITY.BRITISH.CROMWELL_SMOKE_SHELL_SHOT_MP","ABILITY.BRITISH.DEFENSIVE_OPERATIONS","ABILITY.BRITISH.DESTROY_COVER_MP","ABILITY.BRITISH.DIRECT_BARRAGE","ABILITY.BRITISH.EARLY_WARNING","ABILITY.BRITISH.ENGINEER_COVER_COMBAT_BONUS","ABILITY.BRITISH.FATALITY_BURN_THEM_OUT","ABILITY.BRITISH.FATALITY_MIGHT_OF_THE_AIR_FORCES","ABILITY.BRITISH.FATALITY_ZEROING_STRIKE","ABILITY.BRITISH.FIELD_RECOVERY","ABILITY.BRITISH.FIRE_SUPPORT_TEAM","ABILITY.BRITISH.FIREFLY_TULIP_ROCKET_BARRAGE_MP","ABILITY.BRITISH.FIREFLY_TULIP_ROCKET_BARRAGE_SKILL_SHOT_MP","ABILITY.BRITISH.FORTIFY_OUR_POSITION","ABILITY.BRITISH.FORWARD_HQ_RETREAT_POINT_GLIDER_MP","ABILITY.BRITISH.FORWARD_HQ_RETREAT_POINT_MP","ABILITY.BRITISH.GLIDER_COMMANDOS_ONLY","ABILITY.BRITISH.GLIDER_HEADQUARTERS","ABILITY.BRITISH.GLIDER_RETREAT_POINT_MP","ABILITY.BRITISH.HOWITZER_COUNTER_BARRAGE_ATTACK_COMMANDER_MP","ABILITY.BRITISH.HOWITZER_COUNTER_BARRAGE_COMMANDER_MP","ABILITY.BRITISH.HQ_BUILD_ANVIL_1_MP","ABILITY.BRITISH.HQ_BUILD_ANVIL_2_MP","ABILITY.BRITISH.IMPROVED_FORTIFCATIONS","ABILITY.BRITISH.INFANTRY_RECON_TACTICS","ABILITY.BRITISH.INFANTRY_SMOKE_GRENADE_RESPOSITION","ABILITY.BRITISH.INFILTRATION_COMMANDOS","ABILITY.BRITISH.LAND_MATTRESS","ABILITY.BRITISH.LAND_MATTRESS_25LB_ROCKET","ABILITY.BRITISH.LAND_MATTRESS_60LB_ROCKET","ABILITY.BRITISH.LAND_MATTRESS_BARRAGE","ABILITY.BRITISH.LAND_MATTRESS_BARRAGE_SMOKE","ABILITY.BRITISH.LAND_MATTRESS_BARRAGE_VICTOR_TARGET_MP","ABILITY.BRITISH.LAND_MATTRESS_FIRE_ALL","ABILITY.BRITISH.LAND_MATTRESS_LOAD_ROCKETS_MP","ABILITY.BRITISH.LAND_MATTRESS_PHOSPHORUS_ROCKET","ABILITY.BRITISH.MEDIC_AUTO_HEAL_MP","ABILITY.BRITISH.MORTAR_ARTILLERY","ABILITY.BRITISH.MORTAR_FIRE_ARTILLERY","ABILITY.BRITISH.MORTAR_PIT_COUNTER_BATTERY_MP","ABILITY.BRITISH.OBSERVATION_MODE","ABILITY.BRITISH.OBSERVATION_VALENTINE","ABILITY.BRITISH.OFFICER_ARTILLERY","ABILITY.BRITISH.OFFICER_ARTILLERY_SEXTON_VICTOR_TARGET_AIRBURST_BARRAGE_MP","ABILITY.BRITISH.OFFICER_ARTILLERY_SEXTON_VICTOR_TARGET_CONCENTRATION_BARRAGE_MP","ABILITY.BRITISH.OFFICER_CHARGE_MP","ABILITY.BRITISH.OFFICER_RECON_SWEEP","ABILITY.BRITISH.PASSIVE_17_POUNDER_EMPLACEMENT_MP","ABILITY.BRITISH.PASSIVE_BOFORS_EMPLACEMENT_MP","ABILITY.BRITISH.PASSIVE_MORTAR_EMPLACEMENT_MP","ABILITY.BRITISH.PEPPER_POT","ABILITY.BRITISH.PERCISION_BARRAGE","ABILITY.BRITISH.PIAT_DEPLOY_MP","ABILITY.BRITISH.QF_25_PDR_FLARE_BARRAGE_ABILITY_MP","ABILITY.BRITISH.QF_25LB_ANTITANK_ABILITY_BASE_MP","ABILITY.BRITISH.QF_25LB_ANTITANK_ABILITY_MP","ABILITY.BRITISH.QF_25LB_BARRAGE_ABILITY_BASE_MP","ABILITY.BRITISH.QF_25LB_BARRAGE_ABILITY_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_BASE_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_BASE_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_FWD_HQ_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_OFFICER_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_SNIPER_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_FIRE_ORDER_VALENTINE_MP","ABILITY.BRITISH.QF_25LB_COORDINATED_SMOKE_SCREEN_BASE_MATT_TEST_VICTOR_TARGET_MP","ABILITY.BRITISH.QF_25LB_CREEPING_SMOKE_BARRAGE_ABILITY_BASE_MP","ABILITY.BRITISH.QF_25LB_CREEPING_SMOKE_BARRAGE_ABILITY_MP","ABILITY.BRITISH.QF_25LB_DIRECT_BARRAGE_BASE_MP","ABILITY.BRITISH.QF_25LB_OVERWATCH_BASE_MP","ABILITY.BRITISH.QF_25LB_RAPID_RESPONSE_BARRAGE_BASE_MP","ABILITY.BRITISH.QF_25LB_RAPID_RESPONSE_BARRAGE_MP","ABILITY.BRITISH.QF_25LB_SMOKE_SCREEN_BASE_MP","ABILITY.BRITISH.RAPID_ADVANCE","ABILITY.BRITISH.RAPID_RESPONSE_ARTILLERY","ABILITY.BRITISH.RECON_SECTION_SPRINT_MP","ABILITY.BRITISH.REINFORCE_THE_FRONT","ABILITY.BRITISH.REINFORCED_STRUCTURES","ABILITY.BRITISH.SAPPER_ANVIL_BOOBY_TRAP","ABILITY.BRITISH.SAPPER_FLAMETHROWERS","ABILITY.BRITISH.SAPPER_GAMMON_BOMB_MEDIUM_MP","ABILITY.BRITISH.SAPPER_SALVAGE_WRECK","ABILITY.BRITISH.SEXTON_ARTILLERY_BARRAGE_CREEPING_VICTOR_TARGET_MP","ABILITY.BRITISH.SEXTON_DISPATCH_BRITISH","ABILITY.BRITISH.SEXTON_SPG_25_CONCENTRATION_BARRAGE_MP","ABILITY.BRITISH.SEXTON_SPG_25_PDR_ARTILLERY_CREEPING_BARRAGE_MP","ABILITY.BRITISH.SEXTON_SPG_25_PDR_BARRAGE_ABILITY_MP","ABILITY.BRITISH.SEXTON_SPG_25_PDR_SUPERCHARGE_AIRBURST_BARRAGE_ABILITY_MP","ABILITY.BRITISH.SEXTON_SPG_25_PDR_SUPERCHARGE_BARRAGE_ABILITY_MP","ABILITY.BRITISH.SMOKE_ASSAULT","ABILITY.BRITISH.SNIPER_BOYS_ANTI_TANK_CRITICAL_SHOT_MP","ABILITY.BRITISH.STAND_FAST","ABILITY.BRITISH.STRAFING_RUN","ABILITY.BRITISH.SUPER_OVERWATCH_TEST","ABILITY.BRITISH.TANK_HUNTER","ABILITY.BRITISH.TOMMY_COVER_COMBAT_BONUS","ABILITY.BRITISH.TOMMY_GAMMON_BOMB_HEAVY_MP","ABILITY.BRITISH.TOMMY_GAMMON_BOMB_MEDIUM_MP","ABILITY.BRITISH.TOMMY_HEAT_GRENADE_MP","ABILITY.BRITISH.TOMMY_MILLS_BOMB_MP","ABILITY.BRITISH.TOMMY_OFFICER_ARTILLERY","ABILITY.BRITISH.TOMMY_STAND_YOUR_GROUND","ABILITY.BRITISH.TUNE_UP_BONUS_MP","ABILITY.BRITISH.UEC_SELF_REPAIR","ABILITY.BRITISH.UEC_SELF_REPAIR_IMPROVED","ABILITY.BRITISH.UNIVERSAL_CARRIER_DROP_LMG","ABILITY.BRITISH.UNIVERSAL_CARRIER_DROP_PIAT","ABILITY.BRITISH.UNIVERSAL_CARRIER_VICKERS_SUPPRESSION_MP","ABILITY.BRITISH.VALENTINE_ARTILLERY_SEXTON_VICTOR_TARGET_CONCENTRATION_BARRAGE_MP","ABILITY.BRITISH.VALENTINE_SMOKE_BARRAGE_MP","ABILITY.BRITISH.VICKERS_AIRDROP","ABILITY.BRITISH.VICKERS_HMG_VET_1_BONUS","UPG.BRITISH.ABILITY_LOCK_OUT_17_POUNDER_ABILITY_ACTIVE","UPG.BRITISH.ABILITY_LOCK_OUT_AVRE_NOT_RELOADED","UPG.BRITISH.ABILITY_LOCK_OUT_AVRE_RELOADING","UPG.BRITISH.ABILITY_LOCK_OUT_BASE_ARTILLERY_COUNTER_BARRAGE_ABILITY_ACTIVE","UPG.BRITISH.ABILITY_LOCK_OUT_BASE_ARTILLERY_OVERWATCH_ABILITY_ACTIVE","UPG.BRITISH.ABILITY_LOCK_OUT_BOFORS_EMPLACEMENT_AA_MODE_ENABLED","UPG.BRITISH.ABILITY_LOCK_OUT_BOFORS_EMPLACEMENT_BARRAGE_ACTIVE","UPG.BRITISH.ABILITY_LOCK_OUT_GLIDER_CUSTOM_LOADOUT_LAUNCH_AVAILABLE","UPG.BRITISH.ABILITY_LOCK_OUT_GLIDER_HARD_LANDED","UPG.BRITISH.ABILITY_LOCK_OUT_GLIDER_NOT_STOPPED","UPG.BRITISH.ABILITY_LOCK_OUT_MORTAR_EMPLACEMENT_BARRAGE_ACTIVE","UPG.BRITISH.ABILITY_LOCK_OUT_MORTAR_EMPLACEMENT_SLOT_1_DEFAULT_LOADED","UPG.BRITISH.ABILITY_LOCK_OUT_MORTAR_EMPLACEMENT_SLOT_1_SPECIAL_1_LOADED","UPG.BRITISH.ABILITY_LOCK_OUT_MORTAR_EMPLACEMENT_SLOT_1_SPECIAL_2_LOADED","UPG.BRITISH.ADVANCED_ASSEMBLY","UPG.BRITISH.ADVANCED_ASSEMBLY_RESEARCH","UPG.BRITISH.ADVANCED_COVER","UPG.BRITISH.AEC_HE_ROUNDS_MP","UPG.BRITISH.AEC_HE_ROUNDS_UNLOCK_MP","UPG.BRITISH.AEC_RAPID_FIRE_MP","UPG.BRITISH.AEC_TARGET_OPTICS_MP","UPG.BRITISH.AEC_TARGET_TURRET_MP","UPG.BRITISH.AEC_TREAD_FIRST_SHOT_MP","UPG.BRITISH.AEC_TREAD_SECOND_SHOT_MP","UPG.BRITISH.ARTY_PIT_LOCKOUT_UPGRADE","UPG.BRITISH.ASSAULT","UPG.BRITISH.ASSAULT_ACTIVE","UPG.BRITISH.AVRE_MORTAR_RELOAD","UPG.BRITISH.BASE_BUILDING_BRACED_MP","UPG.BRITISH.BOYS_AT_RIFLE","UPG.BRITISH.BREN_LMG_UNLOCK_MP","UPG.BRITISH.BRITISH_TANK_COMMANDER","UPG.BRITISH.CAN_TUNE_UP_MP","UPG.BRITISH.COMMAND_HQ","UPG.BRITISH.COMMAND_VEHICLE","UPG.BRITISH.COMMAND_VEHICLE_ACTIVE","UPG.BRITISH.COMMAND_VEHICLE_ACTIVE_PLAYER","UPG.BRITISH.COMMANDO_RETREAT_SMOKE_DELAY","UPG.BRITISH.COMPANY_ANVIL_BUILDING_MP","UPG.BRITISH.COMPANY_ANVIL_MP","UPG.BRITISH.COMPANY_ANVIL_POINT_SIGHT_MP","UPG.BRITISH.COMPANY_HAMMER_BUILDING_MP","UPG.BRITISH.COMPANY_HAMMER_MP","UPG.BRITISH.COUNTER_BATTERY","UPG.BRITISH.COUNTER_BATTERY_MP","UPG.BRITISH.DEFENSIVE_OPERATIONS","UPG.BRITISH.EMPLACEMENT_DEACTIVATE_BRACE_DELAY","UPG.BRITISH.FIREFLY_TULIP_RELOAD","UPG.BRITISH.FIREFLY_TULIP_ROCKET","UPG.BRITISH.FLAMETHROWERS","UPG.BRITISH.FWD_HQ_RETREAT_MP","UPG.BRITISH.IMPROVED_FORTIFCATION","UPG.BRITISH.IMPROVED_FORTIFCATION_ASSSEMBLY_SQUAD","UPG.BRITISH.IMPROVED_FORTIFCATION_SQUAD","UPG.BRITISH.INFILTRATION_COMMANDOS","UPG.BRITISH.LAND_MATTRESS_FIRING","UPG.BRITISH.LAND_MATTRESS_LOADED_ROCKET","UPG.BRITISH.LAND_MATTRESS_LOADING_25LB_ROCKET","UPG.BRITISH.LAND_MATTRESS_LOADING_60LB_ROCKET","UPG.BRITISH.LAND_MATTRESS_LOADING_PHOS_ROCKET","UPG.BRITISH.PIAT","UPG.BRITISH.PIAT_UNLOCK_MP","UPG.BRITISH.PLATOON_AEC_RESEARCH_BUILDING_MP","UPG.BRITISH.PLATOON_AEC_RESEARCH_MP","UPG.BRITISH.PLATOON_BOFORS_RESEARCH_BUILDING_MP","UPG.BRITISH.PLATOON_BOFORS_RESEARCH_MP","UPG.BRITISH.PRECISION_BARRAGE","UPG.BRITISH.QF_25LB_COORDINATED_FIRE_MP","UPG.BRITISH.QF_25LB_COUNTER_BATTERY_MP","UPG.BRITISH.QF_25LB_FIRE_SUPPORT_BASE_MP","UPG.BRITISH.QF_25LB_FIRE_SUPPORT_MP","UPG.BRITISH.QF_25LB_HE_SHELL_MP","UPG.BRITISH.QF_25LB_RAPID_RESPONSE_DELAY_MP","UPG.BRITISH.QF_25LB_RAPID_RESPONSE_MP","UPG.BRITISH.QF_25LB_SHELL_TOGGLE_ABILITY_DELAY_MP","UPG.BRITISH.QF_25LB_TACTICAL_SUPPORT_BASE_MP","UPG.BRITISH.QF_25LB_TACTICAL_SUPPORT_MP","UPG.BRITISH.QF_25LB_TARGET_ACQUISITION_MP","UPG.BRITISH.REINFORCED_STRUCTURE","UPG.BRITISH.SAPPER_FLAMETHROWER","UPG.BRITISH.SAPPERS_HEAVY_SQUAD_MP","UPG.BRITISH.SAPPERS_MINESWEEPER_UPGRADE_MP","UPG.BRITISH.SNIPER_BOYS_AT_RIFLE","UPG.BRITISH.TANK_HUNTER_ACTIVE","UPG.BRITISH.TECH_STRUCTURE_1_CONSTRUCT_MP","UPG.BRITISH.TECH_STRUCTURE_1_MP","UPG.BRITISH.TECH_STRUCTURE_2_CONSTRUCT_MP","UPG.BRITISH.TECH_STRUCTURE_2_MP","UPG.BRITISH.TOMMY_BOYS_AT_RIFLES","UPG.BRITISH.TOMMY_INCREASED_SQUAD_SIZE_MP","UPG.BRITISH.TOMMY_MEDICAL_SUPPLIES","UPG.BRITISH.TOMMY_MILLS_BOMB_MP","UPG.BRITISH.TOMMY_PYROTECHNICS_SUPPLIES","UPG.BRITISH.UNIVERSAL_CARRIER_VICKERS_K_PACKAGE_UPGRADE_MP","UPG.BRITISH.UNIVERSAL_CARRIER_WASP_PACKAGE_UPGRADE_MP","UPG.BRITISH.VALENTINE_OBSERVATION_MODE_MP","UPG.BRITISH.WEAPON_RACK_UNLOCK_MP","EBP.GERMAN.ANTITANK_88MM_PAK43_SANDBAGS","EBP.GERMAN.ARMORED_CAR_SDKFZ_222","EBP.GERMAN.ARMORED_CAR_SDKFZ_222_MP","EBP.GERMAN.ASSAULT_GRENADIERS_LEADER_MP","EBP.GERMAN.ASSAULT_GRENADIERS_MP","EBP.GERMAN.ASSAULT_OFFICER","EBP.GERMAN.ASSAULT_OFFICER_GRENADIERS_BODYGUARD_MP","EBP.GERMAN.ASSAULT_OFFICER_MP","EBP.GERMAN.ATGUN_CREW","EBP.GERMAN.ATGUN_CREW_MP","EBP.GERMAN.ATGUN88_CREW","EBP.GERMAN.ATGUN88_CREW_MP","EBP.GERMAN.AXIS_BUNKER_STARTING_POSITION","EBP.GERMAN.AXIS_BUNKER_STARTING_POSITION_MP","EBP.GERMAN.BEREICH_FESTUNG","EBP.GERMAN.BEREICH_FESTUNG_MP","EBP.GERMAN.BRUMMBAR_STURMPANZER_IV_SDKFZ_166","EBP.GERMAN.BRUMMBAR_STURMPANZER_IV_SDKFZ_166_MP","EBP.GERMAN.BUNKER","EBP.GERMAN.BUNKER_MP","EBP.GERMAN.BUNKER_OF_DEATH_MP","EBP.GERMAN.CARGO_PLANE","EBP.GERMAN.CARGO_PLANE_1","EBP.GERMAN.CARGO_PLANE_FUEL","EBP.GERMAN.CARGO_PLANE_MUNITIONS","EBP.GERMAN.DOLCH_AKTIONEN","EBP.GERMAN.DOLCH_AKTIONEN_MP","EBP.GERMAN.ELEFANT_SDKFZ_184","EBP.GERMAN.ELEFANT_SDKFZ_184_MP","EBP.GERMAN.FUEL_POST_GERMAN","EBP.GERMAN.FUEL_POST_GERMAN_MP","EBP.GERMAN.GERMAN_BASE_STAMPER","EBP.GERMAN.GERMAN_HQ","EBP.GERMAN.GERMAN_HQ_MP","EBP.GERMAN.GERMAN_HQ_WRECK","EBP.GERMAN.GERMAN_HQ_WRECK_MP","EBP.GERMAN.GERMAN_MEDIC","EBP.GERMAN.GERMAN_MEDIC_MP","EBP.GERMAN.GERMAN_MINE","EBP.GERMAN.GERMAN_MINE_MP","EBP.GERMAN.GERMAN_SANDBAG_FENCE","EBP.GERMAN.GRANATEWERFER_34_81MM_MORTAR","EBP.GERMAN.GRANATEWERFER_34_81MM_MORTAR_MP","EBP.GERMAN.GRENADIERS","EBP.GERMAN.GRENADIERS_MP","EBP.GERMAN.GRENADIERS_SP","EBP.GERMAN.HACK_INVISI_PIONEER_MP","EBP.GERMAN.HALFTRACK_RIEGEL_43_MINE_MP","EBP.GERMAN.HALFTRACK_SDKFZ_251","EBP.GERMAN.HALFTRACK_SDKFZ_251_MP","EBP.GERMAN.HINTERE_PANZERWERK","EBP.GERMAN.HINTERE_PANZERWERK_MP","EBP.GERMAN.HINTERE_PANZERWERK_VORONEZH","EBP.GERMAN.HOWITZER_105MM_DUMMY","EBP.GERMAN.HOWITZER_105MM_LE_FH18","EBP.GERMAN.HOWITZER_105MM_LE_FH18_MP","EBP.GERMAN.HOWITZER_CREW","EBP.GERMAN.HOWITZER_CREW_MP","EBP.GERMAN.HULLDOWN_SANDBAG_WALL","EBP.GERMAN.HULLDOWN_SANDBAG_WALL_MP","EBP.GERMAN.INVISIBLE_RETREAT_POINT","EBP.GERMAN.LUFTWAFFE_OFFICER_TOW","EBP.GERMAN.M01_STUKA_DOGFIGHT","EBP.GERMAN.M01_STUKA_GROUND_ATTACK_FAST","EBP.GERMAN.MECHANIZED_250_HALFTRACK_GRENADIER_MP","EBP.GERMAN.MECHANIZED_250_HALFTRACK_MP","EBP.GERMAN.MG42_CREW","EBP.GERMAN.MG42_CREW_MP","EBP.GERMAN.MG42_CREW_SINGLE","EBP.GERMAN.MG42_HMG","EBP.GERMAN.MG42_HMG_ATTACK_GROUND","EBP.GERMAN.MG42_HMG_MP","EBP.GERMAN.MINE_FIELD","EBP.GERMAN.MINE_FIELD_BORDER","EBP.GERMAN.MINE_FIELD_BORDER_MP","EBP.GERMAN.MINE_FIELD_MINE","EBP.GERMAN.MINE_FIELD_MINE_M03","EBP.GERMAN.MINE_FIELD_MINE_MP","EBP.GERMAN.MINE_FIELD_MINE_TOW","EBP.GERMAN.MINE_FIELD_MP","EBP.GERMAN.MORTAR_CREW","EBP.GERMAN.MORTAR_CREW_MP","EBP.GERMAN.MORTAR_LIGHT_HALFTRACK_250_7","EBP.GERMAN.MORTAR_LIGHT_HALFTRACK_250_7_MP","EBP.GERMAN.MUNITION_POST_GERMAN","EBP.GERMAN.MUNITION_POST_GERMAN_MP","EBP.GERMAN.OFFICER","EBP.GERMAN.OFFICER_MP","EBP.GERMAN.OFFICER_TOW_OCCUPATION","EBP.GERMAN.OPEL_BLITZ_SUPPLY_TRUCK_MP","EBP.GERMAN.OPEL_BLITZ_TRUCK","EBP.GERMAN.OSTRUPPEN_SOLDIER","EBP.GERMAN.OSTRUPPEN_SOLDIER_MP","EBP.GERMAN.OSTWIND_FLAK_PANZER","EBP.GERMAN.OSTWIND_FLAK_PANZER_MP","EBP.GERMAN.PAK40_75MM_AT_GUN","EBP.GERMAN.PAK40_75MM_AT_GUN_MP","EBP.GERMAN.PAK43_88MM_AT_GUN","EBP.GERMAN.PAK43_88MM_AT_GUN_MP","EBP.GERMAN.PANTHER_SDKFZ_171","EBP.GERMAN.PANTHER_SDKFZ_171_MP","EBP.GERMAN.PANZER_GRENADIERS","EBP.GERMAN.PANZER_GRENADIERS_MP","EBP.GERMAN.PANZER_III_MP","EBP.GERMAN.PANZER_IV_COMMANDER_SDKFZ_161","EBP.GERMAN.PANZER_IV_COMMANDER_SDKFZ_161_MP","EBP.GERMAN.PANZER_IV_SDKFZ_161","EBP.GERMAN.PANZER_IV_SDKFZ_161_MP","EBP.GERMAN.PANZER_IV_SDKFZ_161_TUTORIAL","EBP.GERMAN.PANZER_IV_SDKFZ_AUSF1","EBP.GERMAN.PANZER_IV_SDKFZ_AUSF1_MP","EBP.GERMAN.PANZER_MG","EBP.GERMAN.PANZERWERFER_SDKFZ_4_1","EBP.GERMAN.PANZERWERFER_SDKFZ_4_1_MP","EBP.GERMAN.PARADROP_SNIPER_SOLDIER_MP","EBP.GERMAN.PIONEER","EBP.GERMAN.PIONEER_MP","EBP.GERMAN.PUMA_EAST_GERMAN","EBP.GERMAN.REPAIR_PIONEER","EBP.GERMAN.REPAIR_PIONEER_MP","EBP.GERMAN.RIEGEL_43_MINE","EBP.GERMAN.RIEGEL_43_MINE_MP","EBP.GERMAN.SCHWERES_KRIEGSWERK","EBP.GERMAN.SCHWERES_KRIEGSWERK_MP","EBP.GERMAN.SDKFZ_221_LIGHT_AT_HALFTRACK","EBP.GERMAN.SLIT_TRENCH_GERMAN","EBP.GERMAN.SLIT_TRENCH_GERMAN_MP","EBP.GERMAN.SNIPER_COVER","EBP.GERMAN.SNIPER_DIGIN_COVER_MP","EBP.GERMAN.SNIPER_SOLDIER","EBP.GERMAN.SNIPER_SOLDIER_MP","EBP.GERMAN.STORMTROOPERS_MP","EBP.GERMAN.STUG_III_E_SDKFZ_141_1","EBP.GERMAN.STUG_III_E_SDKFZ_141_1_COMMANDER_MP","EBP.GERMAN.STUG_III_E_SDKFZ_141_1_MP","EBP.GERMAN.STUG_III_G_SDKFZ_141_1","EBP.GERMAN.STUG_III_G_SDKFZ_141_1_MP","EBP.GERMAN.STUKA_AIR_RECON","EBP.GERMAN.STUKA_AIR_RECON_MP","EBP.GERMAN.STUKA_BOMBING_DIVE","EBP.GERMAN.STUKA_BOMBING_DIVE_MP","EBP.GERMAN.STUKA_BOMBING_RUN_SP","EBP.GERMAN.STUKA_FRAGEMENTATION_BOMB","EBP.GERMAN.STUKA_FRAGEMENTATION_BOMB_MP","EBP.GERMAN.STUKA_GROUND_ATTACK","EBP.GERMAN.STUKA_GROUND_ATTACK_LONG","EBP.GERMAN.STUKA_GROUND_ATTACK_M09","EBP.GERMAN.STUKA_GROUND_ATTACK_MP","EBP.GERMAN.STUKA_GROUND_ATTACK_WEST_AIRBORNE_ASSAULT","EBP.GERMAN.STUKA_INCENDIARY_BOMB","EBP.GERMAN.STUKA_INCENDIARY_BOMB_VICTORY","EBP.GERMAN.STUKA_JU87_ANTI_TANK","EBP.GERMAN.STUKA_JU87_ANTI_TANK_M06","EBP.GERMAN.STUKA_JU87_ANTI_TANK_MP","EBP.GERMAN.STUKA_JU87_ANTI_TANK_SUPERIORITY","EBP.GERMAN.STUKA_SMOKE_BOMB","EBP.GERMAN.STUKA_SMOKE_BOMB_MP","EBP.GERMAN.SUPPLY_TRUCK_METAL_M_01","EBP.GERMAN.SUPPLY_TRUCK_METAL_M_02","EBP.GERMAN.SUPPLY_TRUCK_MUNITIONS_CASE_AX_01","EBP.GERMAN.SUPPLY_TRUCK_MUNITIONS_CASE_AX_03","EBP.GERMAN.SUPPLY_TRUCK_SANDBAG_PILE_02","EBP.GERMAN.TACTICAL_BOMBER","EBP.GERMAN.TACTICAL_BOMBER_ACCURATE","EBP.GERMAN.TANK_TRAP","EBP.GERMAN.TELLER_MINE_MP","EBP.GERMAN.TIER1_MARKER","EBP.GERMAN.TIER2_MARKER","EBP.GERMAN.TIER3_MARKER","EBP.GERMAN.TIER4_MARKER","EBP.GERMAN.TIGER_ACE_SDKFZ_181_MP","EBP.GERMAN.TIGER_SDKFZ_181","EBP.GERMAN.TIGER_SDKFZ_181_MP","EBP.GERMAN.TIGER_SDKFZ_181_SINGLEPLAYER_MISSION","EBP.GERMAN.TIGER_SDKFZ_181_TOW","EBP.GERMAN.URBAN_ASSAULT_PANZER_GRENADIERS_MP","SBP.GERMAN.ASSAULT_GRENADIER_SQUAD_MP","SBP.GERMAN.ASSAULT_OFFICER_SQUAD","SBP.GERMAN.ASSAULT_OFFICER_SQUAD_MP","SBP.GERMAN.BRUMMBAR_SQUAD","SBP.GERMAN.BRUMMBAR_SQUAD_MP","SBP.GERMAN.CARGO_PLANE","SBP.GERMAN.CARGO_PLANE_FUEL","SBP.GERMAN.CARGO_PLANE_MUNITIONS","SBP.GERMAN.COMMAND_OFFICER_SQUAD_TOW","SBP.GERMAN.CONVOY_PIONEER_SQUAD","SBP.GERMAN.ELEFANT_TANK_DESTROYER_SQUAD","SBP.GERMAN.ELEFANT_TANK_DESTROYER_SQUAD_MP","SBP.GERMAN.GRENADIER_SQUAD","SBP.GERMAN.GRENADIER_SQUAD_M14","SBP.GERMAN.GRENADIER_SQUAD_MG42LMG_MP","SBP.GERMAN.GRENADIER_SQUAD_MP","SBP.GERMAN.GRENADIER_SQUAD_SP","SBP.GERMAN.HACK_INVISI_PIONEER_SQUAD_MP","SBP.GERMAN.HOWITZER_105MM_DUMMY_SQUAD","SBP.GERMAN.HOWITZER_105MM_LE_FH18_ARTILLERY","SBP.GERMAN.HOWITZER_105MM_LE_FH18_ARTILLERY_MP","SBP.GERMAN.LUFTWAFFE_OFFICER_SQUAD_TOW","SBP.GERMAN.M01_MG42_HEAVY_MACHINE_GUN_SQUAD_SINGLE","SBP.GERMAN.M01_STUKA_DOGFIGHT","SBP.GERMAN.M01_STUKA_GROUND_ATTACK_SQUAD_FAST","SBP.GERMAN.MECHANIZED_250_HALFTRACK_GRENADIERS_MP","SBP.GERMAN.MECHANIZED_250_HALFTRACK_MP","SBP.GERMAN.MECHANIZED_250_HALFTRACK_TOW","SBP.GERMAN.MG42_HEAVY_MACHINE_GUN_SQUAD","SBP.GERMAN.MG42_HEAVY_MACHINE_GUN_SQUAD_MP","SBP.GERMAN.MORTAR_250_HALFTRACK_SQUAD","SBP.GERMAN.MORTAR_250_HALFTRACK_SQUAD_MP","SBP.GERMAN.MORTAR_TEAM_81MM","SBP.GERMAN.MORTAR_TEAM_81MM_MP","SBP.GERMAN.OFFICER_SQUAD","SBP.GERMAN.OFFICER_SQUAD_MP","SBP.GERMAN.OPEL_BLITZ_SQUAD","SBP.GERMAN.OPEL_BLITZ_SUPPLY_SQUAD","SBP.GERMAN.OSTRUPPEN_SQUAD","SBP.GERMAN.OSTRUPPEN_SQUAD_M14","SBP.GERMAN.OSTRUPPEN_SQUAD_MP","SBP.GERMAN.OSTRUPPEN_SQUAD_RESERVES_MP","SBP.GERMAN.OSTWIND_SQUAD","SBP.GERMAN.OSTWIND_SQUAD_MP","SBP.GERMAN.PAK40_75MM_AT_GUN_SQUAD","SBP.GERMAN.PAK40_75MM_AT_GUN_SQUAD_MP","SBP.GERMAN.PAK43_88MM_AT_GUN_SQUAD","SBP.GERMAN.PAK43_88MM_AT_GUN_SQUAD_MP","SBP.GERMAN.PANTHER_SQUAD","SBP.GERMAN.PANTHER_SQUAD_MP","SBP.GERMAN.PANZER_GRENADIER_SQUAD","SBP.GERMAN.PANZER_GRENADIER_SQUAD_M14","SBP.GERMAN.PANZER_GRENADIER_SQUAD_MP","SBP.GERMAN.PANZER_IV_COMMAND_SQUAD","SBP.GERMAN.PANZER_IV_COMMAND_SQUAD_MP","SBP.GERMAN.PANZER_IV_SQUAD","SBP.GERMAN.PANZER_IV_SQUAD_MP","SBP.GERMAN.PANZER_IV_SQUAD_TUTORIAL","SBP.GERMAN.PANZER_IV_STUBBY_SQUAD","SBP.GERMAN.PANZER_IV_STUBBY_SQUAD_MP","SBP.GERMAN.PANZER_MG_SQUAD","SBP.GERMAN.PANZERWERFER_SQUAD","SBP.GERMAN.PANZERWERFER_SQUAD_MP","SBP.GERMAN.PARTISAN_SQUAD_M13","SBP.GERMAN.PIONEER_SQUAD","SBP.GERMAN.PIONEER_SQUAD_MP","SBP.GERMAN.PIONEER_SQUAD_TOW","SBP.GERMAN.PUMA_EAST_GERMAN_MP","SBP.GERMAN.SCOUTCAR_SDKFZ222","SBP.GERMAN.SCOUTCAR_SDKFZ222_MP","SBP.GERMAN.SDKFZ_221_LIGHT_AT_HALFTRACK","SBP.GERMAN.SDKFZ_251_HALFTRACK_SQUAD","SBP.GERMAN.SDKFZ_251_HALFTRACK_SQUAD_MP","SBP.GERMAN.SNIPER_SQUAD","SBP.GERMAN.SNIPER_SQUAD_MP","SBP.GERMAN.STORMTROOPER_SQUAD_MP","SBP.GERMAN.STUG_III_E_COMMANDER_SQUAD_MP","SBP.GERMAN.STUG_III_E_SQUAD","SBP.GERMAN.STUG_III_E_SQUAD_MP","SBP.GERMAN.STUG_III_SQUAD","SBP.GERMAN.STUG_III_SQUAD_MP","SBP.GERMAN.STUKA_AIR_CAP_SQUAD","SBP.GERMAN.STUKA_AIR_CAP_SQUAD_MP","SBP.GERMAN.STUKA_GROUND_ANTI_TANK_SQUAD","SBP.GERMAN.STUKA_GROUND_ANTI_TANK_SQUAD_M06","SBP.GERMAN.STUKA_GROUND_ANTI_TANK_SQUAD_MP","SBP.GERMAN.STUKA_GROUND_ANTI_TANK_SQUAD_SUPERIORITY","SBP.GERMAN.STUKA_GROUND_ATTACK_SQUAD","SBP.GERMAN.STUKA_GROUND_ATTACK_SQUAD_LONG","SBP.GERMAN.STUKA_GROUND_ATTACK_SQUAD_M09","SBP.GERMAN.STUKA_GROUND_ATTACK_SQUAD_MP","SBP.GERMAN.STUKA_GROUND_ATTACK_WEST_GERMANS_SQUAD","SBP.GERMAN.STUKA_GROUND_FRAGMENTATION_SQUAD","SBP.GERMAN.STUKA_GROUND_FRAGMENTATION_SQUAD_MP","SBP.GERMAN.STUKA_INCENDIARY_BOMB_SQUAD","SBP.GERMAN.STUKA_INCENDIARY_BOMB_VICTORY_SQUAD","SBP.GERMAN.STUKA_SMOKE_SQUAD","SBP.GERMAN.STUKA_SMOKE_SQUAD_MP","SBP.GERMAN.TACTICAL_BOMBERS","SBP.GERMAN.TACTICAL_BOMBERS_ACCURATE","SBP.GERMAN.TIGER_ACE_SQUAD_MP","SBP.GERMAN.TIGER_SQUAD","SBP.GERMAN.TIGER_SQUAD_MP","SBP.GERMAN.TIGER_SQUAD_SP_A2_M02","SBP.GERMAN.TIGER_SQUAD_TOW","SBP.GERMAN.URBAN_ASSAULT_PANZER_GRENADIER_SQUAD_MP","ABILITY.GERMAN.AIR_DROPPED_MEDICAL_SUPPLIES","ABILITY.GERMAN.AIR_DROPPED_MUNITIONS","ABILITY.GERMAN.AMBUSH_CAMO_HOLD_FIRE_MP","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE_AT","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE_MORTAR","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE_PIO","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE_UNLOCK","ABILITY.GERMAN.AMBUSH_CAMOUFLAGE_UPGRADE","ABILITY.GERMAN.ARMOR_COMMANDER","ABILITY.GERMAN.ASSAULT_FIELD_OFFICER","ABILITY.GERMAN.ASSAULT_GRENADIER_GRENADE","ABILITY.GERMAN.ASSAULT_GRENADIER_SPRINT_MP","ABILITY.GERMAN.ASSAULT_GRENADIERS","ABILITY.GERMAN.ASSAULT_OFFICER_INSPIRATION","ABILITY.GERMAN.ASSAULT_OFFICER_INSPIRATION_VET3","ABILITY.GERMAN.ASSAULT_OFFICER_VICTOR_TARGET","ABILITY.GERMAN.AXIS_SNIPER_DELAYED_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.GERMAN.BLINDING_GRENADE","ABILITY.GERMAN.BLINDING_GRENADES_UNLOCK","ABILITY.GERMAN.BREAKTHROUGH","ABILITY.GERMAN.BRUMMBAR_BUNKER_BUSTER_MP","ABILITY.GERMAN.BRUMMBAR_CRITICAL_SHOTS_MP","ABILITY.GERMAN.BRUMMBAR_HOLD_FIRE_MP","ABILITY.GERMAN.CAMOUFLAGE_NETS","ABILITY.GERMAN.CAMOUFLAGE_NETS_UNLOCK","ABILITY.GERMAN.COMMAND_PANTHER_MARK_TARGET","ABILITY.GERMAN.CONVERT_TANK_WRECK","ABILITY.GERMAN.CONVERT_TANK_WRECK_UNLOCK","ABILITY.GERMAN.COUNTERATTACK_TACTICS","ABILITY.GERMAN.CRUSH_THE_POCKET","ABILITY.GERMAN.DEFENSIVE_FORTIFICATIONS","ABILITY.GERMAN.ELEFANT_CRITICAL_SHOTS_MP","ABILITY.GERMAN.ELEFANT_UNLOCK","ABILITY.GERMAN.ELEPHANT_CONE_LOS_TOGGLE_ABILITY","ABILITY.GERMAN.ELEPHANT_CONE_LOS_TOGGLE_ABILITY_MP","ABILITY.GERMAN.FAST_MARCH","ABILITY.GERMAN.FATALITY_PANZERWERFER_BARRAGE","ABILITY.GERMAN.FATALITY_SMOKE_BARRAGE","ABILITY.GERMAN.FATALITY_STUKA_INCENDIARY_AIRSTRIKE","ABILITY.GERMAN.FATALITY_STUKA_SMOKE_STRAFE_AIRSTRIKE","ABILITY.GERMAN.FORWARD_REPAIR_STATION","ABILITY.GERMAN.GERMAN_HQ_PIONEER_CALL_IN","ABILITY.GERMAN.GERMAN_HULLDOWN_ABILITY","ABILITY.GERMAN.GERMAN_HULLDOWN_DISABLE","ABILITY.GERMAN.GERMAN_MORTAR_HOLD_FIRE","ABILITY.GERMAN.GERMAN_MORTAR_HOLD_FIRE_MP","ABILITY.GERMAN.GERMAN_SALVAGE_ABILITY","ABILITY.GERMAN.GERMAN_SALVAGE_ABILITY_CONVOY","ABILITY.GERMAN.GERMAN_SALVAGE_ABILITY_MP","ABILITY.GERMAN.GERMAN_WARNING_SMOKE","ABILITY.GERMAN.GOLIATH_IN_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.GERMAN.GOLIATH_SELF_DESTRUCT_MP","ABILITY.GERMAN.GRENADIER_ANTITANK_RIFLE_GRENADE_ABILITY","ABILITY.GERMAN.GRENADIER_ANTITANK_RIFLE_GRENADE_ABILITY_MP","ABILITY.GERMAN.GRENADIER_PANZERFAUST","ABILITY.GERMAN.GRENADIER_PANZERFAUST_MP","ABILITY.GERMAN.GRENADIER_RIFLE_GRENADE_ABILITY","ABILITY.GERMAN.GRENADIER_RIFLE_GRENADE_ABILITY_MP","ABILITY.GERMAN.GRENADIER_RIFLE_GRENADE_ABILITY_TUTORIAL","ABILITY.GERMAN.HALFTRACK_INCENDIARY_MORTAR_BARRAGE","ABILITY.GERMAN.HALFTRACK_INCENDIARY_MORTAR_BARRAGE_MP","ABILITY.GERMAN.HALFTRACK_MORTAR_BARRAGE","ABILITY.GERMAN.HALFTRACK_MORTAR_BARRAGE_MP","ABILITY.GERMAN.HALFTRACK_MORTAR_VICTORTARGET_BARRAGE_MP","ABILITY.GERMAN.HALFTRACK_SMOKE_BARRAGE","ABILITY.GERMAN.HALFTRACK_SMOKE_BARRAGE_MP","ABILITY.GERMAN.HEAVY_AT_MINE_UNLOCK","ABILITY.GERMAN.HOWITZER_105MM_BARRAGE_ABILITY","ABILITY.GERMAN.HOWITZER_105MM_BARRAGE_ABILITY_MP","ABILITY.GERMAN.HOWITZER_105MM_BARRAGE_VET3_ABILITY_MP","ABILITY.GERMAN.HOWITZER_105MM_EMPLACEMENT_UNLOCK","ABILITY.GERMAN.HOWITZER_105MM_VICTORTARGET_BARRAGE_ABILITY_MP","ABILITY.GERMAN.HOWITZER_COUNTER_BARRAGE_ATTACK_MP","ABILITY.GERMAN.HOWITZER_COUNTER_BARRAGE_MP","ABILITY.GERMAN.HOWITZER_DEFAULT_REFACE_ACTION","ABILITY.GERMAN.HULL_DOWN_UNLOCK","ABILITY.GERMAN.INFANTRY_MEDKITS","ABILITY.GERMAN.INFANTRY_MEDKITS_MP","ABILITY.GERMAN.JAEGER_INFANTRY_UNLOCK","ABILITY.GERMAN.JAEGER_INTERROGATION","ABILITY.GERMAN.LAY_HEAVY_AT_MINE","ABILITY.GERMAN.LIGHT_SUPPORT_ARTILLERY","ABILITY.GERMAN.MECHANIZED_ASSAULT_GROUP","ABILITY.GERMAN.MECHANIZED_GRENADIER_GROUP","ABILITY.GERMAN.MG42_CAMO_HOLD_FIRE_MP","ABILITY.GERMAN.MG42_PHOSPHORUS_ROUNDS","ABILITY.GERMAN.MG42_PHOSPHORUS_ROUNDS_MP","ABILITY.GERMAN.MORTAR_COUNTER_BARRAGE_ATTACK_MP","ABILITY.GERMAN.MORTAR_COUNTER_BARRAGE_MP","ABILITY.GERMAN.MORTAR_COUNTER_BARRAGE_WEAPON_MP","ABILITY.GERMAN.MORTAR_HALFTRACK","ABILITY.GERMAN.MORTAR_INCENDIARY_BARRAGE","ABILITY.GERMAN.MORTAR_TEAM_INCENDIARY_BARRAGE_MP","ABILITY.GERMAN.MORTAR_TEAM_MORTAR_BARRAGE","ABILITY.GERMAN.MORTAR_TEAM_MORTAR_BARRAGE_MP","ABILITY.GERMAN.MORTAR_TEAM_MORTAR_VICTORTARGET_BARRAGE_MP","ABILITY.GERMAN.MORTAR_TEAM_SMOKE_BARRAGE","ABILITY.GERMAN.MORTAR_TEAM_SMOKE_BARRAGE_MP","ABILITY.GERMAN.MUNITIONS_BLITZ","ABILITY.GERMAN.OFFICER_SMOKE_ARTILLERY","ABILITY.GERMAN.OPEL_SUPPLY_TERRITORY_CHECK","ABILITY.GERMAN.OSTRUPPEN","ABILITY.GERMAN.OSTRUPPEN_COVER_BONUS","ABILITY.GERMAN.OSTRUPPEN_RESERVES","ABILITY.GERMAN.PAK_43_EMPLACEMENT_UNLOCK","ABILITY.GERMAN.PAK40_CRITICAL_SHOTS_MP","ABILITY.GERMAN.PAK40_TARGET_WEAK_POINT_MP","ABILITY.GERMAN.PAK43_CRITICAL_SHOTS_MP","ABILITY.GERMAN.PAK43_TARGET_WEAK_POINT_MP","ABILITY.GERMAN.PANTHER_TIGER_BLITZKRIEG_MP","ABILITY.GERMAN.PANZER_COMMANDER_AURA_MP","ABILITY.GERMAN.PANZER_DEFENSIVE_SMOKE","ABILITY.GERMAN.PANZER_GRENADIER_BUNDLED_CAMPAIGN","ABILITY.GERMAN.PANZER_GRENADIER_BUNDLED_GRENADE","ABILITY.GERMAN.PANZER_GRENADIER_BUNDLED_GRENADE_MP","ABILITY.GERMAN.PANZER_GRENADIER_BUNDLED_TUTORIAL","ABILITY.GERMAN.PANZER_PANTHER_TIGER_DEFENSIVE_SMOKE_TOW","ABILITY.GERMAN.PANZER_PANTHER_TIGER_OSTWIND_BLITZKRIEG","ABILITY.GERMAN.PANZER_PANTHER_TIGER_OSTWIND_BLITZKRIEG_MP","ABILITY.GERMAN.PANZER_PANTHER_TIGER_OSTWIND_BLITZKRIEG_TOW","ABILITY.GERMAN.PANZER_PANTHER_TIGER_OSTWIND_FLARES_ABILITY","ABILITY.GERMAN.PANZER_PANTHER_TIGER_OSTWIND_REPAIR_TOW","ABILITY.GERMAN.PANZER_TACTICIAN_UNLOCK","ABILITY.GERMAN.PANZERWERFER_COUNTER_BARRAGE_ATTACK_MP","ABILITY.GERMAN.PANZERWERFER_COUNTER_BARRAGE_MP","ABILITY.GERMAN.PANZERWERFER_ROCKET_BARRAGE","ABILITY.GERMAN.PANZERWERFER_ROCKET_BARRAGE_MP","ABILITY.GERMAN.PANZERWERFER_ROCKET_VICTORTARGET_BARRAGE_MP","ABILITY.GERMAN.PIONEER_BARBED_WIRE_CUTTING_ABILITY","ABILITY.GERMAN.PIONEER_BARBED_WIRE_CUTTING_ABILITY_MP","ABILITY.GERMAN.PIONEER_FLAMETHROWER","ABILITY.GERMAN.PUMA_CRITICAL_SHOTS_MP","ABILITY.GERMAN.PUMA_DISPATCH","ABILITY.GERMAN.RAILWAY_GUN_ARTILLERY","ABILITY.GERMAN.REDISTRIBUTE_RESOURCES","ABILITY.GERMAN.RELIEF_INFANTRY","ABILITY.GERMAN.REMOVE_AMBUSH_CAMOUFLAGE","ABILITY.GERMAN.RESOURCE_REQUISITION","ABILITY.GERMAN.RETREAT_TO_FORWARD_HQ","ABILITY.GERMAN.SCOUT_CAR_HALFTRACK_INFANTRY_AWARENESS","ABILITY.GERMAN.SCOUT_CAR_HALFTRACK_INFANTRY_AWARENESS_MP","ABILITY.GERMAN.SDKFZ_221_LIGHT_AT_HALFTRACK","ABILITY.GERMAN.SECTOR_ARTILLERY","ABILITY.GERMAN.SNIPER_INCENDIARY_ROUND_MP","ABILITY.GERMAN.SPRINT","ABILITY.GERMAN.STATIONARY_LOS_UNLOCK","ABILITY.GERMAN.STORMTROOPER_ASSAULT_AMBUSH_MP","ABILITY.GERMAN.STORMTROOPER_SPRINT_MP","ABILITY.GERMAN.STORMTROOPER_TANK_DETECTION_MP","ABILITY.GERMAN.STORMTROOPERS","ABILITY.GERMAN.STRATEGIC_BOMBING","ABILITY.GERMAN.STUG_CRITICAL_SHOTS_MP","ABILITY.GERMAN.STUG_ELEFANT_PAK40_PAK43_BRUMMBAR_CRITICAL_SHOTS","ABILITY.GERMAN.STUG_ELEFANT_PAK40_PAK43_BRUMMBAR_CRITICAL_SHOTS_MP","ABILITY.GERMAN.STUG_III_E","ABILITY.GERMAN.STUKA_AERIAL_SUPERIORITY_CLOSE_AIR_SUPPORT","ABILITY.GERMAN.STUKA_AERIAL_SUPERIORITY_RECON","ABILITY.GERMAN.STUKA_AERIAL_SUPERIORITY_STRAFING_RUN","ABILITY.GERMAN.STUKA_AIR_RECON","ABILITY.GERMAN.STUKA_BOMBING_RUN_SP","ABILITY.GERMAN.STUKA_BOMBING_STRIKE","ABILITY.GERMAN.STUKA_BOMBING_STRIKE_TOW","ABILITY.GERMAN.STUKA_CLOSE_AIR_M06","ABILITY.GERMAN.STUKA_CLOSE_AIR_M06_MP","ABILITY.GERMAN.STUKA_CLOSE_AIR_SUPPORT","ABILITY.GERMAN.STUKA_FRAGMENTATION_BOMB","ABILITY.GERMAN.STUKA_INCENDIARY_BOMBS","ABILITY.GERMAN.STUKA_SMOKE_BOMB","ABILITY.GERMAN.STUKA_STRAFING_RUN","ABILITY.GERMAN.SUPPLY_BREAK","ABILITY.GERMAN.SUPPLY_TRUCK","ABILITY.GERMAN.SUPPLY_TRUCK_LOCKDOWN","ABILITY.GERMAN.SUPPORT_TEAM_AMBUSH_CAMOUFLAGE","ABILITY.GERMAN.TANK_AWARENESS_UNLOCK","ABILITY.GERMAN.TANK_DETECTION_ABILITY_CONVOY","ABILITY.GERMAN.TIGER_ACE_CRITICAL_SHOTS_MP","ABILITY.GERMAN.TIGER_TANK","ABILITY.GERMAN.TIGER_TANK_ACE","ABILITY.GERMAN.TRENCH_UNLOCK","ABILITY.GERMAN.TROOP_TRAINING","ABILITY.GERMAN.URBAN_ASSAULT_GRENADIERS","ABILITY.GERMAN.URBAN_ASSAULT_SATCHEL_CHARGE_THROW_ABILITY_MP","ABILITY.GERMAN.URBAN_ASSAULT_SMOKE_GRENADE","ABILITY.GERMAN.URBAN_ASSAULT_SMOKE_GRENADE_2","ABILITY.GERMAN.WEHR_VEHICLE_HOLD_FIRE_MP","UPG.GERMAN.AERIAL_SUPERIORITY_RECON_PLANE","UPG.GERMAN.AERIAL_SUPERIORITY_STUKA_CLOSE_AIR_SUPPORT","UPG.GERMAN.AERIAL_SUPERIORITY_STUKA_STRAFE","UPG.GERMAN.AIR_DROP_MEDICAL_SUPPLIES","UPG.GERMAN.AIR_DROP_RESOURCES","UPG.GERMAN.AMBUSH_CAMOU_PACKAGE","UPG.GERMAN.AMBUSH_CAMOUFLAGE","UPG.GERMAN.ARMOR_COMMANDER","UPG.GERMAN.ASSAULT_ARCHETYPE","UPG.GERMAN.ASSAULT_FIELD_OFFICER","UPG.GERMAN.ASSAULT_GRENADIERS","UPG.GERMAN.BATTLE_PHASE_2","UPG.GERMAN.BATTLE_PHASE_2_MP","UPG.GERMAN.BATTLE_PHASE_3","UPG.GERMAN.BATTLE_PHASE_3_MP","UPG.GERMAN.BATTLE_PHASE_4","UPG.GERMAN.BATTLE_PHASE_4_MP","UPG.GERMAN.BLINDING_GRENADES","UPG.GERMAN.BREAKTHROUGH","UPG.GERMAN.BRUMMBAR_TOP_GUNNER","UPG.GERMAN.BRUMMBAR_TOP_GUNNER_MP","UPG.GERMAN.BUNKER_COMMAND","UPG.GERMAN.BUNKER_COMMAND_MP","UPG.GERMAN.BUNKER_MEDIC_STATION","UPG.GERMAN.BUNKER_MEDIC_STATION_MP","UPG.GERMAN.BUNKER_MG42_ADDITION","UPG.GERMAN.BUNKER_MG42_ADDITION_MP","UPG.GERMAN.CAMOUFLAGE_NET_ACTIVATED","UPG.GERMAN.CAMOUFLAGE_NETS","UPG.GERMAN.CAN_CAMOUFLAGE","UPG.GERMAN.COUNTERATTACK_TACTICS","UPG.GERMAN.CRUSH_THE_POCKET","UPG.GERMAN.DEFENSIVE_FORTIFICATIONS","UPG.GERMAN.ELEFANT_UNLOCK","UPG.GERMAN.FAST_MARCH","UPG.GERMAN.FESTUNG_ARCHETYPE","UPG.GERMAN.FORWARD_REPAIR_STATION","UPG.GERMAN.GRENADIER_MG42_LMG","UPG.GERMAN.GRENADIER_MG42_LMG_MP","UPG.GERMAN.HEAVY_AT_MINE","UPG.GERMAN.HOWITZER_105MM_EMPLACEMENT","UPG.GERMAN.HOWITZER_COUNTER_BARRAGE_COOLDOWN_MP","UPG.GERMAN.HULL_DOWN","UPG.GERMAN.HULLDOWN_ACTIVATED","UPG.GERMAN.HULLDOWN_CONSTRUCTING","UPG.GERMAN.JAEGER_ARCHETYPE","UPG.GERMAN.JAEGER_LIGHT_INFANTRY","UPG.GERMAN.LIGHT_ARTILLERY_SUPPORT","UPG.GERMAN.LIGHT_INFANTRY_PACKAGE","UPG.GERMAN.LIGHT_INFANTRY_PANZERGREN_PACKAGE","UPG.GERMAN.MECHANIZED_GRENADIER_GROUP","UPG.GERMAN.MECHANIZED_GROUP","UPG.GERMAN.MG42_HOLDFIRE_CAMOUFLAGE_NET_ACTIVATED","UPG.GERMAN.MORTAR_COUNTER_BARRAGE_COOLDOWN_MP","UPG.GERMAN.MORTAR_COUNTER_BARRAGE_MP","UPG.GERMAN.MORTAR_HALFTRACK","UPG.GERMAN.MORTAR_HALFTRACK_250_UPGRADE","UPG.GERMAN.MORTAR_HALFTRACK_COUNTER_BARRAGE_COOLDOWN_MP","UPG.GERMAN.MORTAR_INCENDIARY_BARRAGE","UPG.GERMAN.MUNITION_BLITZ","UPG.GERMAN.OSTRUPPEN","UPG.GERMAN.OSTRUPPEN_RESERVES","UPG.GERMAN.PAK_43_EMPLACEMENT","UPG.GERMAN.PANTHER_TOP_GUNNER","UPG.GERMAN.PANTHER_TOP_GUNNER_MP","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM_1_SCHREK_MP","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM_MP","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM_SECOND","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM_SECOND_MP","UPG.GERMAN.PANZER_GRENADIER_PANZERSHRECK_ATW_ITEM_THIRD_MP","UPG.GERMAN.PANZER_TACTICIAN","UPG.GERMAN.PANZER_TOP_GUNNER","UPG.GERMAN.PANZER_TOP_GUNNER_MP","UPG.GERMAN.PANZERBUSCHE_39","UPG.GERMAN.PANZERBUSCHE_39_MP","UPG.GERMAN.PANZERWERFER_COUNTER_BARRAGE_COOLDOWN_MP","UPG.GERMAN.PIONEER_FLAMETHROWER","UPG.GERMAN.PIONEER_FLAMETHROWER_MP","UPG.GERMAN.PIONEER_MINESWEEPER","UPG.GERMAN.PIONEER_MINESWEEPER_MP","UPG.GERMAN.PUMA_DISPATCH","UPG.GERMAN.RAILWAY_ARTILLERY_SUPPORT","UPG.GERMAN.RECON_PLANE","UPG.GERMAN.REDISTRIBUTE_RESOURCES","UPG.GERMAN.RELIEF_INFANTRY","UPG.GERMAN.SDKFZ_222_20MM_GUN","UPG.GERMAN.SDKFZ_222_20MM_GUN_MP","UPG.GERMAN.SDKFZ_251_HALFTRACK_FLAMMPANZERWAGEN_UPGRADE","UPG.GERMAN.SDKFZ_251_HALFTRACK_FLAMMPANZERWAGEN_UPGRADE_MP","UPG.GERMAN.SDKFZ_251_HALFTRACK_MOBILE_MEDIC_STATION_UPGRADE","UPG.GERMAN.SECTOR_ARTILLERY","UPG.GERMAN.SPRINT","UPG.GERMAN.STATIONARY_LOS_GAIN","UPG.GERMAN.STORMTROOPER_ANTITANK_PACKAGE_MP","UPG.GERMAN.STORMTROOPER_ASSAULT_PACKAGE_MP","UPG.GERMAN.STORMTROOPER_PANZERSCHRECK_MP","UPG.GERMAN.STORMTROOPERS","UPG.GERMAN.STRATEGIC_BOMBING","UPG.GERMAN.STUG_III_E_UNLOCK","UPG.GERMAN.STUG_SHORT_BARREL","UPG.GERMAN.STUG_TOP_GUNNER","UPG.GERMAN.STUG_TOP_GUNNER_MP","UPG.GERMAN.STUKA_BOMBING_RUN_UPGRADE","UPG.GERMAN.STUKA_CLOSE_AIR_SUPPORT","UPG.GERMAN.STUKA_FLAME_STRIKE","UPG.GERMAN.STUKA_FRAGMENTATION_BOMB","UPG.GERMAN.STUKA_SMOKE_BOMB","UPG.GERMAN.STUKA_STRAFE","UPG.GERMAN.SUPPLY_BREAK","UPG.GERMAN.SUPPLY_TRUCK_ACTIVE","UPG.GERMAN.SUPPLY_TRUCK_EXIT","UPG.GERMAN.SUPPLY_TRUCK_FILL_STATE","UPG.GERMAN.SUPPLY_TRUCK_FULL","UPG.GERMAN.SUPPLY_TRUCK_LOCKDOWN","UPG.GERMAN.TANK_AWARENESS","UPG.GERMAN.TIGER_TANK","UPG.GERMAN.TIGER_TANK_ACE","UPG.GERMAN.TIGER_TANK_ACE_CALLIN_RESTRICTION","UPG.GERMAN.TIGER_TOP_GUNNER","UPG.GERMAN.TIGER_TOP_GUNNER_MP","UPG.GERMAN.TIGER_TOP_GUNNER_TOW","UPG.GERMAN.TOW_1941_GERMAN","UPG.GERMAN.TRENCH","UPG.GERMAN.TROOP_TRAINING","UPG.GERMAN.URBAN_ASSAULT_ARMOR_UPGRADE","UPG.GERMAN.URBAN_ASSAULT_PANZER_GRENADIERS","UPG.GERMAN.URBAN_ASSAULT_PANZER_GRENADIERS_FLAMETHROWER_MP","UPG.GERMAN.VEHICLES_OPTICS","UPG.GERMAN.XP1_GERMAN_DEMO_UPGRADE","EBP.PROXY.PROXY_MEDIC_MP","EBP.PROXY.PROXY_RIFLEMAN_SOLDIER_A","EBP.PROXY.PROXY_RIFLEMAN_SOLDIER_B","EBP.PROXY.PROXY_RIFLEMAN_SOLDIER_C","EBP.PROXY.PROXY_SNIPER_RECON_MP","SBP.PROXY.PROXY_HMG_SQUAD_MP","SBP.PROXY.PROXY_MECH_SQUAD_MP","SBP.PROXY.PROXY_RIFLEMEN_SQUAD_MP","SBP.PROXY.PROXY_SNIPER_SQUAD_MP","EBP.SOVIET._CIVILIAN_FEMALE","EBP.SOVIET._CIVILIAN_FEMALE_MP","EBP.SOVIET._CIVILIAN_MALE","EBP.SOVIET._CIVILIAN_MALE_MP","EBP.SOVIET.ANTI_PERSONNEL_MINES","EBP.SOVIET.ARTILLERY_203MM_B4","EBP.SOVIET.ATGUN53K_CREW","EBP.SOVIET.ATGUN53K_CREW_MP","EBP.SOVIET.ATGUNZIS_CREW","EBP.SOVIET.ATGUNZIS_CREW_MP","EBP.SOVIET.BARBED_WIRE_FENCE","EBP.SOVIET.BARBED_WIRE_FENCE_MP","EBP.SOVIET.BARBED_WIRE_FIELD","EBP.SOVIET.BARBED_WIRE_FIELD_MP","EBP.SOVIET.BARRACKS","EBP.SOVIET.BARRACKS_MP","EBP.SOVIET.BASE_CONSCRIPT_SOLDIER","EBP.SOVIET.BASE_CONSCRIPT_SOLDIER_MP","EBP.SOVIET.BOAT_01_ENTITY","EBP.SOVIET.CARGO_PLANE_SOVIET","EBP.SOVIET.COMBAT_ENGINEER","EBP.SOVIET.COMBAT_ENGINEER_MP","EBP.SOVIET.COMMISSAR","EBP.SOVIET.COMMISSAR_227","EBP.SOVIET.COMMISSAR_MP","EBP.SOVIET.COMMISSAR_OF_DEATH_227_MP","EBP.SOVIET.CONSCRIPT_SOLDIER","EBP.SOVIET.CONSCRIPT_SOLDIER_CONSCRIPT_BODYGUARD_MP","EBP.SOVIET.CONSCRIPT_SOLDIER_MP","EBP.SOVIET.DHSK_38_MACHINE_GUN","EBP.SOVIET.DHSK_38_MACHINE_GUN_MP","EBP.SOVIET.DSHK_WEAPON_CREW","EBP.SOVIET.DSHK_WEAPON_CREW_MP","EBP.SOVIET.FLARE_FIRE_MP","EBP.SOVIET.FLARE_MINE","EBP.SOVIET.FLARE_MINE_MP","EBP.SOVIET.FORWARD_HQ","EBP.SOVIET.GUARD_TROOPS","EBP.SOVIET.GUARD_TROOPS_ASSAULT_MP","EBP.SOVIET.GUARD_TROOPS_MP","EBP.SOVIET.HM_120_38_MORTAR","EBP.SOVIET.HM_120_38_MORTAR_MP","EBP.SOVIET.HOWITZER_CREW_SOVIET","EBP.SOVIET.HOWITZER_CREW_SOVIET_MP","EBP.SOVIET.HOWITZER_CREW203__SOVIET_MP","EBP.SOVIET.HQ","EBP.SOVIET.HQ_INVISIBLE_SP","EBP.SOVIET.HQ_MP","EBP.SOVIET.HQ_NO_WRECK","EBP.SOVIET.HQ_WRECK","EBP.SOVIET.HQ_WRECK_M06","EBP.SOVIET.HQ_WRECK_MP","EBP.SOVIET.IL_2_STURMOVIK","EBP.SOVIET.IL_2_STURMOVIK_ADVANCED_MP","EBP.SOVIET.IL_2_STURMOVIK_ANTI_TANK_BOMB_MP","EBP.SOVIET.IL_2_STURMOVIK_MARK_VEHICLE_MP","EBP.SOVIET.IL_2_STURMOVIK_MP","EBP.SOVIET.IL_2_STURMOVIK_RECON","EBP.SOVIET.IL_2_STURMOVIK_RECON_MP","EBP.SOVIET.IL_2_STURMOVIK_ROCKET","EBP.SOVIET.IL_2_STURMOVIK_ROCKET_MP","EBP.SOVIET.IL_2_STURMOVIK_ROCKET_SP","EBP.SOVIET.IL_2_STURMOVIK_VICTORY_MP","EBP.SOVIET.IS_2_HEAVY_TANK","EBP.SOVIET.IS_2_HEAVY_TANK_MP","EBP.SOVIET.ISAKOVICH_A01_COMMANDER","EBP.SOVIET.ISAKOVICH_M06","EBP.SOVIET.ISU_152_SPG","EBP.SOVIET.ISU_152_SPG_MP","EBP.SOVIET.KATYUSHA_BM_13N","EBP.SOVIET.KATYUSHA_BM_13N_MP","EBP.SOVIET.KV_1","EBP.SOVIET.KV_1_COMMANDER_MP","EBP.SOVIET.KV_1_MP","EBP.SOVIET.KV_2","EBP.SOVIET.KV_2_MP","EBP.SOVIET.KV_2_TOW","EBP.SOVIET.KV_8","EBP.SOVIET.KV_8_MP","EBP.SOVIET.LIGHT_ANTI_VEHICLE_MINES","EBP.SOVIET.M01_BASE_CONSCRIPT_SOLDIER","EBP.SOVIET.M01_BASE_CONSCRIPT_SOLDIER_DURABLE","EBP.SOVIET.M01_CONSCRIPT_SOLDIER","EBP.SOVIET.M01_CONSCRIPT_SOLDIER_DOCK","EBP.SOVIET.M01_CONSCRIPT_SOLDIER_HARMLESS","EBP.SOVIET.M01_CONSCRIPT_SOLDIER_HARMLESS_DURABLE","EBP.SOVIET.M01_IL_2_STURMOVIK_ROCKET","EBP.SOVIET.M01_IL2_DOGFIGHT","EBP.SOVIET.M01_MEDIC","EBP.SOVIET.M08_T_34_76_SMALLPATH","EBP.SOVIET.M08_TANK_BUSTER_CONSCRIPT","EBP.SOVIET.M11_ANIA_SNIPER","EBP.SOVIET.M11_ISAKOVICH_RECON","EBP.SOVIET.M11_PARTISAN_TROOP_KAR98K","EBP.SOVIET.M11_PARTISAN_TROOP_NAGANT","EBP.SOVIET.M11_PARTISAN_TROOP_NOWEAPON","EBP.SOVIET.M11_SNIPER","EBP.SOVIET.M11_SNIPER_RECON","EBP.SOVIET.M1910_MAXIM_HEAVY_MACHINE_GUN","EBP.SOVIET.M1910_MAXIM_HEAVY_MACHINE_GUN_MP","EBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY","EBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY_COMMANDER_MP","EBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY_MP","EBP.SOVIET.M1937_152MM_ML_20_ARTILLERY","EBP.SOVIET.M1937_152MM_ML_20_ARTILLERY_MP","EBP.SOVIET.M1937_53_K_45MM_AT_GUN","EBP.SOVIET.M1937_53_K_45MM_AT_GUN_MP","EBP.SOVIET.M1942_76MM_DIVISIONAL_GUN_ZIS_3","EBP.SOVIET.M1942_76MM_DIVISIONAL_GUN_ZIS_3_MP","EBP.SOVIET.M3A1_SCOUT_CAR","EBP.SOVIET.M3A1_SCOUT_CAR_MP","EBP.SOVIET.M5_HALFTRACK","EBP.SOVIET.M5_HALFTRACK_ASSAULT_MP","EBP.SOVIET.M5_HALFTRACK_MP","EBP.SOVIET.MACHINE_GUN_NEST","EBP.SOVIET.MACHINE_GUN_NEST_MP","EBP.SOVIET.MAXIM_WEAPON_CREW","EBP.SOVIET.MAXIM_WEAPON_CREW_MP","EBP.SOVIET.MEDIC","EBP.SOVIET.MEDIC_MP","EBP.SOVIET.MORTAR_120MM_WEAPON_CREW_MP","EBP.SOVIET.MORTAR_WEAPON_CREW","EBP.SOVIET.MORTAR_WEAPON_CREW_MP","EBP.SOVIET.MOTORPOOL","EBP.SOVIET.MOTORPOOL_MP","EBP.SOVIET.OBSERVATION_POST_FUEL","EBP.SOVIET.OBSERVATION_POST_FUEL_MP","EBP.SOVIET.OBSERVATION_POST_MUNITION","EBP.SOVIET.OBSERVATION_POST_MUNITION_MP","EBP.SOVIET.PARTISAN_SNIPER","EBP.SOVIET.PARTISAN_TROOP_KAR98K","EBP.SOVIET.PARTISAN_TROOP_KAR98K_2","EBP.SOVIET.PARTISAN_TROOP_KAR98K_2_MP","EBP.SOVIET.PARTISAN_TROOP_KAR98K_MP","EBP.SOVIET.PARTISAN_TROOP_KAR98K_TOW_BD","EBP.SOVIET.PARTISAN_TROOP_KAR98K_TOW_MP","EBP.SOVIET.PARTISAN_TROOP_NAGANT","EBP.SOVIET.PARTISAN_TROOP_NAGANT_MP","EBP.SOVIET.PARTISAN_TROOP_NAGANT_TOW_MP","EBP.SOVIET.PARTISAN_TROOPS_ANTITANK","EBP.SOVIET.PARTISAN_TROOPS_LMG","EBP.SOVIET.PARTISAN_TROOPS_RIFLE","EBP.SOVIET.PARTISAN_TROOPS_SMG","EBP.SOVIET.PENAL_BATTALION_TROOPS","EBP.SOVIET.PENAL_BATTALION_TROOPS_MP","EBP.SOVIET.PM41_82MM_MORTAR","EBP.SOVIET.PM41_82MM_MORTAR_MP","EBP.SOVIET.REFUGEE_FEMALE","EBP.SOVIET.REFUGEE_FEMALE_MP","EBP.SOVIET.REFUGEE_MALE","EBP.SOVIET.REFUGEE_MALE_MP","EBP.SOVIET.REPAIR_ENGINEER","EBP.SOVIET.REPAIR_ENGINEER_MP","EBP.SOVIET.REPAIR_STATION_MP","EBP.SOVIET.SAND_BAG_SOVIET","EBP.SOVIET.SAND_BAG_SOVIET_MP","EBP.SOVIET.SAND_BAG_SOVIET_TUTORIAL","EBP.SOVIET.SHERMAN_SOVIET","EBP.SOVIET.SHOCK_TROOPS","EBP.SOVIET.SHOCK_TROOPS_MP","EBP.SOVIET.SNIPER","EBP.SOVIET.SNIPER_ATK_TARGET","EBP.SOVIET.SNIPER_MP","EBP.SOVIET.SNIPER_RECON","EBP.SOVIET.SNIPER_RECON_MP","EBP.SOVIET.SOVIET_ALLIED_CARGO_PLANE","EBP.SOVIET.SOVIET_BASE_STAMPER","EBP.SOVIET.SOVIET_MINE","EBP.SOVIET.SOVIET_MINE_M08","EBP.SOVIET.SOVIET_MINE_MP","EBP.SOVIET.SOVIET_MINE_SP","EBP.SOVIET.SOVIET_MINE_TOW","EBP.SOVIET.SOVIET_OFFICER","EBP.SOVIET.SOVIET_OFFICER_MP","EBP.SOVIET.STEAM_TRAIN","EBP.SOVIET.SU_76M","EBP.SOVIET.SU_76M_MP","EBP.SOVIET.SU_85","EBP.SOVIET.SU_85_MP","EBP.SOVIET.T_34_76","EBP.SOVIET.T_34_76_MP","EBP.SOVIET.T_34_85","EBP.SOVIET.T_34_85_MP","EBP.SOVIET.T_70M","EBP.SOVIET.T_70M_MP","EBP.SOVIET.TANK_DEPOT","EBP.SOVIET.TANK_DEPOT_MP","EBP.SOVIET.TANKTRAP","EBP.SOVIET.TOW_COLD_WEAETHER_GUARD_TROOPS","EBP.SOVIET.US6_TRUCK","EBP.SOVIET.US6_TRUCK_MP","EBP.SOVIET.WEAPON_SUPPORT_CENTER","EBP.SOVIET.WEAPON_SUPPORT_CENTER_MP","EBP.SOVIET.WIRE_FIELD","EBP.SOVIET.WIRE_FIELD_MP","EBP.SOVIET.ZIS_6_TRANSPORT","EBP.SOVIET.ZIS_6_TRANSPORT_MP","SBP.SOVIET.BASE_CONSCRIPT_SQUAD","SBP.SOVIET.BASE_CONSCRIPT_SQUAD_MP","SBP.SOVIET.BOAT_01","SBP.SOVIET.CARGO_PLANE_SOVIET","SBP.SOVIET.COMBAT_ENGINEER_SQUAD","SBP.SOVIET.COMBAT_ENGINEER_SQUAD_MP","SBP.SOVIET.COMMISSAR_227","SBP.SOVIET.COMMISSAR_SQUAD_BATTLE","SBP.SOVIET.COMMISSAR_SQUAD_MP","SBP.SOVIET.COMMISSAR_SQUAD_TOW","SBP.SOVIET.CONSCRIPT_SQUAD","SBP.SOVIET.CONSCRIPT_SQUAD_MP","SBP.SOVIET.CONSCRIPT_SQUAD_TUTORIAL","SBP.SOVIET.DSHK_38_HMG_SQUAD","SBP.SOVIET.DSHK_38_HMG_SQUAD_MP","SBP.SOVIET.GUARDS_TROOPS","SBP.SOVIET.GUARDS_TROOPS_ASSAULT_MP","SBP.SOVIET.GUARDS_TROOPS_M08","SBP.SOVIET.GUARDS_TROOPS_MP","SBP.SOVIET.HM_120_38_MORTAR_SQUAD","SBP.SOVIET.HM_120_38_MORTAR_SQUAD_MP","SBP.SOVIET.IL_2_STUMOVIK_SQUAD","SBP.SOVIET.IL_2_STUMOVIK_SQUAD_ADVANCED_MP","SBP.SOVIET.IL_2_STUMOVIK_SQUAD_MP","SBP.SOVIET.IL_2_STURMOVIK_ANTI_TANK_BOMB_SQUAD_MP","SBP.SOVIET.IL_2_STURMOVIK_MARK_VEHICLE_SQUAD_MP","SBP.SOVIET.IL_2_STURMOVIK_RECON_SQUAD","SBP.SOVIET.IL_2_STURMOVIK_RECON_SQUAD_MP","SBP.SOVIET.IL_2_STURMOVIK_RECON_SQUAD_SP","SBP.SOVIET.IL_2_STURMOVIK_ROCKET_SP_SQUAD","SBP.SOVIET.IL_2_STURMOVIK_ROCKET_SP_SQUAD_MP","SBP.SOVIET.IL_2_STURMOVIK_ROCKET_SQUAD","SBP.SOVIET.IL_2_STURMOVIK_ROCKET_SQUAD_MP","SBP.SOVIET.IS_2","SBP.SOVIET.IS_2_MP","SBP.SOVIET.IS_2_TOW","SBP.SOVIET.ISU_152","SBP.SOVIET.ISU_152_MP","SBP.SOVIET.KATYUSHA_BM_13N_SQUAD","SBP.SOVIET.KATYUSHA_BM_13N_SQUAD_MP","SBP.SOVIET.KV_1","SBP.SOVIET.KV_1_COMMANDER_MP","SBP.SOVIET.KV_1_MP","SBP.SOVIET.KV_1_SP","SBP.SOVIET.KV_2","SBP.SOVIET.KV_2_MP","SBP.SOVIET.KV_2_TOW","SBP.SOVIET.KV_2_TOW_BATTLE","SBP.SOVIET.KV_8","SBP.SOVIET.KV_8_MP","SBP.SOVIET.M01_CONSCRIPT_SQUAD_DOCKS","SBP.SOVIET.M01_CONSCRIPT_SQUAD_HARMLESS","SBP.SOVIET.M01_CONSCRIPT_SQUAD_HARMLESS_DURABLE","SBP.SOVIET.M01_CONSCRIPT_SQUAD_WOUNDED","SBP.SOVIET.M01_IL_2_STURMOVIK_ROCKET_SQUAD","SBP.SOVIET.M01_IL2_DOGFIGHT","SBP.SOVIET.M01_MEDIC","SBP.SOVIET.M02_COMBAT_ENGINEER_SQUAD","SBP.SOVIET.M02_REFUGEE_SQUAD","SBP.SOVIET.M08_COMBAT_ENGINEER_SQUAD","SBP.SOVIET.M08_T_34_76_SQUAD_SMALLPATH","SBP.SOVIET.M08_TANK_BUSTER_CONSCRIPT_SQUAD","SBP.SOVIET.M11_ANIA_SNIPER_SQUAD","SBP.SOVIET.M11_ISAKOVICH_SQUAD","SBP.SOVIET.M11_PARTISAN_SQUAD_KAR98K_RIFLE","SBP.SOVIET.M11_PARTISAN_SQUAD_NAGANT_RIFLE","SBP.SOVIET.M11_PARTISAN_SQUAD_NOWEAPON","SBP.SOVIET.M11_SNIPER_TEAM","SBP.SOVIET.M1910_MAXIM_HEAVY_MACHINE_GUN_SQUAD","SBP.SOVIET.M1910_MAXIM_HEAVY_MACHINE_GUN_SQUAD_MP","SBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY","SBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY_COMMANDER_MP","SBP.SOVIET.M1931_203MM_B_4_HOWITZER_ARTILLERY_MP","SBP.SOVIET.M1937_152MM_ML_20_ARTILLERY","SBP.SOVIET.M1937_152MM_ML_20_ARTILLERY_MP","SBP.SOVIET.M1937_53_K_45MM_AT_GUN_SQUAD","SBP.SOVIET.M1937_53_K_45MM_AT_GUN_SQUAD_MP","SBP.SOVIET.M1942_ZIS_3_76MM_AT_GUN_SQUAD","SBP.SOVIET.M1942_ZIS_3_76MM_AT_GUN_SQUAD_MP","SBP.SOVIET.M3A1_SCOUT_CAR_SQUAD","SBP.SOVIET.M3A1_SCOUT_CAR_SQUAD_MP","SBP.SOVIET.M5_HALFTRACK__ASSAULT_SQUAD_MP","SBP.SOVIET.M5_HALFTRACK_SQUAD","SBP.SOVIET.M5_HALFTRACK_SQUAD_MP","SBP.SOVIET.PARTISAN_SQUAD_GRANATEWERFER_34_81MM_MORTAR","SBP.SOVIET.PARTISAN_SQUAD_GRANATEWERFER_34_81MM_MORTAR_MP","SBP.SOVIET.PARTISAN_SQUAD_KAR98K_RIFLE","SBP.SOVIET.PARTISAN_SQUAD_KAR98K_RIFLE_MP","SBP.SOVIET.PARTISAN_SQUAD_MAXIM_HMG","SBP.SOVIET.PARTISAN_SQUAD_MAXIM_HMG_MP","SBP.SOVIET.PARTISAN_SQUAD_MG42_HMG","SBP.SOVIET.PARTISAN_SQUAD_MG42_HMG_MP","SBP.SOVIET.PARTISAN_SQUAD_NAGANT_RIFLE","SBP.SOVIET.PARTISAN_SQUAD_NAGANT_RIFLE_MP","SBP.SOVIET.PARTISAN_SQUAD_PM_82_41_MORTAR","SBP.SOVIET.PARTISAN_SQUAD_PM_82_41_MORTAR_MP","SBP.SOVIET.PARTISANS_LMG_MP","SBP.SOVIET.PARTISANS_PANZERSCHRECK_MP","SBP.SOVIET.PARTISANS_PTRS_MP","SBP.SOVIET.PARTISANS_RIFLE_MP","SBP.SOVIET.PARTISANS_SMG_MP","SBP.SOVIET.PENAL_BATTALION","SBP.SOVIET.PENAL_BATTALION_MP","SBP.SOVIET.PM_82_41_MORTAR_SQUAD","SBP.SOVIET.PM_82_41_MORTAR_SQUAD_MP","SBP.SOVIET.SHOCK_TROOPS","SBP.SOVIET.SHOCK_TROOPS_M11","SBP.SOVIET.SHOCK_TROOPS_MP","SBP.SOVIET.SNIPER_TEAM","SBP.SOVIET.SNIPER_TEAM_MALE","SBP.SOVIET.SNIPER_TEAM_MP","SBP.SOVIET.SOVIET_76MM_SHERMAN_MP","SBP.SOVIET.SOVIET_ALLIED_CARGO_PLANE","SBP.SOVIET.SOVIET_OFFICER_SQUAD","SBP.SOVIET.SOVIET_OFFICER_SQUAD_MP","SBP.SOVIET.STEAM_TRAIN","SBP.SOVIET.SU_76M","SBP.SOVIET.SU_76M_MP","SBP.SOVIET.SU_76M_TOW","SBP.SOVIET.SU_85","SBP.SOVIET.SU_85_MP","SBP.SOVIET.T_34_76_SQUAD","SBP.SOVIET.T_34_76_SQUAD_MP","SBP.SOVIET.T_34_85_ADVANCED_SQUAD_MP","SBP.SOVIET.T_34_85_SQUAD","SBP.SOVIET.T_34_85_SQUAD_MP","SBP.SOVIET.T_70M","SBP.SOVIET.T_70M_MP","SBP.SOVIET.TOW_BRIDGE_PARTISAN_SQUAD_AT","SBP.SOVIET.TOW_BRIDGE_PARTISAN_SQUAD_BASE","SBP.SOVIET.TOW_BRIDGE_PARTISAN_SQUAD_MAXIM","SBP.SOVIET.TOW_BRIDGE_PARTISAN_SQUAD_MORTAR","SBP.SOVIET.TOW_COLD_WEATHER_GUARDS_TROOPS","SBP.SOVIET.TOW_PARTISAN_SQUAD_KAR98K_RIFLE_MP","SBP.SOVIET.TOW_PARTISAN_SQUAD_LMG_SQUAD","SBP.SOVIET.TOW_PARTISAN_SQUAD_MAXIM_HMG_MP","SBP.SOVIET.US6_TRUCK_SQUAD","SBP.SOVIET.ZIS_6_TRANSPORT_TRUCK","SBP.SOVIET.ZIS_6_TRANSPORT_TRUCK_MP","ABILITY.SOVIET.ALLIED_AIR_SUPPLIES","ABILITY.SOVIET.ANTI_PERSONNEL_MINES","ABILITY.SOVIET.ANTI_TANK_GRENADE","ABILITY.SOVIET.ANTI_TANK_GRENADE_ASSAULT","ABILITY.SOVIET.ANTI_TANK_GRENADE_MP","ABILITY.SOVIET.ANTI_TANK_GRENADE_NO_REQUIREMENTS_MP","ABILITY.SOVIET.AT_76MM_HE_BARRAGE_ABILITY","ABILITY.SOVIET.AT_76MM_HE_BARRAGE_ABILITY_MP","ABILITY.SOVIET.AT_GUN_AMBUSH_TACTICS","ABILITY.SOVIET.B4_203MM_BARRAGE","ABILITY.SOVIET.B4_203MM_BARRAGE_COMMANDER_MP","ABILITY.SOVIET.B4_203MM_BARRAGE_COMMANDER_PRECISE_MP","ABILITY.SOVIET.B4_203MM_BARRAGE_COMMANDER_VET3_MP","ABILITY.SOVIET.B4_203MM_BARRAGE_COMMANDER_VICTORTARGET_MP","ABILITY.SOVIET.B4_203MM_BARRAGE_MP","ABILITY.SOVIET.B4_203MM_DIRECT_FIRE","ABILITY.SOVIET.B4_203MM_HOWITZER","ABILITY.SOVIET.BASE_CONSCRIPT_DISPATCH","ABILITY.SOVIET.BASE_CONSCRIPT_DISPATCH_MP","ABILITY.SOVIET.BOMBARDMENT_FX","ABILITY.SOVIET.BOOBY_TRAP","ABILITY.SOVIET.BUTTON_VEHICLE","ABILITY.SOVIET.BUTTON_VEHICLE_MP","ABILITY.SOVIET.BUTTON_VEHICLE_TOW","ABILITY.SOVIET.CAMPAIGN_SHOCK_FIRE_SUPERIORITY","ABILITY.SOVIET.CMD_120MM_MORTAR_CREW","ABILITY.SOVIET.CMD_ADVANCED_T34_85_MEDIUM_TANK","ABILITY.SOVIET.CMD_AT_GUN_AMBUSH_TACTICS_MP","ABILITY.SOVIET.CMD_CONSCRIPT_ASSAULT_PACKAGE","ABILITY.SOVIET.CMD_CONSCRIPT_EVASIVE_TACTICS","ABILITY.SOVIET.CMD_CONSCRIPT_REPAIR_KIT","ABILITY.SOVIET.CMD_GUARD_TROOPS","ABILITY.SOVIET.CMD_IS2_HEAVY_TANK","ABILITY.SOVIET.CMD_ISU_152","ABILITY.SOVIET.CMD_KATYUSHA","ABILITY.SOVIET.CMD_KV_1_UNLOCK","ABILITY.SOVIET.CMD_KV_8_UNLOCK_MP","ABILITY.SOVIET.CMD_ML_20","ABILITY.SOVIET.CMD_PENAL_BATTALION","ABILITY.SOVIET.CMD_RADIO_INTERCEPT","ABILITY.SOVIET.CMD_SHOCK_TROOPS","ABILITY.SOVIET.CMD_SOVIET_INDUSTRY","ABILITY.SOVIET.CMD_T34_85_MEDIUM_TANK","ABILITY.SOVIET.CMD_VEHICLE_CREW_REPAIR_TRAINING","ABILITY.SOVIET.COMMISSAR_SQUAD_MP","ABILITY.SOVIET.CONE_LOS_TOGGLE_ABILITY","ABILITY.SOVIET.CONE_LOS_TOGGLE_ABILITY_MP","ABILITY.SOVIET.CONSCRIPT_ANTI_TANK_GRENADE_ASSAULT_MP","ABILITY.SOVIET.CONSCRIPT_DISPATCH_MP","ABILITY.SOVIET.CONSCRIPT_EVASIVE_TACTICS","ABILITY.SOVIET.CONSCRIPT_EVASIVE_TACTICS_MP","ABILITY.SOVIET.CONSCRIPT_MOLOTOV_COCKTAIL","ABILITY.SOVIET.CONSCRIPT_MOLOTOV_COCKTAIL_MP","ABILITY.SOVIET.CONSCRIPT_OORAH","ABILITY.SOVIET.CONSCRIPT_OORAH_MP","ABILITY.SOVIET.CONSCRIPT_PTRS_UPGRADE","ABILITY.SOVIET.DSHK_ARMOR_PIERCING","ABILITY.SOVIET.DSHK_MP","ABILITY.SOVIET.ENGINEER_SALVAGE_WRECK","ABILITY.SOVIET.FATALITY_FEAR_PROPAGANDA_ARTILLERY","ABILITY.SOVIET.FATALITY_INCENDIARY_ARTILLERY","ABILITY.SOVIET.FATALITY_KATYUSHA_ROCKETS","ABILITY.SOVIET.FEAR_PROPAGANDA_ARTILLERY","ABILITY.SOVIET.FIELDCRAFT_TRIP_FLARE","ABILITY.SOVIET.FIELDCRAFT_TRIP_FLARE_MP","ABILITY.SOVIET.FIRE_ARTILLERY","ABILITY.SOVIET.FOR_MOTHER_RUSSIA_ABILITY","ABILITY.SOVIET.FORWARD_HQ","ABILITY.SOVIET.FRONTOVIKI_CONSCRIPT_DISPATCH","ABILITY.SOVIET.GUARDS_THROW_DEFENSIVE_GRENADE","ABILITY.SOVIET.GUARDS_THROW_DEFENSIVE_GRENADE_MP","ABILITY.SOVIET.HOLD_THE_LINE","ABILITY.SOVIET.IL_2_ANTI_TANK_BOMB_STRIKE","ABILITY.SOVIET.IL_2_ATTACK_STRAFE","ABILITY.SOVIET.IL_2_BOMBING_RUN_SP","ABILITY.SOVIET.IL_2_PRECISION_BOMB_STRIKE","ABILITY.SOVIET.IL_2_RECON","ABILITY.SOVIET.IL_2_RECON_SINGLEPASS_SP","ABILITY.SOVIET.IL_2_RECON_SP","ABILITY.SOVIET.IL_2_STURMOVIK_ATTACK","ABILITY.SOVIET.IL_2_STURMOVIK_ATTACK_ADVANCED","ABILITY.SOVIET.IL_2_SUPPORT","ABILITY.SOVIET.IL_2_SUPPORT_PRECISION_SP","ABILITY.SOVIET.IL_2_SUPPORT_SP","ABILITY.SOVIET.IS2_DISPATCH_SP","ABILITY.SOVIET.IS2_TANK_DEFENSIVE_WEAPON_MP","ABILITY.SOVIET.ISU_152_DISPATCH_SP","ABILITY.SOVIET.ISU_152_PIERCING_SHOT_ABILITY","ABILITY.SOVIET.ISU_152_PIERCING_SHOT_ABILITY_MP","ABILITY.SOVIET.ISU152_AMMO_SWITCH_AP_SHELL_MP","ABILITY.SOVIET.ISU152_AMMO_SWITCH_HE_SHELL_MP","ABILITY.SOVIET.ISU152_CONCRETE_PIERCING_ROUND_MP","ABILITY.SOVIET.KATUSHYA_CREEPING_BARRAGE_MP","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_BARRAGE","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_BARRAGE_MP","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_BARRAGE_VET3_MP","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_BARRAGE_VICTORTARGET_MP","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_CREEPING_BARRAGE_MP","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_PRECISION_BARRAGE","ABILITY.SOVIET.KAYTUSHA_ROCKET_TRUCK_PRECISION_BARRAGE_MP","ABILITY.SOVIET.KV_2","ABILITY.SOVIET.KV_2_SEIGE_MODE","ABILITY.SOVIET.KV_8_FLAME_45MM_TOGGLE_MP","ABILITY.SOVIET.LIGHT_ANTI_VEHICLE_MINES","ABILITY.SOVIET.M_42_AT_GUN","ABILITY.SOVIET.M11_PARTISANS_DISPATCH_KARK98K","ABILITY.SOVIET.M11_PARTISANS_DISPATCH_NAGANT","ABILITY.SOVIET.M11_SNIPER_DISPATCH02","ABILITY.SOVIET.M11_SNIPER_DISPATCH02_MP","ABILITY.SOVIET.M11_SNIPER_HOLD_FIRE","ABILITY.SOVIET.M3A1_M5_MOVING_ACCURACY_MP","ABILITY.SOVIET.M5_HALFTRACK_ASSAULT","ABILITY.SOVIET.M5_M3A1_OVERDRIVE","ABILITY.SOVIET.M5_M3A1_OVERDRIVE_MP","ABILITY.SOVIET.MANPOWER_BLITZ","ABILITY.SOVIET.MARK_VEHICLE","ABILITY.SOVIET.MAXIM_HMG_DISPATCH_SP","ABILITY.SOVIET.MERGE_ABILITY","ABILITY.SOVIET.MERGE_ABILITY_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_SLOW","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_SLOW_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_VET_1_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_VET3_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_ABILITY_VICTORTARGET_MP","ABILITY.SOVIET.ML_20_152MM_BARRAGE_PRECISON_ABILITY_MP","ABILITY.SOVIET.MORTAR_EXPLOSION_FX","ABILITY.SOVIET.MORTAR_EXPLOSION_FX_ICE","ABILITY.SOVIET.MORTAR_FIRE_FLARES_ABILITY_MP","ABILITY.SOVIET.MORTAR_PRECISION_BARRAGE_120MM_VET","ABILITY.SOVIET.MORTAR_PRECISION_BARRAGE_120MM_VET_MP","ABILITY.SOVIET.MORTAR_PRECISION_BARRAGE_82MM","ABILITY.SOVIET.MORTAR_PRECISION_BARRAGE_82MM_MP","ABILITY.SOVIET.NO_RETREAT_NO_SURRENDER","ABILITY.SOVIET.PARTISAN_DISPATCH","ABILITY.SOVIET.PARTISAN_DISPATCH_TOW","ABILITY.SOVIET.PARTISAN_MOLOTOV_COCKTAIL_MP","ABILITY.SOVIET.PARTISANS_COMMANDER_ANTI_INFANTRY","ABILITY.SOVIET.PARTISANS_COMMANDER_ANTI_VEHICLE","ABILITY.SOVIET.PENAL_OORAH_MP","ABILITY.SOVIET.PENAL_TROOP_DISPATCH_SINGLE_SP","ABILITY.SOVIET.PENAL_TROOP_DISPATCH_SP","ABILITY.SOVIET.RAPID_CONSCRIPTION","ABILITY.SOVIET.REPAIR_STATION","ABILITY.SOVIET.RG_42_ANTI_PERSONNEL_GRENADE","ABILITY.SOVIET.RG_42_ANTI_PERSONNEL_GRENADE_MP","ABILITY.SOVIET.RGD_1_SMOKE_GRENADE","ABILITY.SOVIET.RGD_1_SMOKE_GRENADE_MP","ABILITY.SOVIET.RGD_33_PARTISAN_GRENADE_MP","ABILITY.SOVIET.SALVAGE_KITS","ABILITY.SOVIET.SATCHEL_CHARGE_THROW_ABILITY_MP","ABILITY.SOVIET.SCORCHED_EARTH_POLICY","ABILITY.SOVIET.SCORCHED_EARTH_POLICY_MP","ABILITY.SOVIET.SHERMAN_SOVIET_DISPATCH","ABILITY.SOVIET.SHERMAN76MM_AMMO_SWITCH_AP_SHELL_MP","ABILITY.SOVIET.SHERMAN76MM_AMMO_SWITCH_HE_SHELL_MP","ABILITY.SOVIET.SHOCK_TROOP_DISPATCH_SP","ABILITY.SOVIET.SHOCK_TROOP_SMOKE_GRENADES","ABILITY.SOVIET.SMOKE_120MM_MORTAR_BARRAGE","ABILITY.SOVIET.SMOKE_120MM_MORTAR_BARRAGE_MP","ABILITY.SOVIET.SMOKE_SYNC_MORTAR_BARRAGE","ABILITY.SOVIET.SMOKE_SYNC_MORTAR_BARRAGE_MP","ABILITY.SOVIET.SNIPER_DELAYED_COVER_AUTO_CAMOUFLAGE","ABILITY.SOVIET.SNIPER_DELAYED_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.SOVIET.SNIPER_FIRE_FLARES_ABILITY","ABILITY.SOVIET.SNIPER_FIRE_FLARES_ABILITY_MP","ABILITY.SOVIET.SNIPER_HMG_SPRINT","ABILITY.SOVIET.SNIPER_HMG_SPRINT_MP","ABILITY.SOVIET.SNIPER_HOLD_FIRE","ABILITY.SOVIET.SNIPER_HOLD_FIRE_MP","ABILITY.SOVIET.SNIPER_IN_COVER_AUTO_CAMOUFLAGE","ABILITY.SOVIET.SNIPER_IN_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.SOVIET.SNIPER_SUPPRESSION_FIRE_ABILITY","ABILITY.SOVIET.SNIPER_SUPPRESSION_FIRE_ABILITY_MP","ABILITY.SOVIET.SOV_VEHICLE_HOLD_FIRE_MP","ABILITY.SOVIET.SOVIET_BARBED_WIRE_CUTTING_ABILITY","ABILITY.SOVIET.SOVIET_BARBED_WIRE_CUTTING_ABILITY_MP","ABILITY.SOVIET.SOVIET_CAMO_HOLD_FIRE_MP","ABILITY.SOVIET.SOVIET_CONSCRIPT_REPAIR_ABILITY","ABILITY.SOVIET.SOVIET_CONSCRIPT_REPAIR_ABILITY_MP","ABILITY.SOVIET.SOVIET_HQ_ENGINEER_CALL_IN","ABILITY.SOVIET.SOVIET_INDUSTRY","ABILITY.SOVIET.SOVIET_REPAIR_ABILITY","ABILITY.SOVIET.SOVIET_REPAIR_ABILITY_MP","ABILITY.SOVIET.SOVIET_WAR_MACHINE_SP","ABILITY.SOVIET.SPY_NETWORK","ABILITY.SOVIET.SU_76_BARRAGE_ABILITY","ABILITY.SOVIET.SU_76_BARRAGE_ABILITY_MP","ABILITY.SOVIET.SU76_SU85_ZIS3_53K_ISU152_INFANTRY_TRACKING","ABILITY.SOVIET.SU76_SU85_ZIS3_53K_ISU152_INFANTRY_TRACKING_MP","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE_120MM","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE_120MM_MP","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE_120MM_VICTORTARGET_MP","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE_MP","ABILITY.SOVIET.SYNC_MORTAR_BARRAGE_VICTORTARGET_MP","ABILITY.SOVIET.T_34_RAMMING_ABILITY","ABILITY.SOVIET.T_34_RAMMING_ABILITY_MP","ABILITY.SOVIET.T70_CREW_REPAIR_ABILITY","ABILITY.SOVIET.T70_CREW_REPAIR_ABILITY_MP","ABILITY.SOVIET.TANK_DETECTION_ABILITY","ABILITY.SOVIET.TANK_TRAPS","ABILITY.SOVIET.TANK_VET_POINT_CAPTURE_ABILITY","ABILITY.SOVIET.TANK_VET_POINT_CAPTURE_ABILITY_MP","ABILITY.SOVIET.TO_THE_LAST_MAN_MP","ABILITY.SOVIET.VEHICLE_CREW_REPAIR_ABILITY","ABILITY.SOVIET.VEHICLE_CREW_REPAIR_ABILITY_MP","ABILITY.SOVIET.VEHICLE_CREW_REPAIR_TOGGLE_MP","ABILITY.SOVIET.VEHICLE_RECON_TOGGLE","ABILITY.SOVIET.VEHICLE_RECON_TOGGLE_MP","ABILITY.SOVIET.VEHICLE_RECON_TOGGLE_VET2_MP","UPG.SOVIET.ABILITY_LOCK_OUT_CONSCRIPT","UPG.SOVIET.ABILITY_LOCK_OUT_CONSCRIPT_MP","UPG.SOVIET.ALLIED_AIR_SUPPLIES","UPG.SOVIET.ANTI_PERSONNEL_MINES","UPG.SOVIET.ANTI_TANK_GUN_AMBUSH_TACTICS","UPG.SOVIET.BASE_CONSCRIPT_AT_GRENADE_UNLOCK","UPG.SOVIET.BASE_CONSCRIPT_AT_GRENADE_UNLOCK_MP","UPG.SOVIET.BASE_CONSCRIPT_MOLOTOV_UNLOCK","UPG.SOVIET.BASE_CONSCRIPT_MOLOTOV_UNLOCK_MP","UPG.SOVIET.BASE_CONSCRIPT_OORAH_UNLOCK","UPG.SOVIET.BASE_CONSCRIPT_OORAH_UNLOCK_MP","UPG.SOVIET.BASE_CONSCRIPT_REPAIR_UNLOCK_MP","UPG.SOVIET.BASE_CONSCRIPT_RIFLE_UNLOCK_MP","UPG.SOVIET.BOOBY_TRAP","UPG.SOVIET.CAMOUFLAGE_NET_ACTIVATED_SOVIET","UPG.SOVIET.COMMANDER_T34_85_MP","UPG.SOVIET.COMMISSAR_SQUAD","UPG.SOVIET.CONSCRIPT_ASSAULT_PACKAGE","UPG.SOVIET.CONSCRIPT_ASSAULT_PACKAGE_INGAME","UPG.SOVIET.CONSCRIPT_AT_GRENADE_ASSAULT","UPG.SOVIET.CONSCRIPT_DP_28_LMG_PACKAGE","UPG.SOVIET.CONSCRIPT_EVASIVE_TACTICS","UPG.SOVIET.CONSCRIPT_MOBILIZE_UNLOCK","UPG.SOVIET.CONSCRIPT_PTRS","UPG.SOVIET.CONSCRIPT_PTRS_PACKAGE","UPG.SOVIET.CONSCRIPT_REPAIR_KIT","UPG.SOVIET.DEMO_IL_2_STRAFING_RUN","UPG.SOVIET.DSHK_MACHINEGUN","UPG.SOVIET.ENGINEER_FLAMETHROWER","UPG.SOVIET.ENGINEER_FLAMETHROWER_MP","UPG.SOVIET.ENGINEER_MINESWEEPER","UPG.SOVIET.ENGINEER_MINESWEEPER_MP","UPG.SOVIET.ENGINEER_SALVAGE_KIT","UPG.SOVIET.ENGINEER_SALVAGE_KITS_UNLOCK","UPG.SOVIET.EVASIVE_TACTICS_IS_ON","UPG.SOVIET.FEAR_PROPAGANDA","UPG.SOVIET.FIRE_ARTILLERY","UPG.SOVIET.FOR_MOTHER_RUSSIA","UPG.SOVIET.FORWARD_HQ","UPG.SOVIET.FORWARD_HQ_AURA","UPG.SOVIET.GUARD_ARCHETYPE","UPG.SOVIET.GUARD_DP_28_LMG_PACKAGE","UPG.SOVIET.GUARD_DP_28_LMG_PACKAGE_MP","UPG.SOVIET.GUARD_TROOPS","UPG.SOVIET.HM120_MORTAR_UNLOCK","UPG.SOVIET.HOLD_FIRE_SOVIET_CAMMO","UPG.SOVIET.HOLD_THE_LINE","UPG.SOVIET.HOWTIZER_203MM","UPG.SOVIET.HQ_ANTI_TANK_GRENADE","UPG.SOVIET.HQ_ANTI_TANK_GRENADE_MP","UPG.SOVIET.HQ_CONSCRIPT_REPAIR_KIT","UPG.SOVIET.HQ_HEALING_AURA","UPG.SOVIET.HQ_HEALING_AURA_M13","UPG.SOVIET.HQ_HEALING_AURA_MP","UPG.SOVIET.HQ_MOLOTOV_GRENADE_MP","UPG.SOVIET.IL_2_ANTI_TANK_BOMB","UPG.SOVIET.IL_2_BOMB_STRIKE","UPG.SOVIET.IL_2_RECON","UPG.SOVIET.IL_2_STURMOVIK_ATTACK","UPG.SOVIET.IL_2_STURMOVIK_ATTACK_ADVANCED","UPG.SOVIET.IL_2_SUPPORT","UPG.SOVIET.IS_2_SUPPORT","UPG.SOVIET.IS2_TOP_GUNNER","UPG.SOVIET.IS2_TOP_GUNNER_MP","UPG.SOVIET.ISAKOVICH_A01","UPG.SOVIET.ISU152_HE_ROUNDS","UPG.SOVIET.ISU152_TOP_GUNNER","UPG.SOVIET.ISU152_TOP_GUNNER_MP","UPG.SOVIET.ISU152_UNLOCK","UPG.SOVIET.KATYUSHA_UNLOCK","UPG.SOVIET.KV_1_UNLOCK_DEMO","UPG.SOVIET.KV_8_UNLOCK","UPG.SOVIET.KV1_UNLOCK","UPG.SOVIET.KV2_UNLOCK","UPG.SOVIET.LIGHT_ANTI_VEHICLE_MINES","UPG.SOVIET.M_42_AT_GUN","UPG.SOVIET.M3_HALFTRACK_ASSAULT","UPG.SOVIET.M5_HALFTRACK_72K_AA_GUN_PACKAGE","UPG.SOVIET.M5_HALFTRACK_72K_AA_GUN_PACKAGE_MP","UPG.SOVIET.MANPOWER_BLITZ","UPG.SOVIET.MARK_VEHICLE","UPG.SOVIET.ML_20_HOWITZER_UNLOCK","UPG.SOVIET.NKVD_ARCHETYPE","UPG.SOVIET.ORDER_227_DISABLE","UPG.SOVIET.ORDER_227_LOCKDOWN","UPG.SOVIET.ORDER227","UPG.SOVIET.PARTISAN_COMMANDER_ANTIVEHICLE_TROOPS","UPG.SOVIET.PARTISAN_COMMANDER_TROOPS","UPG.SOVIET.PARTISAN_HEALTH_UPGRADE","UPG.SOVIET.PARTISAN_HEALTH_UPGRADE_TANK_HUNTER","UPG.SOVIET.PARTISAN_TROOPS","UPG.SOVIET.PARTISAN_TROOPS_TOW","UPG.SOVIET.PENAL_BATTALION","UPG.SOVIET.PENAL_BATTALION_FLAMETHROWER_PACKAGE","UPG.SOVIET.PENAL_BATTALION_FLAMETHROWER_PACKAGE_MP","UPG.SOVIET.PPSH_41_SUB_MACHINE_GUN_UPGRADE","UPG.SOVIET.PPSH_41_SUB_MACHINE_GUN_UPGRADE_MP","UPG.SOVIET.PTRS_41_AT_RIFLE_PACKAGE_GUARD_TROOP","UPG.SOVIET.PTRS_41_AT_RIFLE_PACKAGE_GUARD_TROOP_ASSAULT_MP","UPG.SOVIET.PTRS_41_AT_RIFLE_PACKAGE_GUARD_TROOP_BETTER_BALANCED","UPG.SOVIET.PTRS_41_AT_RIFLE_PACKAGE_GUARD_TROOP_MP","UPG.SOVIET.RADIO_INTERCEPT","UPG.SOVIET.RAPID_CONSCRIPTION","UPG.SOVIET.REPAIR_BUNKER","UPG.SOVIET.SCORCHED_EARTH_POLICY","UPG.SOVIET.SCORCHED_EARTH_POLICY_MP","UPG.SOVIET.SHERMAN_SOVIET_DISPATCH","UPG.SOVIET.SHERMAN_SOVIET_TOP_GUNNER","UPG.SOVIET.SHOCK_ARCHETYPE","UPG.SOVIET.SHOCK_TROOPS","UPG.SOVIET.SHOCK_TROOPS_SP","UPG.SOVIET.SOVIET_GRENADES_LONG_TIMER","UPG.SOVIET.SOVIET_INDUSTRY","UPG.SOVIET.SPY_NETWORK","UPG.SOVIET.T34_85_ADVANCED_UNLOCK","UPG.SOVIET.T34_85_UNLOCK","UPG.SOVIET.TANK_DETECTION","UPG.SOVIET.TANK_RAID_ENABLED","UPG.SOVIET.TANK_TRAPS","UPG.SOVIET.TOW_1941_SOVIET","UPG.SOVIET.VEHICLE_SELF_REPAIR_TRAINING","EBP.WEST_GERMAN.ANTI_TANK_GUN_CREW_MP","EBP.WEST_GERMAN.ARMORED_CAR_SDKFZ_223","EBP.WEST_GERMAN.ARTY_CREW_MP","EBP.WEST_GERMAN.ASSAULT_PIONEER_MP","EBP.WEST_GERMAN.ASSAULT_PIONEERS_HEAVY_MINE_MP","EBP.WEST_GERMAN.BASE_FLAK_GUN_MP","EBP.WEST_GERMAN.BASE_FLAK_SANDBAGS","EBP.WEST_GERMAN.BUNKER_WESTGERMAN_MP","EBP.WEST_GERMAN.FALLSCHIRMJAGER_MP","EBP.WEST_GERMAN.FIELD_OFFICER_MP","EBP.WEST_GERMAN.FLAK_EMPLACEMENT","EBP.WEST_GERMAN.FLAK_EMPLACEMENT_BASE","EBP.WEST_GERMAN.FLAK_EMPLACEMENT_CREW","EBP.WEST_GERMAN.FLAK_EMPLACEMENT_CREW_BASE","EBP.WEST_GERMAN.GOLIATH_MP","EBP.WEST_GERMAN.GRANATWERFER_34_81MM_MORTAR_WG_MP","EBP.WEST_GERMAN.HALFTRACK_SDKFZ_251_17_FLAK_MP","EBP.WEST_GERMAN.HALFTRACK_SDKFZ_251_20_IR_SEARCHLIGHT_MP","EBP.WEST_GERMAN.HALFTRACK_SDKFZ_251_20_IR_SEARCHLIGHT_SP","EBP.WEST_GERMAN.HALFTRACK_SDKFZ_251_MP_2","EBP.WEST_GERMAN.HALFTRACK_SDKFZ_251_WURFRAHMEN_40_MP","EBP.WEST_GERMAN.HEAVY_ARMOR_SUPPORT_MP","EBP.WEST_GERMAN.HEAVY_ARMOR_SUPPORT_PREPLACED","EBP.WEST_GERMAN.HETZER_MP","EBP.WEST_GERMAN.HMG_CREW_MP","EBP.WEST_GERMAN.HOWITZER_105MM_LE_FH18_MINICHALLENGE","EBP.WEST_GERMAN.HOWITZER_105MM_LONG_RANGE","EBP.WEST_GERMAN.INFANTRY_SUPPORT_MP","EBP.WEST_GERMAN.INFANTRY_SUPPORT_PREPLACED","EBP.WEST_GERMAN.JAEGER_LIGHT_INFANTRY_RECON","EBP.WEST_GERMAN.JAGDPANZER_IV_SDKFZ_162_MP","EBP.WEST_GERMAN.JAGDTIGER_SDKFZ_186_MP","EBP.WEST_GERMAN.JU52_PARATROOPER_PLANE","EBP.WEST_GERMAN.JU52_PLANE","EBP.WEST_GERMAN.KING_TIGER_SDKFZ_182_MP","EBP.WEST_GERMAN.KUBELWAGEN_TYPE_82_MP","EBP.WEST_GERMAN.LE_IG_18_INF_SUPPORT_GUN_MP","EBP.WEST_GERMAN.LIGHT_ARMOR_SUPPORT_MP","EBP.WEST_GERMAN.LIGHT_ARMOR_SUPPORT_PREPLACED","EBP.WEST_GERMAN.MED_SUPPLY_STASH","EBP.WEST_GERMAN.MG34_HMG_CREW","EBP.WEST_GERMAN.MG34_HMG_MP","EBP.WEST_GERMAN.MG42_HMG_WG_MP","EBP.WEST_GERMAN.MINE_FIELD_WESTGERMAN_MP","EBP.WEST_GERMAN.MORTAR_TEAM_CREW_MP","EBP.WEST_GERMAN.OBERSOLDATEN_MP","EBP.WEST_GERMAN.OKW_HOWITZER_105MM_LE_FH18_MP","EBP.WEST_GERMAN.OKW_HOWITZER_CREW_MP","EBP.WEST_GERMAN.OSTWIND_FLAK_PANZER_WEST_GERMAN_MP","EBP.WEST_GERMAN.PAK40_75MM_AT_GUN_WG_MP","EBP.WEST_GERMAN.PAK43_88MM_AT_GUN_WESTGERMAN_MP","EBP.WEST_GERMAN.PANTHER_SDKFZ_171_AUSF_G_MP","EBP.WEST_GERMAN.PANTHER_SDKFZ_171_COMMANDER_MP","EBP.WEST_GERMAN.PANZER_II_LUCHS_SDKFZ_123_MP","EBP.WEST_GERMAN.PANZER_IV_SDKFZ_AUSF_J_MP","EBP.WEST_GERMAN.PANZERFUSILIER_MP","EBP.WEST_GERMAN.PUMA_SDKFZ_234_MP","EBP.WEST_GERMAN.RAKETENWERFER43_88MM_PUPPCHEN_ANTITANK_GUN_MP","EBP.WEST_GERMAN.REINFORCED_BARBED_WIRE_FENCE_MP","EBP.WEST_GERMAN.REINFORCED_BARBED_WIRE_TANK_TRAP_MP","EBP.WEST_GERMAN.SCHU_MINE_42_MP","EBP.WEST_GERMAN.SIPHON_STRUCTURE","EBP.WEST_GERMAN.STURMTIGER_606_38CM_RW_61_MP","EBP.WEST_GERMAN.SWS_HALFTRACK_MP","EBP.WEST_GERMAN.SWS_HALFTRACK_SP","EBP.WEST_GERMAN.TERROR_OFFICER_GUARD_MP","EBP.WEST_GERMAN.TERROR_OFFICER_MP","EBP.WEST_GERMAN.URBAN_ASSAULT_LIGHT_INFANTRY","EBP.WEST_GERMAN.VOLKSGRENADIER_MP","EBP.WEST_GERMAN.WEST_GERMAN_BASE_STAMPER","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_BARREL","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_CRATES_01","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_CRATES_02","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_GENERATOR","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_SANDBAG_01","EBP.WEST_GERMAN.WEST_GERMAN_COMMAND_POST_SANDBAG_02","EBP.WEST_GERMAN.WEST_GERMAN_HQ_MP","EBP.WEST_GERMAN.WEST_GERMAN_HQ_WRECK_MP","EBP.WEST_GERMAN.WEST_GERMAN_INVISI_REPAIR_STATION_MP","EBP.WEST_GERMAN.WG_BARBED_WIRE_FENCE_MP","EBP.WEST_GERMAN.WG_SANDBAG_FENCE_MP","SBP.WEST_GERMAN.ARMORED_CAR_SDKFZ_234_SQUAD_MP","SBP.WEST_GERMAN.ASSAULT_PIONEER_SQUAD_MP","SBP.WEST_GERMAN.COMMAND_KING_TIGER_SQUAD_MP","SBP.WEST_GERMAN.FALLSCHIRMJAGER_SQUAD_MP","SBP.WEST_GERMAN.FIELD_OFFICER_SQUAD_MP","SBP.WEST_GERMAN.FLAK_EMPLACEMENT","SBP.WEST_GERMAN.FLAK_EMPLACEMENT_BASE","SBP.WEST_GERMAN.GOLIATH_MP","SBP.WEST_GERMAN.GRW34_81MM_MORTAR_SQUAD_MP","SBP.WEST_GERMAN.HETZER_SQUAD_MP","SBP.WEST_GERMAN.HOWITZER_105MM_LE_FH18_ARTILLERY_MINICHALLENGE","SBP.WEST_GERMAN.HOWITZER_105MM_LONG_RANGE","SBP.WEST_GERMAN.JAEGER_LIGHT_INFANTRY_RECON_SQUAD_MP","SBP.WEST_GERMAN.JAGDPANZER_TANK_DESTROYER_SQUAD_MP","SBP.WEST_GERMAN.JAGDTIGER_TD_SQUAD_MP","SBP.WEST_GERMAN.JU52_PARATROOPER_PLANE","SBP.WEST_GERMAN.JU52_PLANE","SBP.WEST_GERMAN.KING_TIGER_SQUAD_MP","SBP.WEST_GERMAN.KUBELWAGEN_SQUAD_MP","SBP.WEST_GERMAN.LE_IG_18_INF_SUPPORT_GUN_SQUAD_MP","SBP.WEST_GERMAN.MG34_HEAVY_MACHINE_GUN_SQUAD_MP","SBP.WEST_GERMAN.MG42_HEAVY_MACHINE_GUN_SQUAD_WG_MP","SBP.WEST_GERMAN.MORTAR_250_HALFTRACK_SQUAD_WESTGERMAN_MP","SBP.WEST_GERMAN.OBERSOLDATEN_SQUAD_MP","SBP.WEST_GERMAN.OKW_HOWITZER_105MM_LE_FH18_ARTILLERY_MP","SBP.WEST_GERMAN.OSTWIND_SQUAD_WESTGERMAN_MP","SBP.WEST_GERMAN.PAK40_75MM_AT_GUN_SQUAD_WG_MP","SBP.WEST_GERMAN.PAK43_88MM_AT_GUN_SQUAD_WESTGERMAN_MP","SBP.WEST_GERMAN.PANTHER_AUSF_G_SQUAD_MP","SBP.WEST_GERMAN.PANTHER_COMMANDER_SQUAD_MP","SBP.WEST_GERMAN.PANZER_II_LUCHS_SQUAD_MP","SBP.WEST_GERMAN.PANZER_IV_AUSF_J_BATTLE_GROUP_MP","SBP.WEST_GERMAN.PANZERFUSILIER_SQUAD_MP","SBP.WEST_GERMAN.RAKETENWERFER43_88MM_PUPPCHEN_ANTITANK_GUN_SQUAD_MP","SBP.WEST_GERMAN.SCOUTCAR_223_SQUAD","SBP.WEST_GERMAN.SDKFZ_251_17_FLAK_HALFTRACK_SQUAD_MP","SBP.WEST_GERMAN.SDKFZ_251_20_IR_SEARCHLIGHT_HALFTRACK_SQUAD_MP","SBP.WEST_GERMAN.SDKFZ_251_20_IR_SEARCHLIGHT_HALFTRACK_SQUAD_SP","SBP.WEST_GERMAN.SDKFZ_251_HALFTRACK_SQUAD_MP_2","SBP.WEST_GERMAN.SDKFZ_251_WURFRAHMEN_40_HALFTRACK_SQUAD_MP","SBP.WEST_GERMAN.STURMTIGER_SQUAD_MP","SBP.WEST_GERMAN.SWS_HALFTRACK_SQUAD_MP","SBP.WEST_GERMAN.SWS_HALFTRACK_SQUAD_SP","SBP.WEST_GERMAN.TERROR_OFFICER_SQUAD_MP","SBP.WEST_GERMAN.URBAN_ASSAULT_LIGHT_INFANTRY","SBP.WEST_GERMAN.VOLKSGRENADIER_SQUAD_MP","ABILITY.WEST_GERMAN.ADVANCED_SIPHON","ABILITY.WEST_GERMAN.AIRBORNE_ASSAULT","ABILITY.WEST_GERMAN.ARMOR_BLITZ_MP","ABILITY.WEST_GERMAN.ASSAULT_ARTILLERY","ABILITY.WEST_GERMAN.ASSAULT_MOVE_MP","ABILITY.WEST_GERMAN.ASSAULT_PIONEER_BARBED_WIRE_CUTTING_ABILITY_MP","ABILITY.WEST_GERMAN.ASSAULT_PIONEER_DROP_MEDPACK_ABILITY_MP","ABILITY.WEST_GERMAN.BARRAGE_ABILITY_MC","ABILITY.WEST_GERMAN.BASE_BUILDING_RETREAT_POINT_MP","ABILITY.WEST_GERMAN.BLENDKORPER_2H_WAFFEN_ELITE","ABILITY.WEST_GERMAN.BREAKTHROUGH_2","ABILITY.WEST_GERMAN.BREAKTHROUGH_TACTICS","ABILITY.WEST_GERMAN.BUILDING_SELF_DESTRUCT","ABILITY.WEST_GERMAN.BUILDING_SWITCH_FUEL","ABILITY.WEST_GERMAN.BUILDING_SWITCH_MUNITIONS","ABILITY.WEST_GERMAN.COMBAT_BLITZ_MP","ABILITY.WEST_GERMAN.COMMAND_MARK_VEHICLE","ABILITY.WEST_GERMAN.COMMAND_PANTHER","ABILITY.WEST_GERMAN.COMMAND_ROYAL_TIGER_DISPATCH","ABILITY.WEST_GERMAN.CONSTRUCT_ARMORED_INFANTRY_COMMAND","ABILITY.WEST_GERMAN.CONSTRUCT_INFANTRY_BARRACKS","ABILITY.WEST_GERMAN.CONSTRUCT_TANK_COMMAND","ABILITY.WEST_GERMAN.COORDINATED_BARRAGE","ABILITY.WEST_GERMAN.DEFENSIVE_MOVE_MP","ABILITY.WEST_GERMAN.EARLY_WARNING_FLARES","ABILITY.WEST_GERMAN.FALLSCHIRMJAEGER","ABILITY.WEST_GERMAN.FALLSCHIRMJAEGER_GREANDE","ABILITY.WEST_GERMAN.FALLSCHIRMJAEGER_PANZERFAUST","ABILITY.WEST_GERMAN.FALLSCHRIMJAEGER_CAMO","ABILITY.WEST_GERMAN.FATALITY_FLARE_ARTILLERY","ABILITY.WEST_GERMAN.FATALITY_STUKA_FRAGMENTATION_AIRSTRIKE","ABILITY.WEST_GERMAN.FATALITY_STURMTIGER_SATURATION","ABILITY.WEST_GERMAN.FATALITY_WALKING_STUKA_BARRAGE","ABILITY.WEST_GERMAN.FIELD_DEFENSES","ABILITY.WEST_GERMAN.FLAK_EMPLACEMENT_SELF_REPAIR","ABILITY.WEST_GERMAN.FLAK_HALFTRACK_CONCEALING_SMOKE_MP","ABILITY.WEST_GERMAN.FLAME_HALTRACK_DISPATCH","ABILITY.WEST_GERMAN.FLARE_ARTILLERY","ABILITY.WEST_GERMAN.FLARE_TRAP_CAPTURE_POINT","ABILITY.WEST_GERMAN.FOR_THE_FATHERLAND","ABILITY.WEST_GERMAN.FORTIFY_POSITION_MP","ABILITY.WEST_GERMAN.FORWARD_RECEIVERS","ABILITY.WEST_GERMAN.GOLIATH_DISPATCH","ABILITY.WEST_GERMAN.GRW34_MORTAR_COUNTER_BARRAGE_ATTACK_MP","ABILITY.WEST_GERMAN.GRW34_MORTAR_COUNTER_BARRAGE_WEAPON_WG_MP","ABILITY.WEST_GERMAN.GRW34_MORTAR_TEAM_MORTAR_BARRAGE_WG_MP","ABILITY.WEST_GERMAN.GRW34_MORTAR_TEAM_MORTAR_VICTORTARGET_BARRAGE_WG_MP","ABILITY.WEST_GERMAN.GRW34_MORTAR_TEAM_SMOKE_BARRAGE_WG_MP","ABILITY.WEST_GERMAN.HEAT_SHELLS_ABILITY_MP","ABILITY.WEST_GERMAN.HEAT_SHELLS_UNLOCK","ABILITY.WEST_GERMAN.HEAVY_FORTIFICATIONS","ABILITY.WEST_GERMAN.HETZER_DISPATCH","ABILITY.WEST_GERMAN.HOWITZER_105MM_EMPLACEMENT_UNLOCK_OKW","ABILITY.WEST_GERMAN.HOWITZER_105MM_LONG_RANGE_BARRAGE","ABILITY.WEST_GERMAN.HOWITZER_105MM_OFFMAP_BARRAGE","ABILITY.WEST_GERMAN.HOWITZER_TOGGLE_FIRE_PM","ABILITY.WEST_GERMAN.INFILTRATION_TACTICS_GRENADE","ABILITY.WEST_GERMAN.INFILTRATION_TACTICS_UNLOCK","ABILITY.WEST_GERMAN.INFRARED_STG44","ABILITY.WEST_GERMAN.JAEGER_BOOBY_TRAP","ABILITY.WEST_GERMAN.JAEGER_LIGHT_INFANTRY_CAMO","ABILITY.WEST_GERMAN.JAEGER_LIGHT_INFANTRY_RECON_DISPATCH","ABILITY.WEST_GERMAN.JAGDTIGER","ABILITY.WEST_GERMAN.JAGDTIGER_128MM_SUPPORTING_FIRE","ABILITY.WEST_GERMAN.JAGDTIGER_PIERCING_SHELL_ABILITY_MP","ABILITY.WEST_GERMAN.KING_TIGER_COMMAND_MODE_MP","ABILITY.WEST_GERMAN.KING_TIGER_DISPATCH","ABILITY.WEST_GERMAN.KUBELWAGEN_DETECTION_MP","ABILITY.WEST_GERMAN.KUBELWAGEN_HOLD_FIRE_MP","ABILITY.WEST_GERMAN.KUBELWAGEN_IN_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.WEST_GERMAN.LE_IG_18_BARRAGE_WG_MP","ABILITY.WEST_GERMAN.LE_IG_18_BARRAGE_WG_VET_MP","ABILITY.WEST_GERMAN.LE_IG_18_HOLLOW_CHARGE_BARRAGE_WG_MP","ABILITY.WEST_GERMAN.LE_IG_18_HOLLOW_CHARGE_BARRAGE_WG_VET_MP","ABILITY.WEST_GERMAN.MG34_DISPATCH","ABILITY.WEST_GERMAN.MG34_PHOSPHORUS_ROUNDS_MP","ABILITY.WEST_GERMAN.MINESWEEPER_DEPLOY_MP","ABILITY.WEST_GERMAN.MINESWEEPER_PUT_AWAY_MP","ABILITY.WEST_GERMAN.MORTAR_HALFTRACK_WEST_GERMAN","ABILITY.WEST_GERMAN.OFFMAP_NEBEL_BARRAGE_MP","ABILITY.WEST_GERMAN.OKW_HOLD_FIRE_MP","ABILITY.WEST_GERMAN.OKW_RATKEN_VEHICLE_HOLD_FIRE_MP","ABILITY.WEST_GERMAN.OKW_SECTOR_ASSAULT","ABILITY.WEST_GERMAN.OKW_STUKA_AERIAL_SUPERIORITY_RECON","ABILITY.WEST_GERMAN.OKW_VEHICLE_HOLD_FIRE_MP","ABILITY.WEST_GERMAN.OSTWIND_DISPATCH","ABILITY.WEST_GERMAN.PAK40_CRITICAL_SHOTS_WG_MP","ABILITY.WEST_GERMAN.PANZER_IV_GROUP_DISPATCH","ABILITY.WEST_GERMAN.PANZERFUSILIER_AT_RIFLE_GRENADE","ABILITY.WEST_GERMAN.PANZERFUSILIER_GRENADE","ABILITY.WEST_GERMAN.PANZERFUSILIERS_DISPATCH","ABILITY.WEST_GERMAN.PANZERFUSILIERS_FLARE","ABILITY.WEST_GERMAN.PIONEER_STUN_GRENADE_MP","ABILITY.WEST_GERMAN.PIONEER_VOLKS_SALVAGE","ABILITY.WEST_GERMAN.PIONEER_VOLKS_THROUGH_SALVAGE","ABILITY.WEST_GERMAN.PUMA_AIMED_SHOT_MP","ABILITY.WEST_GERMAN.PUMA_SMOKE_SCREEN","ABILITY.WEST_GERMAN.PYRO_VOLKS","ABILITY.WEST_GERMAN.RADIO_SILENCE","ABILITY.WEST_GERMAN.RAKETEN_IN_COVER_AUTO_CAMOUFLAGE_MP","ABILITY.WEST_GERMAN.RAKTEN_CAMOUFLAGE_MP","ABILITY.WEST_GERMAN.RECON_STANCE_MP","ABILITY.WEST_GERMAN.RECOUP_LOSSES","ABILITY.WEST_GERMAN.REFUEL_TANK_WG_SP","ABILITY.WEST_GERMAN.ROCKET_BARRAGE","ABILITY.WEST_GERMAN.SDKFZ_251_17_FLAK_HALFTRACK_DEPLOY_DEFENS","ABILITY.WEST_GERMAN.SDKFZ_251_17_FLAK_HALFTRACK_DEPLOY_WEAPON","ABILITY.WEST_GERMAN.SDKFZ_251_17_FLAK_HALFTRACK_DEPLOY_WEAPON_VET","ABILITY.WEST_GERMAN.SIGNAL_FLAGS","ABILITY.WEST_GERMAN.SIPHON_INCREASE_RESOURCES_ADVANCED_MP","ABILITY.WEST_GERMAN.SIPHON_INCREASE_RESOURCES_MP","ABILITY.WEST_GERMAN.SPEARHEAD_MP","ABILITY.WEST_GERMAN.STALKER_STATE_MP","ABILITY.WEST_GERMAN.STURMTIGER_380MM_ROCKET_ATTACK","ABILITY.WEST_GERMAN.STURMTIGER_380MM_ROCKET_RELOAD","ABILITY.WEST_GERMAN.STURMTIGER_DISPATCH","ABILITY.WEST_GERMAN.STURMTIGER_NAHVW_CLOSE_RANGE_GRENADE_TARGETED","ABILITY.WEST_GERMAN.SUPPORT_TRUCK_GAIN_RESOURCECS","ABILITY.WEST_GERMAN.SUPPORT_TRUCK_TARGET_SETUP","ABILITY.WEST_GERMAN.SUPPORT_TRUCK_TARGET_UNSETUP","ABILITY.WEST_GERMAN.SUPPRESSIVE_FIRE_MP","ABILITY.WEST_GERMAN.SWS_HALFTRACK_DISPATCH","ABILITY.WEST_GERMAN.SWS_HALFTRACK_FORWARD_RECEIVERS","ABILITY.WEST_GERMAN.SWS_HALFTRACK_INTERVAL_DISPATCH","ABILITY.WEST_GERMAN.TANK_COMMANDER_UNLOCK","ABILITY.WEST_GERMAN.TANK_THROW_DEFENSIVE_GRENADE_MP","ABILITY.WEST_GERMAN.TANK_THROW_DEFENSIVE_GRENADE_UNLOCK_MP","ABILITY.WEST_GERMAN.TERROR_OFFICER","ABILITY.WEST_GERMAN.TERROR_OFFICER_FORCE_RETREAT","ABILITY.WEST_GERMAN.TERROR_OFFICER_MARK_TARGET","ABILITY.WEST_GERMAN.THROUGH_SALVAGE","ABILITY.WEST_GERMAN.TIGER_PROWL_JAGDPANZER_MP","ABILITY.WEST_GERMAN.TIGER_PROWL_MP","ABILITY.WEST_GERMAN.URBAN_ASSAULT_LIGHT_INFANTRY","ABILITY.WEST_GERMAN.URBAN_ASSAULT_LIGHT_INFANTRY_THROW_ABILITY_MP","ABILITY.WEST_GERMAN.VALIANT_ASSAULT","ABILITY.WEST_GERMAN.VEHICLE_CRITICAL_REPAIR_UNLOCK","ABILITY.WEST_GERMAN.VEHICLE_EMERGENCY_REPAIR_ABILITY_MP","ABILITY.WEST_GERMAN.VEHICLE_EMERGENCY_REPAIR_ABILITY_SWS_MP","ABILITY.WEST_GERMAN.VOLKS_PANZERFAUST_MP","ABILITY.WEST_GERMAN.VOLKSGRENADIER_FIRE_GRENADE_MP","ABILITY.WEST_GERMAN.VOLKSGRENADIER_GRENADE_MP","ABILITY.WEST_GERMAN.VOLKSGRENADIER_PANZERFAUST_MP","ABILITY.WEST_GERMAN.VOLKSGRENADIER_PANZERFAUST_VET_4_MP","ABILITY.WEST_GERMAN.WAFFEN_BOOBY_TRAP_CAPTURE_POINT","ABILITY.WEST_GERMAN.WAFFEN_ELITE_BUNDLED_ASSAULT_GRENADE","ABILITY.WEST_GERMAN.WALKING_STUKA_ROCKET_BARRAGE_CREEPING_MP","ABILITY.WEST_GERMAN.WALKING_STUKA_ROCKET_BARRAGE_CREEPING_NAPALM_MP","ABILITY.WEST_GERMAN.WEST_GERMAN_REPAIR_ABILITY_MP","ABILITY.WEST_GERMAN.WG_HQ_PIONEER_CALL_IN","ABILITY.WEST_GERMAN.ZEROING_ARTILLERY","UPG.WEST_GERMAN.ABILITY_LOCK_OUT_STURMTIGER_NOT_RELOADED","UPG.WEST_GERMAN.ABILITY_LOCK_OUT_STURMTIGER_RELOADING","UPG.WEST_GERMAN.ABILITY_LOCK_OUT_SWS_TRUCK","UPG.WEST_GERMAN.ADVANCED_SIPHON","UPG.WEST_GERMAN.AERIAL_SUPERIORITY_STUKA_RECON_PLANE","UPG.WEST_GERMAN.AIRBORNE_ASSAULT","UPG.WEST_GERMAN.ASSAULT_ARTILLERY","UPG.WEST_GERMAN.ASSAULT_PIONEER_COMBAT_UPGRADE","UPG.WEST_GERMAN.ASSAULT_PIONEER_PANZERSCHRECK_UPGRADE","UPG.WEST_GERMAN.ASSAULT_PIONEER_REPAIR_UPGRADE","UPG.WEST_GERMAN.BREAKTHROUGH_2","UPG.WEST_GERMAN.BREAKTHROUGH_TACTICS","UPG.WEST_GERMAN.BUILDING_1","UPG.WEST_GERMAN.BUILDING_2","UPG.WEST_GERMAN.BUILDING_3","UPG.WEST_GERMAN.COMMAND_PANTHER","UPG.WEST_GERMAN.COMMAND_ROYAL_TIGER_DISPATCH","UPG.WEST_GERMAN.CONSTRUCT_BASE_BUILDING_UPGRADE","UPG.WEST_GERMAN.FALLSCHRIMJAGER_DISPATCH","UPG.WEST_GERMAN.FIELD_DEFENSES","UPG.WEST_GERMAN.FIRST_SWS_HALFTRACK_LOCKOUT","UPG.WEST_GERMAN.FLAK_GUN_UNLOCK_UPGRADE","UPG.WEST_GERMAN.FLAK_PANZER_DEFENSIVES","UPG.WEST_GERMAN.FLAK_PANZER_IS_SETUP","UPG.WEST_GERMAN.FLAME_HALFTRACK_DISPATCH","UPG.WEST_GERMAN.FLAMMPANZER_38T_HETZER","UPG.WEST_GERMAN.FLARE_ARTILLERY","UPG.WEST_GERMAN.FOR_THE_FATHER_LAND","UPG.WEST_GERMAN.FORWARD_RECEIVERS","UPG.WEST_GERMAN.GOLIATH_REMOTE_CONTROLLED_BOMB","UPG.WEST_GERMAN.HEALING_POINT_UNLOCK_UPGRADE","UPG.WEST_GERMAN.HEAT_SHELLS","UPG.WEST_GERMAN.HEAVY_FORTIFICATIONS","UPG.WEST_GERMAN.HOWITZER_105MM_EMPLACEMENT_OKW","UPG.WEST_GERMAN.HOWITZER_105MM_OFFMAP_BARRAGE","UPG.WEST_GERMAN.INFILTRATION_TACTICS","UPG.WEST_GERMAN.INFRARED_STG44","UPG.WEST_GERMAN.JAEGER_LIGHT_INFANTRY_RECON_DISPATCH","UPG.WEST_GERMAN.JAGDTIGER","UPG.WEST_GERMAN.JAGDTIGER_ABILITY_AP_LOCK_OUT","UPG.WEST_GERMAN.JAGDTIGER_ABILITY_BARRAGE_LOCK_OUT","UPG.WEST_GERMAN.JAGDTIGER_ENGINE_IMPROVEMENTS_I_MP","UPG.WEST_GERMAN.KING_TIGER_TOP_GUNNER_MP","UPG.WEST_GERMAN.MEDIC_HEALING_MP","UPG.WEST_GERMAN.MEDICAL_SUPPLIES_0_USES_REMAINING","UPG.WEST_GERMAN.MEDICAL_SUPPLIES_1_USE_REMAINING","UPG.WEST_GERMAN.MEDICAL_SUPPLIES_2_USES_REMAINING","UPG.WEST_GERMAN.MG34_DISPATCH","UPG.WEST_GERMAN.OKW_SECTOR_ASSAULT","UPG.WEST_GERMAN.OSTWIND_DISPATCH","UPG.WEST_GERMAN.PANZER_IV_GROUP_DISPATCH","UPG.WEST_GERMAN.PANZER_IV_SIDE_SKIRTS_MP","UPG.WEST_GERMAN.PANZERFUSILER_DISPATCH","UPG.WEST_GERMAN.PANZERFUSILIER_G43","UPG.WEST_GERMAN.PANZERSCHRECK_UNLOCKED","UPG.WEST_GERMAN.PYRO_VOLKS","UPG.WEST_GERMAN.RADIO_SILENCE","UPG.WEST_GERMAN.RECOUP_ACTIVE","UPG.WEST_GERMAN.RECOUP_LOSS","UPG.WEST_GERMAN.REPAIR_ENGINEERS_MP","UPG.WEST_GERMAN.REPAIR_POINT_UNLOCK_UPGRADE","UPG.WEST_GERMAN.RESOURCE_POINT_SIPHON","UPG.WEST_GERMAN.RETREAT_POINT_UNLOCK_UPGRADE","UPG.WEST_GERMAN.ROCKET_BARRAGE","UPG.WEST_GERMAN.SDKFZ_251_HALFTRACK_FLAMMPANZERWAGEN_UPGRADE_MP_2","UPG.WEST_GERMAN.SIGNAL_FLAGS","UPG.WEST_GERMAN.SIPHON_LOCK_OUT","UPG.WEST_GERMAN.STURMTIGER_DISPATCH","UPG.WEST_GERMAN.SWS_INTERVAL_UNLOCK","UPG.WEST_GERMAN.SWS_STARTING_DISPATCH_UNLOCK","UPG.WEST_GERMAN.TANK_COMMANDER","UPG.WEST_GERMAN.TANK_COMMANDER_UNLOCK","UPG.WEST_GERMAN.TANK_GRENADE","UPG.WEST_GERMAN.TERROR_OFFICER","UPG.WEST_GERMAN.THROUGH_SALVAGE","UPG.WEST_GERMAN.URBAN_ASSAULT_LIGHT_INFANTRY","UPG.WEST_GERMAN.VALIANT_ASSAULT","UPG.WEST_GERMAN.VEHICLE_CRITICAL_REPAIR","UPG.WEST_GERMAN.VOLKS_FLAMETHROWER_MP","UPG.WEST_GERMAN.VOLKS_STG44_UPGRADE","UPG.WEST_GERMAN.WAFFEN_INFRARED_STG44","UPG.WEST_GERMAN.WAFFEN_MG34_LMG_MP","UPG.WEST_GERMAN.WARNING_FLARES","UPG.WEST_GERMAN.WG_HETZER_TOP_GUNNER_MP","UPG.WEST_GERMAN.WG_PANTHER_TOP_GUNNER_MP","UPG.WEST_GERMAN.ZEROING_ARTILLERY","ABILITY.GLOBAL.ARMY_ITEM_GLOBAL_COVER_TRAINING","ABILITY.GLOBAL.ARMY_ITEM_SOVIET_NOT_GONNA_DIE_LIKE_THIS","ABILITY.GLOBAL.AT_76MM_SINGLE_SHOT_ACCURATE","ABILITY.GLOBAL.BLIZZARD_EFFECT","ABILITY.GLOBAL.BLIZZARD_EFFECT_DEEP_SNOW_CAMO","ABILITY.GLOBAL.BLIZZARD_EFFECT_MORTARS","ABILITY.GLOBAL.BLIZZARD_EFFECT_VEHICLE","ABILITY.GLOBAL.BLIZZARD_HOWITZER","ABILITY.GLOBAL.BONUS_0","ABILITY.GLOBAL.BONUS_1","ABILITY.GLOBAL.BONUS_2","ABILITY.GLOBAL.BONUS_2B","ABILITY.GLOBAL.BONUS_3","ABILITY.GLOBAL.BONUS_3B","ABILITY.GLOBAL.BONUS_3C","ABILITY.GLOBAL.BONUS_BACK","ABILITY.GLOBAL.BREAKTHROUGH_TOW","ABILITY.GLOBAL.CAMOUFLAGE_CONSTRUCTION","ABILITY.GLOBAL.CAMOUFLAGE_CONSTRUCTION_ANIA","ABILITY.GLOBAL.CAMPAIGN_STUKA_STRAFE_LONG","ABILITY.GLOBAL.CAPTURE_SPEED","ABILITY.GLOBAL.COMMISSAR_SHOT_227","ABILITY.GLOBAL.COMMISSAR_SHOT_227_ENEMY","ABILITY.GLOBAL.COMMISSAR_SQUAD_TOW","ABILITY.GLOBAL.CONVOY_BUILDBARRICADE","ABILITY.GLOBAL.COVER_ANIMATION_TEST","ABILITY.GLOBAL.DIG_OUT_OF_MUD","ABILITY.GLOBAL.DISPATCH_BRIDGE_PARTISAN","ABILITY.GLOBAL.DISPATCH_BRIDGE_PARTISAN_AT","ABILITY.GLOBAL.DISPATCH_BRIDGE_PARTISAN_HMG","ABILITY.GLOBAL.DISPATCH_BRIDGE_PARTISAN_MORTAR","ABILITY.GLOBAL.DROP_WEAPONS","ABILITY.GLOBAL.FATALITY_BULLSEYE","ABILITY.GLOBAL.FATALITY_COORDINATED_MORTAR_BOMBARDMENT","ABILITY.GLOBAL.FATALITY_DEFAULT","ABILITY.GLOBAL.FATALITY_HOWITZER_105MM_BARRAGE","ABILITY.GLOBAL.FATALITY_HOWITZER_240MM","ABILITY.GLOBAL.FATALITY_LIGHT_SUPPORT_ARTILLERY","ABILITY.GLOBAL.FATALITY_PROTOTYPE","ABILITY.GLOBAL.FATALITY_RAILWAY_GUN_ARTILLERY","ABILITY.GLOBAL.FATALITY_TIME_ON_TARGET_ARTILLERY","ABILITY.GLOBAL.FIRE_DOT","ABILITY.GLOBAL.FLAME_THROWER_ABILITY","ABILITY.GLOBAL.FORWARD_REPAIR_STATION_TOW","ABILITY.GLOBAL.FROZEN_ICON_TEST","ABILITY.GLOBAL.GARRISONED_SQUAD_FACING","ABILITY.GLOBAL.GARRISONED_SQUAD_FACING_UNSET","ABILITY.GLOBAL.HEAL_IN_COVER","ABILITY.GLOBAL.HOWITZER_105MM_BARRAGE_SHORT","ABILITY.GLOBAL.HOWITZER_105MM_BARRAGE_SHORT_PRECISE","ABILITY.GLOBAL.HOWITZER_105MM_DUMMY","ABILITY.GLOBAL.IL_2_ATTACK_STRAFE_HMG","ABILITY.GLOBAL.IL_2_PRECISION_BOMB_STRIKE_TOW","ABILITY.GLOBAL.KV_2_TOW","ABILITY.GLOBAL.LIGHT_ARTILLERY_M10","ABILITY.GLOBAL.M01_IL2_DOGFIGHT_PASS","ABILITY.GLOBAL.M01_IL2_PRECISION_BOMB_STRIKE","ABILITY.GLOBAL.M01_MEDIC_HEAL","ABILITY.GLOBAL.M01_MEDIC_HEAL_CONSTANT","ABILITY.GLOBAL.M01_MORTAR_SINGLE_PRECISE_HARMLESS","ABILITY.GLOBAL.M01_SPRINT_OUT_OF_COMBAT","ABILITY.GLOBAL.M01_STUKA_BOMBING_STRIKE","ABILITY.GLOBAL.M01_STUKA_DOGFIGHT_PASS","ABILITY.GLOBAL.M01_STUKA_STRAFE_FAST","ABILITY.GLOBAL.M01_WOUNDED","ABILITY.GLOBAL.M11_LIGHT_FIRE","ABILITY.GLOBAL.M12_HOWITZER_BARRAGE","ABILITY.GLOBAL.M14_GUARD_TROOP_DISPATCH","ABILITY.GLOBAL.M14_OFF_MAP_SMOKE_BARRAGE","ABILITY.GLOBAL.M24_ANTI_TANK_BUNDLED_GRENADE","ABILITY.GLOBAL.MECHANIZED_ASSAULT_GROUP_TOW","ABILITY.GLOBAL.MOLTKE_DET_PACK","ABILITY.GLOBAL.MUDDY_POINT","ABILITY.GLOBAL.NO_RETREAT_NO_SURRENDER_TOW","ABILITY.GLOBAL.OFF_MAP_ARTILLERY","ABILITY.GLOBAL.OFF_MAP_ARTILLERY_PERCISE","ABILITY.GLOBAL.OFF_MAP_ARTILLERY_PERCISE_FAST","ABILITY.GLOBAL.OFF_MAP_ARTILLERY_PERCISE_SEP","ABILITY.GLOBAL.OFF_MAP_ARTY_SINGLE_SHOT_INSTANT","ABILITY.GLOBAL.OFFICER_AIR_RECON","ABILITY.GLOBAL.OFFICER_CLOSE_AIR_SUPPORT","ABILITY.GLOBAL.OFFICER_FRAGMENTATION_BOMB","ABILITY.GLOBAL.PARTISAN_REPAIR_ABILITY","ABILITY.GLOBAL.PARTISAN_SPRINT","ABILITY.GLOBAL.PREVENT_SUPPRESSION","ABILITY.GLOBAL.PRODUCTION_SPEED","ABILITY.GLOBAL.RADIO_TOWER_REVEAL","ABILITY.GLOBAL.RAILWAY_GUN_ARTILLERY_SINGLE","ABILITY.GLOBAL.READY_UP","ABILITY.GLOBAL.REV_OUT_OF_MUD","ABILITY.GLOBAL.SHOCK_TROOP_FULL_AUTO","ABILITY.GLOBAL.SP_DROP_WEAPONS","ABILITY.GLOBAL.SP_OFF_MAP_ARTY_HARMLESS","ABILITY.GLOBAL.SP_OFF_MAP_ARTY_REAL","ABILITY.GLOBAL.SP_SINGLE_SHOT_MORTAR","ABILITY.GLOBAL.SP_SINGLE_SHOT_MORTAR_M01","ABILITY.GLOBAL.SP_SPRINT","ABILITY.GLOBAL.SP_SPRINT_TOGGLEABLE","ABILITY.GLOBAL.SPY_NETWORK_TOW","ABILITY.GLOBAL.STUKA_BOMBING_STRIKE_W_SMOKE","ABILITY.GLOBAL.STUKA_FAKE_BOMBING_STRIKE","ABILITY.GLOBAL.STUKA_FAKE_STRAFE","ABILITY.GLOBAL.STUKA_STRAFE","ABILITY.GLOBAL.STUKA_STRAFE_M02","ABILITY.GLOBAL.STUKA_STRAFE_M09","ABILITY.GLOBAL.TANK_BUSTER_CONSCRIPT_DISPATCH","ABILITY.GLOBAL.TOW_AIRFIELD_DISPATCH_KV1","ABILITY.GLOBAL.TOW_AIRFIELD_DISPATCH_KV2","ABILITY.GLOBAL.TOW_AIRFIELD_DISPATCH_KV8","ABILITY.GLOBAL.TOW_AIRFIELD_DISPATCH_T34","ABILITY.GLOBAL.TOW_AIRFIELD_STUKA_BOMBING_RUN","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_IS2","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_KAT","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_KV1","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_SU76","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_T34","ABILITY.GLOBAL.TOW_STALINGRAD_DISPATCH_T70","ABILITY.GLOBAL.TRANSFER_ORDERS","ABILITY.GLOBAL.TROOP_TRAINING_TOW","ABILITY.GLOBAL.TUNSTEN_SHELLS_TOW","ABILITY.GLOBAL.WARMING_ANIMATION_TEST","ABILITY.GLOBAL.WE_SURRENDER","SLOT_ITEM.AEC_TARGET_OPTICS_SLOT_ITEM_MP","SLOT_ITEM.AEC_TARGET_TURRET_SLOT_ITEM_MP","SLOT_ITEM.AEC_TREAD_SHOT_MP","SLOT_ITEM.AEF_CALLIOPE_DUMMY_SLOT_ITEM","SLOT_ITEM.AEF_SHERMAN_DUMMY_SLOT_ITEM","SLOT_ITEM.AEF_VEHICLE_ENTERS_INFANTRY_BUFF_APPLIED","SLOT_ITEM.AEF_WHITE_PHOSPHOROUS_MORTAR_UI_ITEM","SLOT_ITEM.AEF_WHITE_PHOSPHOROUS_SHELLS_UI_ITEM","SLOT_ITEM.AEF_WRENCH_ICON_SLOT_ITEM","SLOT_ITEM.AMBUSH_CAMO_PORTRAIT_ICON_ITEM","SLOT_ITEM.AMBUSH_CAMO_SLOT_ITEM","SLOT_ITEM.AMBUSH_CAMO_VISUAL_ITEM","SLOT_ITEM.ARMOR_BLITZ_ITEM","SLOT_ITEM.ASSAULT_ENGINEER_FLAMETHROWER","SLOT_ITEM.ASSAULT_MOVE_ITEM","SLOT_ITEM.AT_76MM_HE_ROUND_ITEM","SLOT_ITEM.AT_76MM_HE_ROUND_ITEM_MP","SLOT_ITEM.AVRE_CREW_SHRAPNEL_GRENADE_SLOT_ITEM_MP","SLOT_ITEM.AVRE_RELOAD_ACTIVE","SLOT_ITEM.AVRE_SPIGOT_MORTAR_MP","SLOT_ITEM.AVRE_SPIGOT_MORTAR_VET_3_MP","SLOT_ITEM.AXIS_ASSAULT_GRENADIER_GRENADE","SLOT_ITEM.AXIS_BLINDING_GRENADE","SLOT_ITEM.AXIS_BLINDING_GRENADE_MP","SLOT_ITEM.AXIS_PANZER_GRENADIER_GRENADE","SLOT_ITEM.AXIS_PANZER_GRENADIER_GRENADE_MP","SLOT_ITEM.AXIS_PG_GRENADE_CAMPAIGN","SLOT_ITEM.AXIS_PG_GRENADE_CAMPAIGN_MP","SLOT_ITEM.AXIS_PG_GRENADE_TUTORIAL","SLOT_ITEM.BAZOOKA_MP","SLOT_ITEM.BLENDKORPER_2H_SMOKE_GRENADE_ITEM_MP","SLOT_ITEM.BOFOR_40MM_AA_MODE_ACTIVATED_MAIN_GUN","SLOT_ITEM.BOFORS_HOLD_FULL","SLOT_ITEM.BOFORS_SUPPRESSIVE_BARRAGE_ROUND_ITEM_MP","SLOT_ITEM.BOFORS_SUPPRESSIVE_BARRAGE_ROUND_ITEM_VICTOR_TARGET_MP","SLOT_ITEM.BOOT_STOMP","SLOT_ITEM.BOYS_ANTI_TANK_RIFLE_MP","SLOT_ITEM.BOYS_ANTI_TANK_RIFLE_SNIPER_MP","SLOT_ITEM.BOYS_SNIPER_RIFLE_ITEM_MP","SLOT_ITEM.BREN_LMG_ICON_DUMMY","SLOT_ITEM.BRIT_17_POUNDER_FLARE_MP","SLOT_ITEM.BRIT_17_POUNDER_HOLD_FULL","SLOT_ITEM.BRIT_17_POUNDER_PIERCING_SHOT_MP","SLOT_ITEM.BRIT_COMMAND_VEHICLE_ITEM","SLOT_ITEM.BRIT_CROC_DUMMY_SLOT_ITEM","SLOT_ITEM.BRIT_EMPLACEMENT_BRACED","SLOT_ITEM.BRIT_EMPLACEMENT_HOLD_FIRE","SLOT_ITEM.BRIT_FIREFLY_TULIP_SLOT_ITEM","SLOT_ITEM.BRIT_HOLD_THE_LINE","SLOT_ITEM.BRIT_MORTAR_PIT_HOLD_FULL","SLOT_ITEM.BRIT_REINFORCE_THE_FRONT","SLOT_ITEM.BRIT_SNIPER_BOYS_ANTI_TANK_CRITICAL_SHOT_MP","SLOT_ITEM.BRIT_UNIT_LOCK_OUT_SLOT_ITEM","SLOT_ITEM.BRUMMBAR_CRITICAL_SHOT_MP","SLOT_ITEM.CAPTAIN_GARRISON_ITEM","SLOT_ITEM.CAPTURE_INTEL_SLOTITEM","SLOT_ITEM.CARRIER_SUPPRESS_ACTIVE","SLOT_ITEM.CAVALRY_AT_SATCHEL_ITEM","SLOT_ITEM.CENTUAR_AA_MODE_ACTIVATED_MAIN_GUN","SLOT_ITEM.CHURUCHILL_SUPPORT_NEGATE","SLOT_ITEM.COMET_SMOKE_SHELL_SHOT_MP","SLOT_ITEM.COMET_SMOKE_SHELL_WP_SHOT_MP","SLOT_ITEM.COMMAND_PANTHER_AURA","SLOT_ITEM.COMMANDO_BREN_LMG_MP","SLOT_ITEM.COMMANDO_DE_LISLE_CARBINE_MP","SLOT_ITEM.COMMANDO_DE_LISLE_CARBINE_SLOT_MP","SLOT_ITEM.COMMANDO_N69_GRENADE_MP","SLOT_ITEM.COMMANDO_THOMPSON_MP","SLOT_ITEM.COMMANDO_THOMPSON_SLOT_MP","SLOT_ITEM.COMMISSAR_SHOT_227","SLOT_ITEM.COMMISSAR_SHOT_227_ENEMY","SLOT_ITEM.CONSCRIPT_MOLOTOV","SLOT_ITEM.CONSCRIPT_MOLOTOV_MP","SLOT_ITEM.COVER_SMOKE_GRENADE_ITEM","SLOT_ITEM.DEF_MOVE_ITEM","SLOT_ITEM.DOUBLE_SWEEP","SLOT_ITEM.DP_28_LIGHT_MACHINE_GUN_PACKAGE","SLOT_ITEM.DP_28_LIGHT_MACHINE_GUN_PACKAGE_MOVING_MP","SLOT_ITEM.DP_28_LIGHT_MACHINE_GUN_PACKAGE_MOVING_NO_PRONE_MP","SLOT_ITEM.DP_28_LIGHT_MACHINE_GUN_PACKAGE_MP","SLOT_ITEM.DSHK38_TURRET_MOUNTED_IS2","SLOT_ITEM.DSHK38_TURRET_MOUNTED_IS2_MP","SLOT_ITEM.DSHK38_TURRET_MOUNTED_ISU152","SLOT_ITEM.DSHK38_TURRET_MOUNTED_ISU152_MP","SLOT_ITEM.DUMMY_FORTIFIED__SLOT_ITEM","SLOT_ITEM.DUMMY_SLOT_ITEM","SLOT_ITEM.DUMMY_SLOT_ITEM_QUAD","SLOT_ITEM.ELEFANT_CRITICAL_SHOT_MP","SLOT_ITEM.ENGINEER_SALVAGE_KIT_DUMMY","SLOT_ITEM.FLAK_HALFTRACK_ICON_ITEM","SLOT_ITEM.FLAMETHROWER_ROKS3_ACCESSORY","SLOT_ITEM.FLAMETHROWER_ROKS3_FAKE","SLOT_ITEM.FLAMETHROWER_ROKS3_ITEM","SLOT_ITEM.FLAMETHROWER_ROKS3_ITEM_MP","SLOT_ITEM.FOR_THE_FATHERLAND_ACTIVE","SLOT_ITEM.FRWD_HQ_SMOKE_MARKER_GRENADE_MP","SLOT_ITEM.FWD_HQ_EMPLACEMENT_SUPPORT","SLOT_ITEM.G43_SNIPER_INCENDIARY_SLOT_ITEM_MP","SLOT_ITEM.GENERIC_MG34_LMG_MP","SLOT_ITEM.GRENADIER_MG42_LMG","SLOT_ITEM.GRENADIER_MG42_LMG_MOVING_MP","SLOT_ITEM.GRENADIER_MG42_LMG_MOVING_NO_PRONE_MP","SLOT_ITEM.GRENADIER_MG42_LMG_MP","SLOT_ITEM.GRENADIER_PANZERFAUST","SLOT_ITEM.GRENADIER_PANZERFAUST_MP","SLOT_ITEM.GROUND_ATTACK_SNIPER_RIFLE_ITEM","SLOT_ITEM.GUARD_TROOP_ASSAULT_PACKAGE","SLOT_ITEM.HALFTRACK_FLAMETHROWER_LEFT","SLOT_ITEM.HALFTRACK_FLAMETHROWER_LEFT_MP","SLOT_ITEM.HALFTRACK_FLAMETHROWER_RIGHT","SLOT_ITEM.HALFTRACK_FLAMETHROWER_RIGHT_MP","SLOT_ITEM.HETZER_FLAMETHROWER_ITEM_MP","SLOT_ITEM.HULLDOWN_SLOT_ITEM","SLOT_ITEM.INFRARED_SQUAD_SETUP","SLOT_ITEM.ISU_PIERCING_SHOT_ROUND_ITEM","SLOT_ITEM.ISU_PIERCING_SHOT_ROUND_ITEM_MP","SLOT_ITEM.JAEGER_G43_RIFLE_ITEM","SLOT_ITEM.JAEGER_G43_RIFLE_ITEM_MP","SLOT_ITEM.JAEGER_LIGHT_RECON_G43","SLOT_ITEM.JAEGER_PANZERGREN_G43_RIFLE_ITEM_MP","SLOT_ITEM.KAR_98K_ANTITANK_RIFLE_GRENADE_SLOT_ITEM","SLOT_ITEM.KAR_98K_ANTITANK_RIFLE_GRENADE_SLOT_ITEM_MP","SLOT_ITEM.KAR_98K_RIFLE_GRENADE_SLOT_ITEM","SLOT_ITEM.KAR_98K_RIFLE_GRENADE_SLOT_ITEM_MP","SLOT_ITEM.KAR_98K_RIFLE_GRENADE_SLOT_ITEM_TUTORIAL","SLOT_ITEM.KV_8_45MM_GUN_ITEM","SLOT_ITEM.KV_8_ATO_41_FLAMETHROWER_ITEM_MP","SLOT_ITEM.KWK_20MM_222_ARMORED_CAR_MP","SLOT_ITEM.LAND_MATTRESS_25LB_ROCKET","SLOT_ITEM.LAND_MATTRESS_60LB_ROCKET","SLOT_ITEM.LAND_MATTRESS_EMPTY","SLOT_ITEM.LAND_MATTRESS_PHOSPHORUS_ROCKET","SLOT_ITEM.LAND_MATTRESS_ROCKET_MARKER","SLOT_ITEM.LEE_ENFIELD_RIFLE_GRENADE_SLOT_ITEM_MP","SLOT_ITEM.LIEUTENANT_GARRISON_ITEM","SLOT_ITEM.LIGHT_AT_MINE_RECENTLY_HIT_HEAVY_VEHICLE","SLOT_ITEM.LIGHT_AT_MINE_RECENTLY_HIT_LIGHT_VEHICLE","SLOT_ITEM.M01_CONSCRIPT_MOSIN_NAGANT","SLOT_ITEM.M15A1_AA_MODE_ACTIVATED","SLOT_ITEM.M15A1_AA_MODE_ACTIVATED_LEFT","SLOT_ITEM.M15A1_AA_MODE_ACTIVATED_MAIN_GUN","SLOT_ITEM.M17_RIFLE_GRENADE_SLOT_ITEM_MP","SLOT_ITEM.M1919A6_LMG_ICON_DUMMY","SLOT_ITEM.M1C_GARAND","SLOT_ITEM.M1C_PATHFINDER_GARAND","SLOT_ITEM.M23_SMOKE_STREAM_GRENADE_ANTI_TANK_ITEM_MP","SLOT_ITEM.M23_SMOKE_STREAM_GRENADE_ITEM_MP","SLOT_ITEM.M24_ANTI_TANK_GRENADIER_GRENADE","SLOT_ITEM.M2HB_50CAL_SHERMAN","SLOT_ITEM.M2HB_TURRET_MOUNTED_M8_MP","SLOT_ITEM.M2HB_TURRET_MOUNTED_SHERMAN_MP","SLOT_ITEM.M5_STUART_DAMAGE_ENGINE_SHOT_SLOT_ITEM_MP","SLOT_ITEM.M5_STUART_SHELL_SHOCK_SHOT_SLOT_ITEM_MP","SLOT_ITEM.M8_CANISTER_SHOT_SLOT_ITEM_MP","SLOT_ITEM.M8_GREYHOUND_RECON_ACTIVATED","SLOT_ITEM.MAJOR_GARRISON_ITEM","SLOT_ITEM.MG34_PINTLE_HETZER","SLOT_ITEM.MG42_TURRET_MOUNTED_BRUMMBAR","SLOT_ITEM.MG42_TURRET_MOUNTED_BRUMMBAR_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_KING_TIGER_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_PANTHER","SLOT_ITEM.MG42_TURRET_MOUNTED_PANTHER_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_PANTHER_WG_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_PZIV","SLOT_ITEM.MG42_TURRET_MOUNTED_PZIV_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_STUGIV","SLOT_ITEM.MG42_TURRET_MOUNTED_STUGIV_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_TIGER","SLOT_ITEM.MG42_TURRET_MOUNTED_TIGER_MP","SLOT_ITEM.MG42_TURRET_MOUNTED_TIGER_TOW","SLOT_ITEM.MINESWEEPER","SLOT_ITEM.MORTAR_FLARE_MP","SLOT_ITEM.MOSIN_NAGANT_SNIPER_RIFLE_ITEM","SLOT_ITEM.MOSIN_NAGANT_SNIPER_RIFLE_ITEM_MP","SLOT_ITEM.OBERSOLDATEN_MG34_LMG_MOVING_MP","SLOT_ITEM.OBERSOLDATEN_MG34_LMG_MOVING_NO_PRONE_MP","SLOT_ITEM.OBERSOLDATEN_MP44_INFARED","SLOT_ITEM.OPEL_SUPPLY_SLOT_ITEM","SLOT_ITEM.PAK40_CRITICAL_SHOT_MP","SLOT_ITEM.PAK43_CRITICAL_SHOT_MP","SLOT_ITEM.PANZER_GRENADIER_MP44_ITEM","SLOT_ITEM.PANZER_GRENADIER_MP44_ITEM_MP","SLOT_ITEM.PANZERBUSCHE_39","SLOT_ITEM.PANZERBUSCHE_39_MP","SLOT_ITEM.PANZERFUISILIER_FLARE_MP","SLOT_ITEM.PANZERFUSILIER_AT_RIFLE_GRENADE","SLOT_ITEM.PANZERFUSILIER_G43","SLOT_ITEM.PANZERFUSILIER_GRENADE","SLOT_ITEM.PANZERSHRECK","SLOT_ITEM.PANZERSHRECK_AT_WEAPON_ITEM","SLOT_ITEM.PANZERSHRECK_DESTROY_ENGINE","SLOT_ITEM.PANZERSHRECK_MP","SLOT_ITEM.PANZERSHRECK_SLOT1","SLOT_ITEM.PANZERSHRECK_SLOT1_MP","SLOT_ITEM.PANZERSHRECK_SLOT2","SLOT_ITEM.PANZERSHRECK_SLOT2_MP","SLOT_ITEM.PARADROP_REINFORCE_ITEM","SLOT_ITEM.PARATROOPER_M1919A6_LMG_MOVING_NO_PRONE_MP","SLOT_ITEM.PARATROOPER_M1919A6_LMG_MP","SLOT_ITEM.PARATROOPER_MK2_GRENADE_MP","SLOT_ITEM.PARATROOPER_THOMPSON_DUMMY","SLOT_ITEM.PARATROOPER_THOMPSON_MP","SLOT_ITEM.PARTISAN_DP_28_LIGHT_MACHINE_GUN_PACKAGE_MP","SLOT_ITEM.PARTISAN_MG42_LMG_MP","SLOT_ITEM.PATHFINDERS_SNIPER_ITEM","SLOT_ITEM.PENAL_TROOP_SATCHEL_CHARGE_ITEM_MP","SLOT_ITEM.PERSHING_HVAP_PIERCING_ITEM_MP","SLOT_ITEM.PIAT_SPIGOT_MORTAR_MP","SLOT_ITEM.PIONEER_FLAMETHROWER","SLOT_ITEM.PIONEER_FLAMETHROWER_ABILITY","SLOT_ITEM.PIONEER_FLAMETHROWER_ABILITY_MP","SLOT_ITEM.PIONEER_FLAMETHROWER_MP","SLOT_ITEM.PIONEER_STUN_GRENADE_MP","SLOT_ITEM.PM_AEF_OFFENSIVE_PUNCH_ITEM","SLOT_ITEM.PPSH41_ASSAULT_PACKAGE","SLOT_ITEM.PPSH41_ASSAULT_PACKAGE_DUMMY_ITEM_MP","SLOT_ITEM.PPSH41_ASSAULT_PACKAGE_MP","SLOT_ITEM.PTRS_41_ANTI_TANK_RIFLE_CONSCRIPT_MP","SLOT_ITEM.PTRS_41_ANTI_TANK_RIFLE_GUARD_TROOP","SLOT_ITEM.PTRS_41_ANTI_TANK_RIFLE_GUARD_TROOP_ASSAULT_MP","SLOT_ITEM.PTRS_41_ANTI_TANK_RIFLE_GUARD_TROOP_MP","SLOT_ITEM.PTRS_41_ANTI_TANK_RIFLE_PARTISAN_TROOP_MP","SLOT_ITEM.PUMA_AIMED_SHOT_MP","SLOT_ITEM.PUMA_CRITICAL_SHOT_MP","SLOT_ITEM.RANGER_PANZERSHRECK_MP","SLOT_ITEM.REAR_ECHELON_RIFLE_GRENADE_ACTIVATED","SLOT_ITEM.REAR_ECHELON_RIFLE_VOLLEY_FIRE","SLOT_ITEM.RECOUP_ACTIVE","SLOT_ITEM.RGD_1_SMOKE_GRENADE_ITEM","SLOT_ITEM.RGD_1_SMOKE_GRENADE_ITEM_MP","SLOT_ITEM.RGD_33_SLEEVED_GRENADE_ITEM","SLOT_ITEM.RGD_33_SLEEVED_GRENADE_ITEM_LONGTIMER","SLOT_ITEM.RGD_33_SLEEVED_GRENADE_ITEM_MP","SLOT_ITEM.RIFLEMAN_AT_RIFLE_GRENADE","SLOT_ITEM.RIFLEMEN_30_CAL","SLOT_ITEM.RIFLEMEN_FLARE","SLOT_ITEM.RIFLEMEN_M1918_BAR_MP","SLOT_ITEM.RIFLEMEN_MK2_GRENADE_MP","SLOT_ITEM.RIFLEMEN_TRAINING_DUMMY_CARBINE","SLOT_ITEM.RIFLEMEN_TRAINING_SATCHEL_ITEM","SLOT_ITEM.ROKS_2_FLAMETHROWER_ITEM","SLOT_ITEM.ROKS_2_FLAMETHROWER_ITEM_MP","SLOT_ITEM.RPG_40_ANTI_TANK_GRENADE_MP","SLOT_ITEM.RPG_43_ANTI_TANK_GRENADE","SLOT_ITEM.RPG_43_ANTI_TANK_GRENADE_MP","SLOT_ITEM.SAPPER_BREN_LIGHT_MACHINE_GUN_MP","SLOT_ITEM.SAPPER_STUN_GRENADE_MP","SLOT_ITEM.SAPPER_VICKERS_K_LIGHT_MACHINE_GUN_MP","SLOT_ITEM.SATCHEL_CHARGE_ITEM_MP","SLOT_ITEM.SELF_REPAIR_DUMMY_SLOT_ITEM","SLOT_ITEM.SHERMAN_BATTLE_GROUP_ITEM_MP","SLOT_ITEM.SHOCK_TROOP_RG_42_GRENADE","SLOT_ITEM.SHOCK_TROOP_RG_42_GRENADE_MP","SLOT_ITEM.SIPHON_ACTIVE","SLOT_ITEM.SNIPER_FLARE_MP","SLOT_ITEM.SNIPER_RIFLE_ITEM","SLOT_ITEM.SNIPER_RIFLE_ITEM_MP","SLOT_ITEM.SNIPER_SMOKE_MARKER_GRENADE_MP","SLOT_ITEM.SNIPER_SUPPRESSIVE_VOLLEY_MP","SLOT_ITEM.SOVIET_FLAG","SLOT_ITEM.SPEARHEAD_ITEM","SLOT_ITEM.STALK_ITEM","SLOT_ITEM.STORMTROOPER_MP44_MP","SLOT_ITEM.STUG_CRITICAL_SHOT_MP","SLOT_ITEM.STUG_ELEFANT_PAK40_PAK43_BRUMMBAR_CRITICAL_SHOT","SLOT_ITEM.STUG_ELEFANT_PAK40_PAK43_BRUMMBAR_CRITICAL_SHOT_MP","SLOT_ITEM.STURMTIGER_RELOAD_ACTIVE","SLOT_ITEM.SU76M_HE_ROUND_ITEM","SLOT_ITEM.SU76M_HE_ROUND_ITEM_MP","SLOT_ITEM.SUPPORT_SQUAD_SETUP","SLOT_ITEM.SUPPRESS_FIRE_ITEM","SLOT_ITEM.SWS_LOCKDOWN_SETUP","SLOT_ITEM.TANK_HUNTER_SHOCK_BAZOOKA_VET","SLOT_ITEM.TIGER_ACE_CRITICAL_SHOT_MP","SLOT_ITEM.TIGER_FLARE_TOW","SLOT_ITEM.TOMMY_BREN_LIGHT_MACHINE_GUN_MP","SLOT_ITEM.TOMMY_FLAMETHROWER","SLOT_ITEM.TOMMY_GAMMON_BOMB_HEAVY","SLOT_ITEM.TOMMY_GAMMON_BOMB_MEDIUM","SLOT_ITEM.TOMMY_HEAT_GRENADE","SLOT_ITEM.TOMMY_MILLS_BOMB","SLOT_ITEM.TOMMY_MILLS_BOMB_ASSAULT","SLOT_ITEM.TOMMY_OFFICER_SMOKE_MARKER_GRENADE_MP","SLOT_ITEM.TOMMY_SCOPED_RIFLE_ITEM_MP","SLOT_ITEM.TOMMY_STEN_SMG","SLOT_ITEM.TROOP_SUPPORT_DUMMY_MEDIC","SLOT_ITEM.UNIVERSAL_CARRIER_VICKERS_K_PACKAGE_MP","SLOT_ITEM.UNIVERSAL_CARRIER_VICKERS_MMG_SUPPRESSIVE_MP","SLOT_ITEM.URBAN_ASSAULT_FLAMETHROWER_MP","SLOT_ITEM.URBAN_ASSAULT_SATCHEL_CHARGE_ITEM_MP","SLOT_ITEM.VALENTINE_SMOKE_MARKER_GRENADE_MP","SLOT_ITEM.VICKERS_K_LIGHT_MACHINE_GUN_MP","SLOT_ITEM.VOLKSGRENADIER_FIRE_GRENADE_MP","SLOT_ITEM.VOLKSGRENADIER_GRENADE_MP","SLOT_ITEM.VOLKSGRENADIER_MP44_ITEM_MP","SLOT_ITEM.VOLKSGRENADIER_PANZERFAUST_MP","SLOT_ITEM.VOLKSGRENADIER_PANZERFAUST_VET_4_MP","SLOT_ITEM.WAFFEN_BUNDLED_ASSAULT_GRENADE","SLOT_ITEM.WEST_GERMAN_MINESWEEPER","SLOT_ITEM.WG_BLENDKORPER_SMOKE_UI_ITEM","SLOT_ITEM.WG_PANZER_IV_ARMORED_SKIRTS","CRIT._NO_CRITICAL","CRIT._NO_CRITICAL_MINE","CRIT._NO_CRITICAL_REAR","CRIT._SP_ANIA_EXPLOSIVE","CRIT._SP_ANIA_KILLED","CRIT.ASSAULT_MODIFIERS","CRIT.ATTACK_PLAN_MODIFIERS","CRIT.AXIS_ASSAULT_MODIFIERS","CRIT.BRIDGE_DEMOLITION_MAKE_WRECK","CRIT.BRIDGE_MAKE_WRECK","CRIT.BUILDING_ABANDON","CRIT.BUILDING_BRACED","CRIT.BUILDING_DESTROY","CRIT.BUILDING_DESTROY_CONSTRUCTION","CRIT.BUILDING_DESTROY_SUPPLY_CENTER","CRIT.BUILDING_FIRE_DAMAGE_DOT","CRIT.BUILDING_FIRE_DAMAGE_PANEL","CRIT.BUILDING_PANEL_DAMAGE_CRITICAL","CRIT.BUILDING_RED_BUILD_TIME_INCREASE","CRIT.BUILDING_STRONG_CRITICAL","CRIT.BUILDING_WEAK_CRITICAL","CRIT.BUILDING_YELLOW_BUILD_TIME_INCREASE","CRIT.BULLET_HIT_CRITICAL","CRIT.BURN","CRIT.BURN_DEATH","CRIT.BURN_DEATH_OUT_OF_CONTROL","CRIT.BURN_WORLD_OBJECT","CRIT.BURN_WORLD_OBJECT_DEATH","CRIT.CAMOUFLAGE_MINE","CRIT.CHURCHILL_TANK_SHOCK_MODIFIERS","CRIT.DETONATE_BANGALORE","CRIT.DETONATE_DEMOLITION_CHARGE","CRIT.DETONATE_MINE","CRIT.EMPLACEMENT_EMPTY","CRIT.EMPLACEMENT_FLAME_CRITICAL","CRIT.EMPLACEMENT_KILL_LOADER","CRIT.EXPLOSIVE_DESTROY","CRIT.GOLIATH_DESTROY","CRIT.HEROIC_CHARGE_FATIGUE","CRIT.MAKE_CASUALTY","CRIT.SOLDIER_BLIND","CRIT.SOLDIER_EXECUTED","CRIT.SOLDIER_EXPLOSIVE_ROUND","CRIT.SOLDIER_FLAMETHROWER_EXPLODE","CRIT.SOLDIER_FORCE_RETREAT","CRIT.SOLDIER_FROZEN","CRIT.SOLDIER_KILLED","CRIT.SOLDIER_KILLED_DEATH_INTENSITY_100","CRIT.SOLDIER_KILLED_DEATH_INTENSITY_30","CRIT.SOLDIER_KILLED_DEATH_INTENSITY_60","CRIT.SOLDIER_KILLED_HMG_DEATH","CRIT.SOLDIER_PIN","CRIT.SOLDIER_SLOW","CRIT.SOLDIER_SNIPED","CRIT.SOLDIER_SNIPED_IN_HALFTRACK","CRIT.SOLDIER_SNIPED_MAKE_CASUALTY","CRIT.SOLDIER_SNIPED_STILL_ALIVE","CRIT.SOLDIER_STUN","CRIT.SOLDIER_SUPPRESS","CRIT.SQUAD_ITEM_DAMAGED","CRIT.STUNNED_CANNOT_SHOOT_10_SECONDS","CRIT.STUNNED_CANNOT_SHOOT_MOVE_10_SECONDS","CRIT.SUPPLY_DROP_BLOW_UP","CRIT.TANK_TRAP_DESTROY","CRIT.TEAM_WEAPON_DISABLING_SHOT","CRIT.VEHICLE_ABANDON","CRIT.VEHICLE_ABANDON_STURMTIGER","CRIT.VEHICLE_AEC_TEMP_ENGINE_DAMAGE","CRIT.VEHICLE_AEC_TEMP_IMMOBILITY","CRIT.VEHICLE_BLIND","CRIT.VEHICLE_CREW_DAZED_JAGDTIGER","CRIT.VEHICLE_CREW_SHOCKED","CRIT.VEHICLE_CREW_STUNNED","CRIT.VEHICLE_CREW_STUNNED_2","CRIT.VEHICLE_DAMAGE_ENGINE","CRIT.VEHICLE_DAMAGE_ENGINE_INCREMENTAL","CRIT.VEHICLE_DAMAGE_ENGINE_REAR","CRIT.VEHICLE_DAMAGE_ENGINE_REAR_RAMMING","CRIT.VEHICLE_DAMAGE_ENGINE_SNARE","CRIT.VEHICLE_DECREW","CRIT.VEHICLE_DESTROY","CRIT.VEHICLE_DESTROY_BREW_UP","CRIT.VEHICLE_DESTROY_ENGINE","CRIT.VEHICLE_DESTROY_ENGINE_REAR","CRIT.VEHICLE_DESTROY_MAINGUN","CRIT.VEHICLE_DESTROY_MAINGUN_RAMMING","CRIT.VEHICLE_DESTROY_QUAD_50","CRIT.VEHICLE_DESTROY_SEARCHLIGHT_IR_HALFTRACK","CRIT.VEHICLE_DESTROY_WEAPON_TEAM","CRIT.VEHICLE_DRIVER_INJURED","CRIT.VEHICLE_ENGINE_BURNING","CRIT.VEHICLE_EXHAUST_DAMAGED","CRIT.VEHICLE_GUNNER_INJURED","CRIT.VEHICLE_KILL_BRIT_TANK_COMMANDER","CRIT.VEHICLE_KILL_COMMANDER","CRIT.VEHICLE_KILL_DRIVER_RUSSIAN","CRIT.VEHICLE_KILL_GUNNER_RUSSIAN","CRIT.VEHICLE_KILL_RELOADER_RUSSIAN","CRIT.VEHICLE_KILL_TOP_GUNNER_HARDPOINT_1","CRIT.VEHICLE_KILL_TOP_GUNNER_HARDPOINT_2","CRIT.VEHICLE_KILL_TOP_GUNNER_HARDPOINT_4","CRIT.VEHICLE_LIGHT_DAMAGE_ENGINE","CRIT.VEHICLE_LIGHT_DAMAGE_ENGINE_REAR","CRIT.VEHICLE_LIGHT_DESTROY_ENGINE","CRIT.VEHICLE_LIGHT_DESTROY_ENGINE_REAR","CRIT.VEHICLE_LOADER_INJURED","CRIT.VEHICLE_LOSE_TREADS_OR_WHEELS","CRIT.VEHICLE_MAKE_WRECK","CRIT.VEHICLE_OPTICS_DAMAGED","CRIT.VEHICLE_OPTICS_DAMAGED_TEMP","CRIT.VEHICLE_OUT_OF_CONTROL_FAST","CRIT.VEHICLE_OUT_OF_CONTROL_SLOW","CRIT.VEHICLE_OUT_OF_FUEL_GERMAN_SP","CRIT.VEHICLE_SHELL_SHOCKED","CRIT.VEHICLE_SNIPER_SLOW","CRIT.VEHICLE_STUCK_IN_MUD","CRIT.VEHICLE_TANK_GRAB_ABANDON_SP","CRIT.VEHICLE_TEMP_IMMOBILITY","CRIT.VEHICLE_TURRET_DISABLED_TEMP","CRIT.VEHICLE_UNIVERSAL_CARRIER_FLAMETHROWER_EXPLODE","CRIT.VEHICLE_VISION","CRIT.VEHICLE_VISON_BLOCK_DAMAGED","CRIT.VEHICLE_WEAPON_DISABLED_TEMP","CRIT.WORLD_DESTROY_BARRIER","CRIT.WORLD_OBJECT_DESTROY","CRIT.WORLD_OWNED_VEHICLE_ABANDON","BridgeReplace_OnInit","SkinPreviewCapture_Init","SkinPreviewCapture_SpawnVehicles","SkinPreviewCapture_UIInit","SkinPreviewCapture_CycleAndCaptureScreenshots","SkinPreviewCapture_Begin","SkinPreviewCapture_BeginCountdown","SkinPreviewCapture_ExitCountdown","SkinPreviewCapture_Exit","SkinPreviewCapture_StartCountdown","Map_PreInit","SkinPreviewCapture_Configure","AOH_PreInit","AV_PreInit","AV_Init","AV_UpdateObjectiveTimer","AV_UIInit","AV_End","CCM_ActionSpawnUKFSpawner","CCM_ActionSpawnUKFMiscSpawner","Squad_ToClipboardData","Squad_FromClipboardData","Entity_FromClipboardData","Entity_ToDataParameters","Entity_GetHealthPointsString","Squad_ToDataParameters","Squad_GetHealthPointsString","Player_ToDataParameters","Player_GetSetting","Player_SetSetting","Player_GetSettings","LocalPlayer_GetSettings","Data_GetHealthModifiedString","Data_GetOwnerChangedString","CCM_EventCueClickManger","CCM_EventMessage","CCM_EventKickerMessage","CCM_EventKickerMessageEval","CCM_EventKickerHealthMessageEval","CCM_ErrorMessage","Item_GetEnemyPlayer","CCM_SpawnQueueTick","CCM_SpawnQueueInit","CCM_SpawnQueueAdd","CCM_DummyMessage","CCM_PlayerCommandIssued","CCM_SquadCommandIssued","CCM_EntityCommandIssued","CCM_CustomUIEvent","Variable_FromG","Ternary","CCM_PlayerCommandIssued2","CCM_ConfigInit","__subMenu_SetUpdateRate","__subMenu_SpawnUnits","__subMenu_ManipulateSquadMembers","__panel_SelectionHealth","TestFormAdd","TestFormRender","Test_SlotItemRemoveSpam","Test_SlotItemRemoveSpam_Tick","CCM_HealthMonitor_Tick","CCM_HealthMonitor_HandleHealthMessage","CCM_HealthMonitor_RegisterNewItem","CCM_HealthMonitorInit","CCM_SuppressionMonitor_Tick","CCM_SuppressionMonitor_HandleMessage","CCM_SuppressionhMonitor_RegisterNewItem","CCM_SuppressionMonitorInit","CCM_Init","CCM_UIInit","CCM_BroadcastMessageReceived","CCM_Broadcast","CCM_ShowCrosshair","CCM_HideCrosshair","CCM_DisableUI","CCM_EnableUI","CCM_KillSelection","CCM_DeleteSelection","CCM_KillSquad","CCM_DeleteSquad","CCM_KillEntity","CCM_DeleteEntity","CCM_EnableFOW","CCM_DisableFOW","CCM_EnableAI","CCM_DisableAI","CCM_SetAIDifficulty","CCM_SetSelectionHealth","CCM_AddSelectionHealthPercentage","CCM_AddSelectionHealthPoints","CCM_SetSelectionInvulnerability","CCM_SetSelectionOwner","CCM_AddResource","CCM_ResetResource","CCM_AddPopulationCap","CCM_SetInstantProductionEnabled","CCM_SetInstantConstructionEnabled","CCM_SetInstantAbilityRechargeEnabled","CCM_SpawnSquad","CCM_SpawnEntity","CCM_SpawnSlotItem","CCM_IncreaseSelectionXP","CCM_IncreaseSelectionVeterancyLevel","CCM_InstantReinforceSelection","CCM_SplitSelection","CCM_RemoveSelectionCriticals","CCM_RemoveSquadCritical","CCM_RemoveEntityCritical","CCM_ApplyCriticalToSelection","CCM_SetSquadAutoTargetting","CCM_RemoveSquadUpgrade","CCM_RemoveEntityUpgrade","CCM_RemoveSquadSlotItem","CCM_SetSelectionFacing","CCM_TeleportSelection","CCM_KillEverything","CCM_DeleteEverything","CCM_RotateEntity","CCM_SetHealthMonitorEnabled","CCM_SetSuppressionMonitorEnabled","CCM_SelectedTeamWeaponGarrisonFacePosition","CCM_CancelTeamWeaponGarrisonFacingOrder","CCM_AddSelectionSuppression","CCM_SetAllAIPlayersEnabled","CCM_ResetSelectionVeterancy","CCM_SetResourceIncomeEnabled","CCM_SetHealthMonitorUpdateRate","CCM_SetSuppressionMonitorUpdateRate","CCM_UnlockCommanderAbility","CCM_ClearCommanderAbilities","CCM_ModifySquadMovementSpeed","CCM_SetSelectionOwnerToEnemy","CCM_SquadToEntity","CCM_SetEntityAnimatorState","CCM_SetSquadAnimatorState","CCM_SetSelectionAnimatorState","CCM_SetSelectionSkinType","CCM_DropSelectionWeapons","CCM_CaptureAllTerritorySectors","CCM_NeutralizeAllTerritorySectors","CCM_SquadToSkinPreviewEntity","Enhanced_Init","Enhanced_SystemInit","Enhanced_BroadcastMessageReceived","Enhanced_UITick","Enhanced_SetButtonsEnabled","Enhanced_ResetButtonIcons","Enhanced_SetButtonsVisible","Enhanced_PreInit","Enhanced_UIInit","Dude","MyMap_OnInit","MyMap_BonusUnitKilled","prnt","toCharArray","export","include","getBlueprintIfItExists","getBlueprintName","instanceOf","parent","Loc_Create","broadcastMessage","delayedStart","Map_PlayerBonusUnitKilled","MyFunction","Map_OnInit","Gardeners_PreInit","AutoAbandonManager","AutoAbandon_Add","AutoAbandon_Remove","AutoDeleteManager","AutoDelete_Add","AutoDelete_Remove","AutoRetreatManager","AutoRetreat_Add","AutoRetreat_Remove","Parameters_ToStringData","Parameters_FromStringData","Player_FromStringData","Squad_FromStringData","Entity_FromStringData","Broadcast","Camera_MoveToCallback","Camera_MoveToCallback_Tick","CameraPosition","Class","Color","Margin","Padding","Player","Control","Button_CreateConfig","Button_GetIcon","Button","FormControl_Init","FormControl_Refresh","Form","Icon","Label","NumericUpDown_CreateIconConfig","NumericUpDownScroll_Tick","NumericUpDown_RegisterAutoScroll","NumericUpDown_UnregisterAutoScroll","NumericUpDown","MenuControl_Init","Menu_AutoRefresh","Menu_AutoCheckEnabledScan","Menu_AutoCheckCheckedScan","CloseMenus","Menu","Menu_CreateBorderImage","Panel_GetMultipartBackground","Panel","PanelColumn","PanelColumnCollection","Class_GetUniqueID","Class_CreateInstance","Construct","CompanyCommander_Create","ControlSystem_Init","Button_FromTag","ButtonCallbackHandler","Control_GetName","Control_GetX","Control_GetY","Control_GetPath","Control_GetText","Control_GetTag","BPData_GetExtensions","Loc_Get","EBP_HasExtension","EBPData_HasExtension","EBP_GetScreenName","EBP_GetIcon","EBPData_GetUIExt","EBPData_GetScreenName","EBPData_GetIcon","SBP_GetScreenName","SBP_GetIcon","SBPData_GetRaceUIExt","SBPData_GetScreenName","SBPData_GetIcon","Crit_GetScreenName","CritData_GetUIExt","CritData_GetScreenName","CritData_GetIcon","UPG_GetScreenName","UPGData_GetScreenName","SlotItem_GetScreenName","SlotItemData_GetScreenName","EGroup_ToTable","EGroup_IsAlive","EGroup_IsCapturedByTeam2","LocalImport","ImportSystem","ImportDataTables","Library_Load","Lib_EnableMessages","Lib_SetMessagesEnabled","Lib_SetupMod","Mod_GetIcon","Mod_GetAbilityBlueprint","Mod_GetSquadBlueprint","Mod_GetEntityBlueprint","Mod_GetUpgradeBlueprint","Msg_Pos","Msg_3D","Lib_GameOver","Debug_SetMessagesEnabled","TryCatch","Library_Setup","Entity_Validate","Entity_AddHealthPercentage","Entity_AddHealthPoints","Entity_GetOwnerString","Entity_GetBPName","Entity_GetCriticals","Entity_RemoveCriticals","Entity_IsTeamWeapon","Entity_IsValidSafe","Entity_GetUpgrades","Entity_Rotate","Entity_GetTypes","Entity_IsOfType2","Entity_HasUpgrades","EBP_GetTypes","EBP_IsOfType","Entity_Decrew","Entity_PrepareForScreenshot","Entity_SetSkinSeason","Entity_GetBlueprintName","Entity_HasModifierExt","Percentage_Normalize","Round","scientific","Player_GetIndex","Player_SetResourcesEnabled","Player_ResetResources","Player_GetAllSquads","Player_DestroyAllSquads","Player_GetDisplayRaceName","Player_GetDisplayRaceNameLong","Player_GetNameWithFaction","AIDifficulty_Tostring","Player_IsAI","Player_ForEachSquad","Pos_NormalizeHeight","Rule_AddIntervalAndRun","Rule_AddIfNotExists","Rule_AddIntervalIfNotExists","Rule_ChangeIntervalIfExists","Rule_AddDelayed","Rule_AddDelayedIfNotExists","Modify_SetSquadtAutoTargetting","Modify_SetEntityAutoTargetting","Modify_SetEntityAutoTargettingAllHardpoints","Modify_SetSquadAutoTargettingAllHardpoints","Modify_SetSGroupAutoTargettingAllHardpoints","Modify_SetEGroupAutoTargettingAllHardpoints","Modify_SquadTypeEnableCapturing","Selection_UnselectAll","Selection_IsOneEntity","Selection_IsOneSquad","Selection_IsOneSquadOrOneEntity","Selection_IsOneOrMoreSquads","Selection_IsOneOrMoreEntities","Selection_IsSquadsOrEntities","Selection_IsSquadOrEntity","Selection_GetSquad","Selection_GetEntity","Selection_GetSquads","Selection_GetEntities","Misc_SomethingIsSelected","Selection_ForEachSquad","Selection_ForEachEntity","Selection_IsNotInvulnerable","Selection_IsNotNeutralSquadOrEntity","Selection_CountInvulnerables","Selection_IsInvulnerable","SelectionMonitor_Init","SelectionMonitor","SelectionMonitor_GetID","SelectionMonitor_AddSquad","SelectionMonitor_AddEntity","SelectionMonitor_RemoveSquad","SelectionMonitor_RemoveEntity","SelectionMonitor_RemoveItemListener","SelectionMonitor_RemoveSquadLister","SelectionSystem_RemoveEntityLister","SGroup_ToTable","SGroup_SetPosition","SGroup_CountEntities","SGroup_IsAlive2","Squad_CountSpawned","Squad_IsPlane","Squad_SetSelectable","Squad_GetLastAttackerSquad","Squad_IsSelected","Squad_AddHealthPercentage","Squad_AddHealthPoints","Squad_Abandon","Squad_GetOwnerString","Squad_GetBPName","Squad_GetCriticals","Squad_RemoveCriticals","Squad_IsValidSafe","Squad_SetAutoTargetting","__RegisterSquadAutoTargettingModifier","__UnRegisterSquadAutoTargettingModifier","__RegisterEntityAutoTargettingModifier","__UnRegisterEntityAutoTargettingModifier","Squad_SetAllAutoTargetting","Squad_GetAutoTargetting","Squad_GetAllAutoTargetting","Squad_GetUpgrades","Squad_HasUpgrades","Squad_RemoveSlotItem","Squad_RemoveUpgradeFully","Squad_ForEachHeldSquad","Squad_DestroyHeldSquads","Squad_KillHeldSquads","Squad_ModifyVehicleSpeed","Squad_ModifyVehicleRotationSpeed","Squad_ModifyTurretHorizontalSpeed","Squad_ModifyMovementSpeed","Squad_GetTypes","Squad_IsOfType","SBP_GetTypes","SBP_IsOfType","Squad_GetEntityTable","Squad_ToEntities","Squad_ToEntity","Squad_AddMainGunHorizontalRotation","Squad_SetMainGunHorizontalRotation","Squad_Decrew","Squad_SetSkinSeason","Squad_RemoveSlotItems","Squad_GetEntityStateString","Squad_RemoveUpgradeIfPresent","Squad_RemoveUpgradesIfPResent","String_Match","String_Replace","String_AddGenetive","String_Split","Number_TrailingZeroes","Outpost","Outpost_Init","OutpostManager_Register","OutpostManager_Tick","OutpostPatrol","OutpostCaptureTrigger","OutpostPatrolAlarmedSquads_Register","OutpostPatrolAlarmedSquads_Tick","OutpostPatrolManager_Register","OutpostPatrolManager_Tick","OutpostRadioPost","OutpostRadioPostManager_Register","OutpostRadioPostManager_Tick","Table_AddTable","Table_GetSmallest","Table_GetLargest","Table_RemoveValue","Table_IsEmpty","Table_GetRandomBlueprint","Table_Remove","Table_ToIndexableList","Table_Count","Table_Compare","RangeTable_GetRandomValue","OutpostReinforcementsManager_Register","OutpostReinforcementsManager_Tick","Team_GetRandomPlayer","Team_GetRandomPlayers","Time_TicksToSeconds","Time_SecondsToTicks","Time_MinutesToTicks","UI_EnableSelectionVisuals","UI_EnableSquadSelectionVisuals","SelectionVisual_Tick","SelectionVisual_RegisterEntity","UI_ScalePoint","Util_DelaySeconds","Util_DelayMinutes","Util_DelayRandom","Util_DelayRandomSeconds","Util_GetRandomPosExtended","Util_GetRandomHeadingPos","UIFrame_Destroy","Misc_Tester","Util_DistanceFromLine","Util_DistancePointToTeamShortest","HintPoints_Remove","MapIcon_CreateAndFacePosition","toboolean","ResourceType_ToString","ResourceType_FromString","ResourceType_ToDisplayString","Selection_GetPlayer","Objective_UpdateTitle","Objective_StartLocally","Util_CallFunctionsWithParameters","World_ForEachEntity","World_DivideTerritoryBetweenTeams","World_GetWidthRange","World_GetLengthRange","World_RegisterPlayers","World_GetEverythingNearPoint","World_GetAll","World_GetAllSquads","World_ForEeachSquad","World_OneOrMoreAIPlayerIsEnabled","World_OneOrMoreAIPlayerIsDisabled","World_CleanUpTheDeadAll","World_GetAllTerritoryPointEntities","Villagers_PreInit","WarDrive_Init","_getPlayerMineEBP","_spawnMines","WarDrive_GetPlayerReconAbility","WarDrive_ReconSweepBetweenTeams","WarDrive_GetNextEffectDelay","WarDrive_PickRandomEffect","WarDrive_SplitTimeUnits","WarDrive_FormatTime","WarDrive_Monitor","Team_HasTerritoryPoint","WarDrive_EntityKilled","WarDrive_SquadKilled","TestEffect","WarDrive_EnableEffect","WarDrive_RemoveModifiers","WarDrive_GetIcon","WarDrive_RegisterModifier","Modify_SquadBuildTime","Modify_SquadReinforceTime","Modify_EntityCaptureTime","WarDrive_ObjectiveInit","WarDrive_ObjectiveAfterInt","WarDrive_Pager","WarDrive_AbilityExecuted","WarDrive_GetAbilityBlueprint","WarDrive_PreInit","CCM_AddInfiniteResourcesPopcap","CCM_ActionKillSelection","CCM_ActionDeleteSelection","CCM_ActionTeleportSelection","CCM_ActionIncreaseSelectionVeterancy","CCM_ActionIncreaseSelectionHealth","CCM_ActionDecreaseSelectionHealth","CCM_ModifySelectionHealth","CCM_ActionAbandonSelected","CCM_ActionRemoveCriticals","CCM_ActionDropSlotItems","CCM_ActionInstantReinforce","CCM_ActionAddPreciseManpower","CCM_ActionAddPreciseFuel","CCM_ActionAddPreciseMunition","_CCM_SpawnSpawnerSquad","CCM_ActionSpawnSovietSpawner","CCM_ActionSpawnAEFSpawner","CCM_ActionSpawnGermanSpawner","CCM_ActionSpawnWestGermanSpawner","CCM_ActionAddFullHealth","CCM_ActionKillOneEntity","CCM_ActionDeleteOneEntity","CCM_ActionSpawnGermanMiscSpawner","CCM_ActionSpawnSovietMiscSpawner","CCM_ActionSpawnWestGermanMiscSpawner","CCM_ActionSpawnAEFMiscSpawner","CCM_DataInit","CCM_CopySelection","CCM_PasteSelection","Clipboard_Clear","CCM_PreInit","CCM_SystemInit","CCM_PlayerResetAbilities","Player_GetSettingsKey","CCM_PlayerAbilityCompleteListener","CCM_PlayerAbilityListener","CCM_RegisterPlayerAction","Squad_GetSpawnerRaceIndex","Squad_GetSpawnerTable","Squad_GetSpawnAbilityPrefix","CCM_SquadAbilityListener","CCM_CountSpawnTableItems","_CCM_InitSpawnerSquad","CCM_AutoHideAbilities","Entity_CreateAndSpawnTowardTeamWeapon","Entity_GetUpgradeTable","Entity_ApplyCriticalHit","Entity_GetText","Entity_Abandon","EntityBP_IsBuilding","Util_Destroy","Util_GetBPName","Misc_CheckForParentSquad","Squad_GetTableKey","Entity_GetTableKey","Util_GetTablekey","Util_SetInvulnerable","Player_GetIDSafe","Table_ForEach","CCM_Msg","CCM_ClearMSG","CCM_GetAbilityBleprint","CCM_GetSquadBlueprint","CCM_GetEntityBlueprint","CCM_GetUpgradeBlueprint","CCM_GetIcon","CCM_EventCue","Misc_AddSpawnedItemToSystem","Util_AddHealth","Misc_DoPercentageSum","Util_SetPosition","Util_GetGameID","Util_DecodeGameID","Misc_SpawnSlotItemOnGround","Squad_ModifySpeed","Squad_GetHealthTable","Squad_ApplyHealthTable","Squad_GetPlayerOwnerSafe","Squad_GetHeadingTable","Squad_ApplyHeadingTable","Squad_GetUpgradesTable","Squad_GetText","Squad_GetCriticalsTable","Squad_ApplyCriticalHitTable","Squad_ModifyDamage","Squad_DropSlotItems","Squad_RemoveUpgrades","Squad_RemoveCritical","Squad_HasCritical","Squad_GetEntityPositionList","Squad_ApplyEntityPositionList","Squad_SetHealthPercentage","Squad_IsVehicle","CCM_ToggleInstantProduction","CCM_ToggleFOW","CCM_ToggleGlobalAI","CCM_ToggleSelectionInvulnerability","CCM_ToggleSelectionOwner","CCM_ToggleDisableWeapons","CCM_ToggleEngineOrPostureState","CCM_ToggleHealthMonitor","CCM_GetSquadKey","CCM_GetEntityKey","CCM_HealthMonitor","_CCM_HealthMonitor_HandleHealth","_CCM_HealthMonitor_KickerMessage","CTF_PreInit","CTFSystem_Init","CTF_GetRandomFlagSpawnPosition","CTF_FreeFlagSpawnPosition","CTFSystem_InitDelayed","CTF_StartCore","CTF_GetWinScoreLimit","CTF_FlagScoreMonitor","CTF_BlinkFlagCarriers","CTF_FlagRespawnMonitor","CTF_FlagStateMonitor","CTF_EnableResources","CTF_UpdateObjectiveUI","CTF_FixFlagColor","CTF_FixFlagColorDelayed","CTF_StopAlarm","CTF_FlagCarrierAbilityExecuted","CTF_DropFlagRequestManager","CTF_PreventFlagCarrierReCrewAndVehicleGarrisoning","CTF_FlagCaptured","CTF_FlagDropped","CTF_FlagScored","CTF_GetOpposingTeam","CTF_TeamFlagScore","CTF_ObjctiveInit","CTF_AddObjectiveUI","Entity_IsReCrewable","Squad_AddFlagCarrierEffects","Squad_RemoveFlagCarrierEffects","Squad_SetCaptureEnabled","Squad_EnableFlagCarrierUI","Squad_EnableCantHoldUI","Squad_ModifyInfantrySpeed","Squad_MonitorDeath","Squad_DisableFlagCarrierUI","isset","Squad_FlagCarrierDeath","Squad_DropFlag","Squad_CarriesFlag","Squad_IsRegisteredFlagCarrier","Squad_RegisterFlagCarrier","Squad_UnRegisterFlagCarrier","Squad_GetSlotItemTable","Player_EnableMoveFlagHereUI","Player_UnlockRetreat","Player_IsHoldingAnyFlags","SGroup_IsCarryingFlag","UI_LocalKickerMessage","UI_GlobalKickerMessage","CTF_Msg","ClearCTF_Msg","Listener","Table_Shuffle","Ability_GetUniqueKey","Player_AddPopulation","Player_ExecuteLocally","Player_GetEnemyPlayer","Player_SetResourceIncomeNumber","Team_GetFirstPlayer","Team_GetEntitiesNearPoint","Team_GetSquadsNearPoint","Team_GetPlayerCount","Team_ExecuteLocally","SGroup_CreateTemp","EGroup_CreateTemp","EGroup_GetClosest","EGroup_AddGroup","EGroup_FilterByUnitType","Entity_GetGarrisonedSquads","Entity_AutoAlign","Entity_CreateAndSpawnToward","Entity_CreateAndSpawnTowardDelayed","Entity_CreateAndSpawnTowardDelayedRandom","Entity_GetName","Entity_GetTempEGroup","Entity_GetOwnerSafe","Entity_Replace","Entity_IsSelected","Entity_HasProductionQueueItem","Entity_IsValidEntity","Squad_GetUniqueKey","Squad_GetName","Squad_IsIdle","Squad_IsConcstructing","Squad_IsHeadingToPosition","Squad_ForEachEntity","UI_FlashSquad","Util_Repeat","Util_Delay","Util_IsPositionInPolygon","Util_GetDirectionalOffset","Util_GetDirectionalOffsetPosition","Util_GetRandomPos","Util_GetAngleTowardsPos","Util_CopyPosition","Util_CreateUIFrame","Util_Tester","Misc_UnSelectAll","Util_DefaultValue","World_ForEachEntitiesByBlueprint","World_GetEntitiesOfType","Pos_AddHeight","Pos_GetString","dr_text3dpos","Heading_Rotate","Squad_InfraRedReveal","WinCondition_PreInit","WinCondition_MonitorVictoryPoints","Team_GetTitle","Team_GetOpposingTeam","OKWNoCache_PreInit","PK_SystemInit","PK_ScanPlayers","PK_PlayerAbilityListener","Player_RemoveTankDispatchAbilities","Squad_GetTempSGroup","Player_GetMapEntryPositionClosest","Util_SortPositionsByClosestImproved","Pos_GetXYZString","PK_Msg","PK_ClearMSG","PK_GetAbilityBleprint","PK_GetUpgradeBleprint","PK_GetSquadBleprint","PK_EventCue","Player_GetRaceIndex","PK_PreInit","RotateThings","TC_PreInit","TC_Init","TC_TogglePlayerCategory","System_PlayerAbilityComplete","System_PlayerAbilityExecuted","TC_UpdatePlayerCircle","TC_UpdatePlayerArrow","TC_GeneralManager","TC_GetAbilityBlueprint","TC_GetMineIcon","TC_GetMineIconScale","TC_MineIsAllowedToMark","TC_MineIsPartOfSMineField","TC_GetMineMarkerColor","TC_GetIcon","TC_MineMarkerManager","TC_BlibMinePlanted","System_EntityConstructionCompleted","System_EntityKilled","Player_IsLocalPlayer","Player_GetUniqueKey","Player_GetName","Players_ForEach","Players_ForEachInTeam","Entity_GetPlayerOwnerSafe","Entity_GetUniqueKey","Entity_CheckForParentSquad","EntityList_ContainsValidEntities","EGroup_GetEntityIds","Util_GlobalMessage","Util_CreateLocString","Util_GetBlueprint","Util_GetUnitOwner","Game_GetLocalPlayerID","World_OwnsUnit","World_GetEntitiesByBlueprint","World_ForEachEntities","Msg","TC_DataInit_Ebps","TC_DataInit","WinCondition_GameOver","WinCondition_Check","WinCondition_Init","$","AAGUID","ANGLE_instanced_arrays","AbstractWorker","AbstractWorkerEventMap","Account","ActiveXObject","AesCbcParams","AesCfbParams","AesCmacParams","AesCtrParams","AesDerivedKeyParams","AesGcmParams","AesKeyAlgorithm","AesKeyGenParams","Algorithm","AlgorithmIdentifier","AnalyserNode","AnimationEvent","AnimationEventInit","ApplicationCache","ApplicationCacheEventMap","Array","ArrayBuffer","ArrayBufferConstructor","ArrayBufferView","ArrayConstructor","ArrayLike","AssertionOptions","AssignedNodesOptions","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioBufferSourceNodeEventMap","AudioContext","AudioContextBase","AudioContextEventMap","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioProcessingEvent","AudioTrack","AudioTrackList","AudioTrackListEventMap","BarProp","BaseJQueryEventObject","BeforeUnloadEvent","BiquadFilterNode","Blob","BlobPropertyBag","Body","BodyInit","Boolean","BooleanConstructor","Buffer","BufferEncoding","BufferSource","ByteString","CDATASection","CSS","CSSConditionRule","CSSFontFaceRule","CSSGroupingRule","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSMediaRule","CSSNamespaceRule","CSSPageRule","CSSRule","CSSRuleList","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSSupportsRule","Cache","CacheQueryOptions","CacheStorage","Canvas2DContextAttributes","CanvasGradient","CanvasPathMethods","CanvasPattern","CanvasRenderingContext2D","ChannelMergerNode","ChannelSplitterNode","CharacterData","ChildNode","ClassDecorator","ClientData","ClientRect","ClientRectList","ClipboardEvent","ClipboardEventInit","CloseEvent","CloseEventInit","Comment","CompositionEvent","CompositionEventInit","ConcatParams","ConfirmSiteSpecificExceptionsInformation","Console","ConstrainBoolean","ConstrainBooleanParameters","ConstrainDOMString","ConstrainDOMStringParameters","ConstrainDouble","ConstrainDoubleRange","ConstrainLong","ConstrainLongRange","ConstrainVideoFacingModeParameters","ConvolverNode","Coordinates","Crypto","CryptoKey","CryptoKeyPair","CryptoOperationData","CustomElementRegistry","CustomEvent","CustomEventInit","DOMError","DOMException","DOMImplementation","DOML2DeprecatedColorProperty","DOML2DeprecatedSizeProperty","DOMParser","DOMRectInit","DOMSettableTokenList","DOMStringList","DOMStringMap","DOMTokenList","DataCue","DataTransfer","DataTransferItem","DataTransferItemList","DataView","DataViewConstructor","Date","DateConstructor","DecodeErrorCallback","DecodeSuccessCallback","DeferredPermissionRequest","DelayNode","DeviceAcceleration","DeviceAccelerationDict","DeviceLightEvent","DeviceLightEventInit","DeviceMotionEvent","DeviceMotionEventInit","DeviceOrientationEvent","DeviceOrientationEventInit","DeviceRotationRate","DeviceRotationRateDict","DhImportKeyParams","DhKeyAlgorithm","DhKeyDeriveParams","DhKeyGenParams","Document","DocumentEvent","DocumentEventMap","DocumentFragment","DocumentOrShadowRoot","DocumentType","DoubleRange","DragEvent","DynamicsCompressorNode","EXT_frag_depth","EXT_texture_filter_anisotropic","EcKeyAlgorithm","EcKeyGenParams","EcKeyImportParams","EcdhKeyDeriveParams","EcdsaParams","Element","ElementDefinitionOptions","ElementEventMap","ElementListTagNameMap","ElementTagNameMap","ElementTraversal","Enumerator","EnumeratorConstructor","ErrnoException","Error","ErrorConstructor","ErrorEvent","ErrorEventHandler","ErrorEventInit","EvalError","EvalErrorConstructor","Event","EventEmitter","EventInit","EventListener","EventListenerObject","EventListenerOrEventListenerObject","EventModifierInit","EventTarget","ExceptionInformation","ExtensionScriptApis","External","FFF","FGHJK","File","FileList","FilePropertyBag","FileReader","Float32Array","Float32ArrayConstructor","Float64Array","Float64ArrayConstructor","FocusEvent","FocusEventInit","FocusNavigationEvent","FocusNavigationEventInit","FocusNavigationOrigin","Foo","Foos","ForEachCallback","FormData","FrameRequestCallback","Function","FunctionConstructor","FunctionStringCallback","GLbitfield","GLboolean","GLbyte","GLclampf","GLenum","GLfloat","GLint","GLintptr","GLshort","GLsizei","GLsizeiptr","GLubyte","GLuint","GLushort","GainNode","Gamepad","GamepadButton","GamepadEvent","GamepadEventInit","GeneratorFunction","GeneratorFunctionConstructor","Geolocation","GetNotificationOptions","GetSVGDocument","GlobalEventHandlers","GlobalEventHandlersEventMap","GlobalFetch","HTMLAllCollection","HTMLAnchorElement","HTMLAppletElement","HTMLAreaElement","HTMLAreasCollection","HTMLAudioElement","HTMLBRElement","HTMLBaseElement","HTMLBaseFontElement","HTMLBodyElement","HTMLBodyElementEventMap","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLCollectionBase","HTMLCollectionOf","HTMLDListElement","HTMLDataElement","HTMLDataListElement","HTMLDirectoryElement","HTMLDivElement","HTMLDocument","HTMLElement","HTMLElementEventMap","HTMLElementTagNameMap","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameElementEventMap","HTMLFrameSetElement","HTMLFrameSetElementEventMap","HTMLHRElement","HTMLHeadElement","HTMLHeadingElement","HTMLHtmlElement","HTMLIFrameElement","HTMLIFrameElementEventMap","HTMLImageElement","HTMLInputElement","HTMLLIElement","HTMLLabelElement","HTMLLegendElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMarqueeElementEventMap","HTMLMediaElement","HTMLMediaElementEventMap","HTMLMenuElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLOListElement","HTMLObjectElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLSlotElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableAlignment","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableDataCellElement","HTMLTableElement","HTMLTableHeaderCellElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","HTMLVideoElementEventMap","HashChangeEvent","HashChangeEventInit","Headers","HeadersInit","History","HkdfCtrParams","HmacImportParams","HmacKeyAlgorithm","HmacKeyGenParams","I","IArguments","IDBArrayKey","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBDatabaseEventMap","IDBEnvironment","IDBFactory","IDBIndex","IDBIndexParameters","IDBKeyPath","IDBKeyRange","IDBObjectStore","IDBObjectStoreParameters","IDBOpenDBRequest","IDBOpenDBRequestEventMap","IDBRequest","IDBRequestEventMap","IDBTransaction","IDBTransactionEventMap","IDBValidKey","IDBVersionChangeEvent","IFoos","IIRFilterNode","ITextWriter","Image","ImageData","Infinity","Int16Array","Int16ArrayConstructor","Int32Array","Int32ArrayConstructor","Int8Array","Int8ArrayConstructor","IntersectionObserver","IntersectionObserverCallback","IntersectionObserverEntry","IntersectionObserverEntryInit","IntersectionObserverInit","Intl","Iterable","IterableIterator","Iterator","IteratorResult","JQuery","JQueryAjaxSettings","JQueryAnimationOptions","JQueryCallback","JQueryCoordinates","JQueryDeferred","JQueryEventConstructor","JQueryEventObject","JQueryGenericPromise","JQueryInputEventObject","JQueryKeyEventObject","JQueryMouseEventObject","JQueryParam","JQueryPromise","JQueryPromiseCallback","JQueryPromiseOperator","JQuerySerializeArrayElement","JQueryStatic","JQuerySupport","JQueryXHR","JSON","JSX","JsonWebKey","KeyAlgorithm","KeyFormat","KeyType","KeyUsage","KeyboardEvent","KeyboardEventInit","LinkStyle","ListeningStateChangedEvent","Location","LongRange","LongRunningScriptDetectedEvent","MSAccountInfo","MSApp","MSAppAsyncOperation","MSAppAsyncOperationEventMap","MSAssertion","MSAudioLocalClientEvent","MSAudioRecvPayload","MSAudioRecvSignal","MSAudioSendPayload","MSAudioSendSignal","MSBaseReader","MSBaseReaderEventMap","MSBlobBuilder","MSConnectivity","MSCredentialFilter","MSCredentialParameters","MSCredentialSpec","MSCredentials","MSDelay","MSDescription","MSExecAtPriorityFunctionCallback","MSFIDOCredentialAssertion","MSFIDOCredentialParameters","MSFIDOSignature","MSFIDOSignatureAssertion","MSFileSaver","MSGesture","MSGestureEvent","MSGraphicsTrust","MSHTMLWebViewElement","MSIPAddressInfo","MSIceWarningFlags","MSInboundPayload","MSInputMethodContext","MSInputMethodContextEventMap","MSJitter","MSLaunchUriCallback","MSLocalClientEvent","MSLocalClientEventBase","MSManipulationEvent","MSMediaKeyError","MSMediaKeyMessageEvent","MSMediaKeyNeededEvent","MSMediaKeySession","MSMediaKeys","MSNavigatorDoNotTrack","MSNetwork","MSNetworkConnectivityInfo","MSNetworkInterfaceType","MSOutboundNetwork","MSOutboundPayload","MSPacketLoss","MSPayloadBase","MSPointerEvent","MSPortRange","MSRangeCollection","MSRelayAddress","MSSignatureParameters","MSSiteModeEvent","MSStream","MSStreamReader","MSTransportDiagnosticsStats","MSUnsafeFunctionCallback","MSUtilization","MSVideoPayload","MSVideoRecvPayload","MSVideoResolutionDistribution","MSVideoSendPayload","MSWebViewAsyncOperation","MSWebViewAsyncOperationEventMap","MSWebViewSettings","Map","MapConstructor","Math","MediaDeviceInfo","MediaDevices","MediaDevicesEventMap","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaEncryptedEventInit","MediaError","MediaKeyMessageEvent","MediaKeyMessageEventInit","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaKeySystemConfiguration","MediaKeySystemMediaCapability","MediaKeys","MediaList","MediaQueryList","MediaQueryListListener","MediaSource","MediaStream","MediaStreamAudioSourceNode","MediaStreamConstraints","MediaStreamError","MediaStreamErrorEvent","MediaStreamErrorEventInit","MediaStreamEvent","MediaStreamEventInit","MediaStreamEventMap","MediaStreamTrack","MediaStreamTrackEvent","MediaStreamTrackEventInit","MediaStreamTrackEventMap","MediaTrackCapabilities","MediaTrackConstraintSet","MediaTrackConstraints","MediaTrackSettings","MediaTrackSupportedConstraints","MessageChannel","MessageEvent","MessageEventInit","MessagePort","MessagePortEventMap","MethodDecorator","MimeType","MimeTypeArray","Model123","Model456","MouseEvent","MouseEventInit","MouseWheelEvent","MsZoomToOptions","MutationCallback","MutationEvent","MutationObserver","MutationObserverInit","MutationRecord","NaN","NamedNodeMap","NavigationCompletedEvent","NavigationEvent","NavigationEventWithReferrer","Navigator","NavigatorBeacon","NavigatorConcurrentHardware","NavigatorContentUtils","NavigatorGeolocation","NavigatorID","NavigatorOnLine","NavigatorStorageUtils","NavigatorUserMedia","NavigatorUserMediaErrorCallback","NavigatorUserMediaSuccessCallback","Node","NodeBuffer","NodeFilter","NodeIterator","NodeJS","NodeList","NodeListOf","NodeModule","NodeProcess","NodeRequire","NodeRequireFunction","NodeSelector","Notification","NotificationEventMap","NotificationOptions","NotificationPermissionCallback","Number","NumberConstructor","OES_element_index_uint","OES_standard_derivatives","OES_texture_float","OES_texture_float_linear","OES_texture_half_float","OES_texture_half_float_linear","Object","ObjectConstructor","ObjectURLOptions","OfflineAudioCompletionEvent","OfflineAudioContext","OfflineAudioContextEventMap","Option","OscillatorNode","OscillatorNodeEventMap","OverflowEvent","PageTransitionEvent","PannerNode","ParameterDecorator","ParentNode","Partial","Path2D","PaymentAddress","PaymentCurrencyAmount","PaymentDetails","PaymentDetailsModifier","PaymentItem","PaymentMethodData","PaymentOptions","PaymentRequest","PaymentRequestEventMap","PaymentRequestUpdateEvent","PaymentRequestUpdateEventInit","PaymentResponse","PaymentShippingOption","Pbkdf2Params","PerfWidgetExternal","Performance","PerformanceEntry","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceNavigationTiming","PerformanceResourceTiming","PerformanceTiming","PeriodicWave","PeriodicWaveConstraints","PermissionRequest","PermissionRequestedEvent","Pick","Plugin","PluginArray","PointerEvent","PointerEventInit","PopStateEvent","PopStateEventInit","Position","PositionCallback","PositionError","PositionErrorCallback","PositionOptions","ProcessingInstruction","ProgressEvent","ProgressEventInit","Promise","PromiseConstructor","PromiseConstructorLike","PromiseLike","PromiseRejectionEvent","PromiseRejectionEventInit","PropertyDecorator","PropertyDescriptor","PropertyDescriptorMap","PropertyKey","Proxy","ProxyConstructor","ProxyHandler","PushManager","PushSubscription","PushSubscriptionOptions","PushSubscriptionOptionsInit","RTCConfiguration","RTCDTMFToneChangeEvent","RTCDTMFToneChangeEventInit","RTCDtlsFingerprint","RTCDtlsParameters","RTCDtlsTransport","RTCDtlsTransportEventMap","RTCDtlsTransportStateChangedEvent","RTCDtmfSender","RTCDtmfSenderEventMap","RTCIceCandidate","RTCIceCandidateAttributes","RTCIceCandidateComplete","RTCIceCandidateDictionary","RTCIceCandidateInit","RTCIceCandidatePair","RTCIceCandidatePairChangedEvent","RTCIceCandidatePairStats","RTCIceGatherCandidate","RTCIceGatherOptions","RTCIceGatherer","RTCIceGathererEvent","RTCIceGathererEventMap","RTCIceParameters","RTCIceServer","RTCIceTransport","RTCIceTransportEventMap","RTCIceTransportStateChangedEvent","RTCInboundRTPStreamStats","RTCMediaStreamTrackStats","RTCOfferOptions","RTCOutboundRTPStreamStats","RTCPeerConnection","RTCPeerConnectionErrorCallback","RTCPeerConnectionEventMap","RTCPeerConnectionIceEvent","RTCPeerConnectionIceEventInit","RTCRTPStreamStats","RTCRtcpFeedback","RTCRtcpParameters","RTCRtpCapabilities","RTCRtpCodecCapability","RTCRtpCodecParameters","RTCRtpContributingSource","RTCRtpEncodingParameters","RTCRtpFecParameters","RTCRtpHeaderExtension","RTCRtpHeaderExtensionParameters","RTCRtpParameters","RTCRtpReceiver","RTCRtpReceiverEventMap","RTCRtpRtxParameters","RTCRtpSender","RTCRtpSenderEventMap","RTCRtpUnhandled","RTCSessionDescription","RTCSessionDescriptionCallback","RTCSessionDescriptionInit","RTCSrtpKeyParam","RTCSrtpSdesParameters","RTCSrtpSdesTransport","RTCSrtpSdesTransportEventMap","RTCSsrcConflictEvent","RTCSsrcRange","RTCStats","RTCStatsCallback","RTCStatsProvider","RTCStatsReport","RTCTransport","RTCTransportStats","RandomSource","Range","RangeError","RangeErrorConstructor","React","ReadableStream","ReadableStreamReader","Readonly","ReadonlyArray","ReadonlyMap","ReadonlySet","Record","ReferenceError","ReferenceErrorConstructor","Reflect","RegExp","RegExpConstructor","RegExpExecArray","RegExpMatchArray","RegistrationOptions","Request","RequestInfo","RequestInit","Response","ResponseInit","RsaHashedImportParams","RsaHashedKeyAlgorithm","RsaHashedKeyGenParams","RsaKeyAlgorithm","RsaKeyGenParams","RsaOaepParams","RsaOtherPrimesInfo","RsaPssParams","SVGAElement","SVGAngle","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPoints","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGCircleElement","SVGClipPathElement","SVGComponentTransferFunctionElement","SVGDefsElement","SVGDescElement","SVGElement","SVGElementEventMap","SVGElementInstance","SVGElementInstanceList","SVGEllipseElement","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGFilterPrimitiveStandardAttributes","SVGFitToViewBox","SVGForeignObjectElement","SVGGElement","SVGGradientElement","SVGGraphicsElement","SVGImageElement","SVGLength","SVGLengthList","SVGLineElement","SVGLinearGradientElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGNumber","SVGNumberList","SVGPathElement","SVGPathSeg","SVGPathSegArcAbs","SVGPathSegArcRel","SVGPathSegClosePath","SVGPathSegCurvetoCubicAbs","SVGPathSegCurvetoCubicRel","SVGPathSegCurvetoCubicSmoothAbs","SVGPathSegCurvetoCubicSmoothRel","SVGPathSegCurvetoQuadraticAbs","SVGPathSegCurvetoQuadraticRel","SVGPathSegCurvetoQuadraticSmoothAbs","SVGPathSegCurvetoQuadraticSmoothRel","SVGPathSegLinetoAbs","SVGPathSegLinetoHorizontalAbs","SVGPathSegLinetoHorizontalRel","SVGPathSegLinetoRel","SVGPathSegLinetoVerticalAbs","SVGPathSegLinetoVerticalRel","SVGPathSegList","SVGPathSegMovetoAbs","SVGPathSegMovetoRel","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGSVGElement","SVGSVGElementEventMap","SVGScriptElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSwitchElement","SVGSymbolElement","SVGTSpanElement","SVGTests","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGURIReference","SVGUnitTypes","SVGUseElement","SVGViewElement","SVGZoomAndPan","SVGZoomEvent","ScopedCredential","ScopedCredentialDescriptor","ScopedCredentialInfo","ScopedCredentialOptions","ScopedCredentialParameters","Screen","ScreenEventMap","ScriptNotifyEvent","ScriptProcessorNode","ScriptProcessorNodeEventMap","ScrollBehavior","ScrollIntoViewOptions","ScrollLogicalPosition","ScrollOptions","ScrollRestoration","ScrollToOptions","Selection","ServiceWorker","ServiceWorkerContainer","ServiceWorkerContainerEventMap","ServiceWorkerEventMap","ServiceWorkerMessageEvent","ServiceWorkerMessageEventInit","ServiceWorkerRegistration","ServiceWorkerRegistrationEventMap","Set","SetConstructor","ShadowRoot","ShadowRootInit","SlowBuffer","SourceBuffer","SourceBufferList","SpeechSynthesis","SpeechSynthesisEvent","SpeechSynthesisEventInit","SpeechSynthesisEventMap","SpeechSynthesisUtterance","SpeechSynthesisUtteranceEventMap","SpeechSynthesisVoice","StereoPannerNode","Storage","StorageEvent","StorageEventInit","StoreExceptionsInformation","StoreSiteSpecificExceptionsInformation","String","StringConstructor","StyleMedia","StyleSheet","StyleSheetList","StyleSheetPageList","SubtleCrypto","Symbol","SymbolConstructor","SyncManager","SyntaxError","SyntaxErrorConstructor","TemplateStringsArray","Text","TextEvent","TextMetrics","TextStreamBase","TextStreamReader","TextStreamWriter","TextTrack","TextTrackCue","TextTrackCueEventMap","TextTrackCueList","TextTrackEventMap","TextTrackList","TextTrackListEventMap","Thenable","TimeRanges","Touch","TouchEvent","TouchList","TrackEvent","TrackEventInit","TransitionEvent","TransitionEventInit","TreeWalker","TypeError","TypeErrorConstructor","TypedPropertyDescriptor","UIEvent","UIEventInit","URIError","URIErrorConstructor","URL","URLSearchParams","USVString","Uint16Array","Uint16ArrayConstructor","Uint32Array","Uint32ArrayConstructor","Uint8Array","Uint8ArrayConstructor","Uint8ClampedArray","Uint8ClampedArrayConstructor","UnviewableContentIdentifiedEvent","VBArray","VBArrayConstructor","ValidityState","VarDate","VideoPlaybackQuality","VideoTrack","VideoTrackList","VideoTrackListEventMap","VoidFunction","WEBGL_compressed_texture_s3tc","WEBGL_debug_renderer_info","WEBGL_depth_texture","WScript","WaveShaperNode","WeakMap","WeakMapConstructor","WeakSet","WeakSetConstructor","WebAuthentication","WebAuthnAssertion","WebAuthnExtensions","WebGLActiveInfo","WebGLBuffer","WebGLContextAttributes","WebGLContextEvent","WebGLContextEventInit","WebGLFramebuffer","WebGLObject","WebGLProgram","WebGLRenderbuffer","WebGLRenderingContext","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebKitCSSMatrix","WebKitDirectoryEntry","WebKitDirectoryReader","WebKitEntriesCallback","WebKitEntry","WebKitErrorCallback","WebKitFileCallback","WebKitFileEntry","WebKitFileSystem","WebKitPoint","WebSocket","WebSocketEventMap","WheelEvent","WheelEventInit","Window","WindowBase64","WindowConsole","WindowEventMap","WindowLocalStorage","WindowSessionStorage","WindowTimers","WindowTimersExtension","Worker","WorkerEventMap","WritableStream","XMLDocument","XMLHttpRequest","XMLHttpRequestEventMap","XMLHttpRequestEventTarget","XMLHttpRequestEventTargetEventMap","XMLHttpRequestUpload","XMLSerializer","XPathEvaluator","XPathExpression","XPathNSResolver","XPathResult","XSLTProcessor","_","__dirname","__filename","a","abstract","addEventListener","alert","any","applicationCache","as","async","atob","await","b","blur","boolean","break","btoa","caches","cancelAnimationFrame","captureEvents","case","catch","class","clearImmediate","clearInterval","clearTimeout","clientInformation","close","closed","confirm","console","const","constructor","continue","count","crypto","customElements","dddd","debugger","declare","decodeURI","decodeURIComponent","default","defaultStatus","delete","departFocus","devicePixelRatio","dispatchEvent","do","doIt","doNotTrack","doUpdateSnippet","document","element","else","encodeURI","encodeURIComponent","enum","eval","event","export","exports","extends","external","false","fetch","finally","findSnippetById","focus","foo","foon","fooo","for","frameElement","frames","from","function","fuzzy_match","fuzzy_match_simple","get","getComputedStyle","getMatchedCSSRules","getSelection","global","global","history","if","implements","import","importScripts","in","indexedDB","innerHeight","innerWidth","instanceof","interface","is","isFinite","isNaN","isSecureContext","jQuery","keyof","length","let","localStorage","location","locationbar","matchMedia","menubar","module","module","more","moveBy","moveTo","msContentScript","msCredentials","msWriteProfilerMark","name","namespace","navigator","never","new","null","number","object","of","offscreenBuffering","onabort","onafterprint","onbeforeprint","onbeforeunload","onblur","oncanplay","oncanplaythrough","onchange","onclick","oncompassneedscalibration","oncontextmenu","ondblclick","ondevicelight","ondevicemotion","ondeviceorientation","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","onhashchange","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onload","onloadeddata","onloadedmetadata","onloadstart","onmessage","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onmsgesturechange","onmsgesturedoubletap","onmsgestureend","onmsgesturehold","onmsgesturestart","onmsgesturetap","onmsinertiastart","onmspointercancel","onmspointerdown","onmspointerenter","onmspointerleave","onmspointermove","onmspointerout","onmspointerover","onmspointerup","onoffline","ononline","onorientationchange","onpagehide","onpageshow","onpause","onplay","onplaying","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointermove","onpointerout","onpointerover","onpointerup","onpopstate","onprogress","onratechange","onreadystatechange","onreset","onresize","onscroll","onseeked","onseeking","onselect","onstalled","onstorage","onsubmit","onsuspend","ontimeupdate","ontouchcancel","ontouchend","ontouchmove","ontouchstart","onunload","onvolumechange","onwaiting","onwheel","open","opener","orientation","outerHeight","outerWidth","package","pageXOffset","pageYOffset","parent","parseFloat","parseInt","payloadtype","performance","personalbar","postMessage","print","private","process","prompt","protected","public","readonly","releaseEvents","removeEventListener","requestAnimationFrame","require","require","resizeBy","resizeTo","return","screen","screenLeft","screenTop","screenX","screenY","scroll","scrollBy","scrollTo","scrollX","scrollY","scrollbars","self","sessionStorage","set","setImmediate","setInterval","setTimeout","speechSynthesis","static","status","statusbar","stop","string","styleMedia","super","switch","symbol","this","throw","toString","toolbar","top","true","try","type","typedoc","typeof","undefined","undefined","updateSnippet","uuid","vSomething","var","void","webkitCancelAnimationFrame","webkitConvertPointFromNodeToPage","webkitConvertPointFromPageToNode","webkitRTCPeerConnection","webkitRequestAnimationFrame","while","window","with","yield"]
}; });

//# sourceURL=file:///C:/Users/<USER>/Desktop/RecLastIDE/RecLast/src/vs/base/test/common/filters.perf.data.js