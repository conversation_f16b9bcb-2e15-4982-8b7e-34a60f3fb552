{"original": {"content": "const API = require('../src/api');\n\ndescribe('API', () => {\n  let api;\n  let database;\n\n  beforeAll(() => {\n    database = {\n      getAllBooks: jest.fn(),\n      getBooksByAuthor: jest.fn(),\n      getBooksByTitle: jest.fn(),\n    };\n    api = new API(database);\n  });\n\n  describe('GET /books', () => {\n    it('should return all books', async () => {\n      const mockBooks = [{ title: 'Book 1' }, { title: 'Book 2' }];\n      database.getAllBooks.mockResolvedValue(mockBooks);\n\n      const req = {};\n      const res = {\n        json: jest.fn(),\n      };\n\n      await api.register({\n        get: (path, handler) => {\n          if (path === '/books') {\n            handler(req, res);\n          }\n        },\n      });\n\n      expect(database.getAllBooks).toHaveBeenCalled();\n      expect(res.json).toHaveBeenCalledWith(mockBooks);\n    });\n  });\n\n  describe('GET /books/author/:author', () => {\n    it('should return books by author', async () => {\n      const mockAuthor = '<PERSON>';\n      const mockBooks = [{ title: 'Book 1', author: mockAuthor }, { title: 'Book 2', author: mockAuthor }];\n      database.getBooksByAuthor.mockResolvedValue(mockBooks);\n\n      const req = {\n        params: {\n          author: mockAuthor,\n        },\n      };\n      const res = {\n        json: jest.fn(),\n      };\n\n      await api.register({\n        get: (path, handler) => {\n          if (path === `/books/author/${mockAuthor}`) {\n            handler(req, res);\n          }\n        },\n      });\n\n      expect(database.getBooksByAuthor).toHaveBeenCalledWith(mockAuthor);\n      expect(res.json).toHaveBeenCalledWith(mockBooks);\n    });\n  });\n\n  describe('GET /books/title/:title', () => {\n    it('should return books by title', async () => {\n      const mockTitle = 'Book 1';\n      const mockBooks = [{ title: mockTitle, author: 'John Doe' }];\n      database.getBooksByTitle.mockResolvedValue(mockBooks);\n\n      const req = {\n        params: {\n          title: mockTitle,\n        },\n      };\n      const res = {\n        json: jest.fn(),\n      };\n\n      await api.register({\n        get: (path, handler) => {\n          if (path === `/books/title/${mockTitle}`) {\n            handler(req, res);\n          }\n        },\n      });\n\n      expect(database.getBooksByTitle).toHaveBeenCalledWith(mockTitle);\n      expect(res.json).toHaveBeenCalledWith(mockBooks);\n    });\n  });\n});\n", "fileName": "./1.tst"}, "modified": {"content": "const request = require('supertest');\nconst API = require('../src/api');\n\ndescribe('API', () => {\n  let api;\n  let database;\n\n  beforeAll(() => {\n    database = {\n      getAllBooks: jest.fn(),\n      getBooksByAuthor: jest.fn(),\n      getBooksByTitle: jest.fn(),\n    };\n    api = new API(database);\n  });\n\n  describe('GET /books', () => {\n    it('should return all books', async () => {\n      const mockBooks = [{ title: 'Book 1' }, { title: 'Book 2' }];\n      database.getAllBooks.mockResolvedValue(mockBooks);\n\n      const response = await request(api.app).get('/books');\n\n      expect(database.getAllBooks).toHaveBeenCalled();\n      expect(response.status).toBe(200);\n      expect(response.body).toEqual(mockBooks);\n    });\n  });\n\n  describe('GET /books/author/:author', () => {\n    it('should return books by author', async () => {\n      const mockAuthor = '<PERSON>';\n      const mockBooks = [{ title: 'Book 1', author: mockAuthor }, { title: 'Book 2', author: mockAuthor }];\n      database.getBooksByAuthor.mockResolvedValue(mockBooks);\n\n      const response = await request(api.app).get(`/books/author/${mockAuthor}`);\n\n      expect(database.getBooksByAuthor).toHaveBeenCalledWith(mockAuthor);\n      expect(response.status).toBe(200);\n      expect(response.body).toEqual(mockBooks);\n    });\n  });\n\n  describe('GET /books/title/:title', () => {\n    it('should return books by title', async () => {\n      const mockTitle = 'Book 1';\n      const mockBooks = [{ title: mockTitle, author: 'John Doe' }];\n      database.getBooksByTitle.mockResolvedValue(mockBooks);\n\n      const response = await request(api.app).get(`/books/title/${mockTitle}`);\n\n      expect(database.getBooksByTitle).toHaveBeenCalledWith(mockTitle);\n      expect(response.status).toBe(200);\n      expect(response.body).toEqual(mockBooks);\n    });\n  });\n});\n", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[1,1)", "modifiedRange": "[1,2)", "innerChanges": null}, {"originalRange": "[21,33)", "modifiedRange": "[22,23)", "innerChanges": [{"originalRange": "[21,15 -> 22,8]", "modifiedRange": "[22,15 -> 22,17]"}, {"originalRange": "[22,11 -> 22,16]", "modifiedRange": "[22,20 -> 22,21]"}, {"originalRange": "[22,19 -> 26,7]", "modifiedRange": "[22,24 -> 22,24]"}, {"originalRange": "[26,13 -> 27,9]", "modifiedRange": "[22,30 -> 22,47]"}, {"originalRange": "[27,12 -> 28,24]", "modifiedRange": "[22,50 -> 22,51]"}, {"originalRange": "[28,33 -> 32,9]", "modifiedRange": "[22,60 -> 22,60]"}]}, {"originalRange": "[35,36)", "modifiedRange": "[25,27)", "innerChanges": [{"originalRange": "[35,17 -> 35,22]", "modifiedRange": "[25,17 -> 25,29]"}, {"originalRange": "[35,26 -> 35,44]", "modifiedRange": "[25,33 -> 26,36]"}]}, {"originalRange": "[45,61)", "modifiedRange": "[36,37)", "innerChanges": [{"originalRange": "[45,15 -> 50,8]", "modifiedRange": "[36,15 -> 36,17]"}, {"originalRange": "[50,11 -> 50,16]", "modifiedRange": "[36,20 -> 36,21]"}, {"originalRange": "[50,19 -> 54,7]", "modifiedRange": "[36,24 -> 36,24]"}, {"originalRange": "[54,13 -> 56,24]", "modifiedRange": "[36,30 -> 36,51]"}, {"originalRange": "[56,54 -> 60,9]", "modifiedRange": "[36,81 -> 36,81]"}]}, {"originalRange": "[63,64)", "modifiedRange": "[39,41)", "innerChanges": [{"originalRange": "[63,17 -> 63,22]", "modifiedRange": "[39,17 -> 39,29]"}, {"originalRange": "[63,26 -> 63,44]", "modifiedRange": "[39,33 -> 40,36]"}]}, {"originalRange": "[73,89)", "modifiedRange": "[50,51)", "innerChanges": [{"originalRange": "[73,15 -> 73,16]", "modifiedRange": "[50,15 -> 50,21]"}, {"originalRange": "[73,19 -> 78,12]", "modifiedRange": "[50,24 -> 50,29]"}, {"originalRange": "[78,15 -> 79,16]", "modifiedRange": "[50,32 -> 50,34]"}, {"originalRange": "[79,19 -> 83,9]", "modifiedRange": "[50,37 -> 50,47]"}, {"originalRange": "[83,12 -> 84,24]", "modifiedRange": "[50,50 -> 50,51]"}, {"originalRange": "[84,52 -> 88,9]", "modifiedRange": "[50,79 -> 50,79]"}]}, {"originalRange": "[91,92)", "modifiedRange": "[53,55)", "innerChanges": [{"originalRange": "[91,17 -> 91,22]", "modifiedRange": "[53,17 -> 53,29]"}, {"originalRange": "[91,26 -> 91,44]", "modifiedRange": "[53,33 -> 54,36]"}]}]}