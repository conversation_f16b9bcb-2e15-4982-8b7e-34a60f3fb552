{"version": 3, "file": "RecLastProvider.js", "sourceRoot": "", "sources": ["../../src/ui/RecLastProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,eAAe;IAMxB,YACqB,OAAgC,EAChC,YAA0B,EAC1B,aAA4B,EAC5B,UAAsB,EACtB,aAA4B;QAJ5B,YAAO,GAAP,OAAO,CAAyB;QAChC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,kBAAa,GAAb,aAAa,CAAe;QAC5B,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAPzC,gBAAW,GAAgC,MAAM,CAAC;IAQvD,CAAC;IAEG,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,YAAY;aAC5B;SACJ,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEvE,+BAA+B;QAC/B,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EACtC,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,CAAC;QAEF,4CAA4C;QAC5C,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;YAC9C,IAAI,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;gBACvC,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY;QACpC,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,aAAa;gBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnC,MAAM;YACV,KAAK,YAAY;gBACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM;YACV,KAAK,gBAAgB;gBACjB,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC1C,MAAM;YACV,KAAK,cAAc;gBACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;SACb;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,IAAY;QACtD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;YACd,OAAO;SACV;QAED,IAAI;YACA,6BAA6B;YAC7B,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,IAAW,CAAC,CAAC;YAE7D,+BAA+B;YAC/B,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACtC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5D;YAED,8BAA8B;YAC9B,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,OAAO,EAAE;gBACT,UAAU,GAAG,GAAG,OAAO,sBAAsB,IAAI,EAAE,CAAC;aACvD;YAED,0BAA0B;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACrD,UAAU,EACV,MAAM,CAAC,QAAQ,KAAK,IAAI,CAC3B,CAAC;YAEF,mCAAmC;YACnC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAW,CAAC,CAAC;YAE3E,2BAA2B;YAC3B,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,cAAc,EAAE,QAAQ,CAAC,cAAc;aAC1C,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAEhD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,UAAU,KAAK,EAAE;aAC7B,CAAC,CAAC;SACN;gBAAS;YACN,qBAAqB;YACrB,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;SACN;IACL,CAAC;IAEO,eAAe,CAAC,IAAY;QAChC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAW,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;YAC5B,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAY;QAC3C,IAAI;YACA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACjE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,iBAAiB;gBACvB,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,IAAI;aACb,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,6BAA6B,KAAK,EAAE;aAChD,CAAC,CAAC;SACN;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACvC,IAAI;YACA,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,gBAAgB;gBACtB,EAAE,EAAE,EAAE;aACT,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,4BAA4B,KAAK,EAAE;aAC/C,CAAC,CAAC;SACN;IACL,CAAC;IAEO,kBAAkB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;YAC5B,IAAI,EAAE,eAAe;YACrB,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAEM,OAAO,CAAC,IAAiC;QAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACxE;IACL,CAAC;IAEO,iBAAiB,CAAC,OAAuB;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAExE,OAAO;sBACO,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAiGS,IAAI,CAAC,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,kCAAkC,OAAO,CAAC,QAAQ;8CAC7F,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,oCAAoC,OAAO,CAAC,UAAU;8CACnG,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,mCAAmC,OAAO,CAAC,SAAS;;;;;kBAK5H,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;;;;wFAImC,OAAO,CAAC,WAAW;mEACxC,OAAO,CAAC,IAAI;kEACb,OAAO,CAAC,KAAK;;;;;;;;;;;;;qCAa1C,IAAI,CAAC,WAAW;;;;;;;;;iCASpB,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oGAiCmD,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;gBAkBnG,CAAC;IACb,CAAC;IAEO,iBAAiB,CAAC,OAAc;QACpC,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACrB,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,mBAAmB,CAAC;YAC7E,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACtD,OAAO,uBAAuB,SAAS,aAAa,KAAK,cAAc,GAAG,CAAC,OAAO,QAAQ,CAAC;QAC/F,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC;;AAjZL,0CAkZC;AAjZ0B,wBAAQ,GAAG,kBAAkB,CAAC"}