import * as vscode from 'vscode';
export interface ModelResponse {
    text: string;
    tokens: number;
    processingTime: number;
}
export declare class ModelManager {
    private context;
    private modelPath;
    private isInitialized;
    private modelProcess;
    constructor(context: vscode.ExtensionContext);
    initialize(): Promise<void>;
    generateResponse(prompt: string, isTurkish?: boolean): Promise<ModelResponse>;
    private createSystemPrompt;
    private mockModelInference;
    isModelReady(): boolean;
    getModelInfo(): {
        path: string;
        ready: boolean;
    };
    dispose(): void;
}
//# sourceMappingURL=ModelManager.d.ts.map