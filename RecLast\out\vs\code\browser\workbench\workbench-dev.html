<!-- Copyright (C) Microsoft Corporation. All rights reserved. -->
<!DOCTYPE html>
<html>
	<head>
		<script>
			performance.mark('code/didStartRenderer');
		</script>
		<meta charset="utf-8" />

		<!-- Mobile tweaks -->
		<meta name="mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-title" content="Code">
		<link rel="apple-touch-icon" href="{{WORKBENCH_WEB_BASE_URL}}/resources/server/code-192.png" />

		<!-- Disable pinch zooming -->
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">

		<!-- Workbench Configuration -->
		<meta id="vscode-workbench-web-configuration" data-settings="{{WORKBENCH_WEB_CONFIGURATION}}">

		<!-- Workbench Auth Session -->
		<meta id="vscode-workbench-auth-session" data-settings="{{WORKBENCH_AUTH_SESSION}}">

		<!-- Builtin Extensions (running out of sources) -->
		<meta id="vscode-workbench-builtin-extensions" data-settings="{{WORKBENCH_BUILTIN_EXTENSIONS}}">

		<!-- Workbench Icon/Manifest/CSS -->
		<link rel="icon" href="{{WORKBENCH_WEB_BASE_URL}}/resources/server/favicon.ico" type="image/x-icon" />
		<link rel="manifest" href="{{WORKBENCH_WEB_BASE_URL}}/resources/server/manifest.json" crossorigin="use-credentials" />
		<style id="vscode-css-modules" type="text/css" media="screen"></style>

	</head>

	<body aria-label="">
	</body>

	<!-- Startup (do not modify order of script tags!) -->
	<script>
		const baseUrl = new URL('{{WORKBENCH_WEB_BASE_URL}}', window.location.origin).toString();
		globalThis._VSCODE_FILE_ROOT = baseUrl + '/out/';
	</script>
	<script>
		const sheet = document.getElementById('vscode-css-modules').sheet;
		globalThis._VSCODE_CSS_LOAD = function (url) {
			sheet.insertRule(`@import url(${url});`);
		};

		const importMap = { imports: {} };
		const cssModules = JSON.parse('{{WORKBENCH_DEV_CSS_MODULES}}');
		for (const cssModule of cssModules) {
			const cssUrl = new URL(cssModule, globalThis._VSCODE_FILE_ROOT).href;
			const jsSrc = `globalThis._VSCODE_CSS_LOAD('${cssUrl}');\n`;
			const blob = new Blob([jsSrc], { type: 'application/javascript' });
			importMap.imports[cssUrl] = URL.createObjectURL(blob);
		}
		const importMapElement = document.createElement('script');
		importMapElement.type = 'importmap';
		importMapElement.setAttribute('nonce', '1nline-m4p')
		importMapElement.textContent = JSON.stringify(importMap, undefined, 2);
		document.head.appendChild(importMapElement);
	</script>
	<script>
		performance.mark('code/willLoadWorkbenchMain');
	</script>
	<script type="module" src="{{WORKBENCH_WEB_BASE_URL}}/out/vs/code/browser/workbench/workbench.js"></script>
</html>
