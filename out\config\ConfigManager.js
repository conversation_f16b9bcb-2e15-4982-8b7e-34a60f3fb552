"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const vscode = __importStar(require("vscode"));
class ConfigManager {
    constructor() {
        this.config = vscode.workspace.getConfiguration('reclast');
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('reclast')) {
                this.config = vscode.workspace.getConfiguration('reclast');
            }
        });
    }
    getConfig() {
        return {
            language: this.config.get('language', 'tr'),
            mode: this.config.get('mode', 'chat'),
            modelPath: this.config.get('modelPath', './models/mamba-7b/ggml-model.bin'),
            webLookupEnabled: this.config.get('webLookupEnabled', false)
        };
    }
    async setLanguage(language) {
        await this.config.update('language', language, vscode.ConfigurationTarget.Global);
    }
    async setMode(mode) {
        await this.config.update('mode', mode, vscode.ConfigurationTarget.Global);
    }
    async setModelPath(path) {
        await this.config.update('modelPath', path, vscode.ConfigurationTarget.Global);
    }
    async setWebLookupEnabled(enabled) {
        await this.config.update('webLookupEnabled', enabled, vscode.ConfigurationTarget.Global);
    }
    async toggleLanguage() {
        const currentLang = this.getConfig().language;
        const newLang = currentLang === 'tr' ? 'en' : 'tr';
        await this.setLanguage(newLang);
    }
    getLanguageStrings() {
        const config = this.getConfig();
        if (config.language === 'tr') {
            return {
                chatMode: 'Sohbet Modu',
                manualMode: 'Manuel Mod',
                agentMode: 'Ajan Modu',
                askQuestion: 'Sorunuzu yazın...',
                send: 'Gönder',
                clear: 'Temizle',
                createSnapshot: 'Snapshot Oluştur',
                loading: 'Yükleniyor...',
                error: 'Hata',
                success: 'Başarılı',
                webLookup: 'Web Araması',
                settings: 'Ayarlar'
            };
        }
        else {
            return {
                chatMode: 'Chat Mode',
                manualMode: 'Manual Mode',
                agentMode: 'Agent Mode',
                askQuestion: 'Type your question...',
                send: 'Send',
                clear: 'Clear',
                createSnapshot: 'Create Snapshot',
                loading: 'Loading...',
                error: 'Error',
                success: 'Success',
                webLookup: 'Web Lookup',
                settings: 'Settings'
            };
        }
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map