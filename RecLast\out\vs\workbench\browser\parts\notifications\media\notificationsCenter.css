/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench > .notifications-center {
	position: absolute;
	z-index: 1000;
	right: 7px; /* attempt to position at same location as a toast */
	bottom: 29px; /* 22px status bar height + 7px (attempt to position at same location as a toast) */
	display: none;
	overflow: hidden;
	border-radius: 4px;
}

.monaco-workbench.nostatusbar > .notifications-center {
	bottom: 11px; /* attempt to position at same location as a toast */
}

.monaco-workbench > .notifications-center.visible {
	display: block;
}

/* Header */

.monaco-workbench > .notifications-center > .notifications-center-header {
	display: flex;
	align-items: center;
	padding-left: 8px;
	padding-right: 5px;
	height: 35px;
}

.monaco-workbench > .notifications-center > .notifications-center-header > .notifications-center-header-title {
	text-transform: uppercase;
	font-size: 11px;
}

.monaco-workbench > .notifications-center > .notifications-center-header > .notifications-center-header-toolbar {
	flex: 1;
}

.monaco-workbench > .notifications-center > .notifications-center-header > .notifications-center-header-toolbar .actions-container {
	justify-content: flex-end;
}

.monaco-workbench > .notifications-center .notifications-list-container .monaco-list-row:not(:last-child) > .notification-list-item {
	border-bottom: 1px solid var(--vscode-notifications-border);
}

.monaco-workbench > .notifications-center .notifications-list-container .monaco-list-row:last-child {
	border-radius: 0px 0px 4px 4px; /* adopt the border radius at the end of the notifications center */
}

/* Icons */

.monaco-workbench > .notifications-center .codicon.codicon-error {
	color: var(--vscode-notificationsErrorIcon-foreground) !important;
}

.monaco-workbench > .notifications-center .codicon.codicon-warning {
	color: var(--vscode-notificationsWarningIcon-foreground) !important;
}

.monaco-workbench > .notifications-center .codicon.codicon-info {
	color: var(--vscode-notificationsInfoIcon-foreground) !important;
}
