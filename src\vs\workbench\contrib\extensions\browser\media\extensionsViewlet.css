/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.extensions-viewlet {
	position: relative;
	height: 100%;
}

.extensions-viewlet .hidden {
	display: none !important;
	visibility: hidden;
}

.extensions-viewlet > .overlay {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2;
}

.extensions-viewlet > .header {
	height: 41px;
	box-sizing: border-box;
	padding: 5px 12px 6px 20px;
}

.extensions-viewlet > .header  > .extensions-search-container {
	position: relative;
}

.extensions-viewlet > .header  > .extensions-search-container >  .search-box {
	width: 100%;
	height: 28px;
	line-height: 18px;
	box-sizing: border-box;
	padding: 4px;
	border: 1px solid transparent;
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
}

.extensions-viewlet > .header  > .extensions-search-container >  .extensions-search-actions-container {
	display: flex;
	align-items: center;
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
}

.extensions-viewlet > .header > .notification-container {
	margin-top: 10px;
	display: flex;
	justify-content: space-between;
}

.extensions-viewlet > .header .notification-container .message-container {
	padding-left: 4px;
}

.extensions-viewlet > .header .notification-container .message-container .codicon {
	vertical-align: text-top;
	padding-right: 5px;
}

.extensions-viewlet .notification-container .message-text-action {
	cursor: pointer;
	margin-left: 5px;
	color: var(--vscode-textLink-foreground);
	text-decoration: underline;
}

.extensions-viewlet .notification-container .message-text-action:hover,
.extensions-viewlet .notification-container .message-text-action:active {
	color: var(--vscode-textLink-activeForeground);
}

.extensions-viewlet .notification-container .message-action {
	cursor: pointer;
	padding: 2px;
	border-radius: 5px;
	height: 16px;
}

.extensions-viewlet .notification-container .message-action:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
	outline: 1px dashed var(--vscode-toolbar-hoverOutline);
}

.extensions-viewlet > .extensions {
	height: calc(100% - 41px);
}

.extensions-viewlet > .extensions .extension-list-item .monaco-action-bar,
.extensions-viewlet > .extensions .extension-view-header .monaco-action-bar {
	margin-right: 4px;
}

.extensions-viewlet > .extensions .extension-view-header .count-badge-wrapper {
	margin-right: 12px;
}

.extensions-viewlet > .extensions .extension-view-header .monaco-action-bar .action-item > .action-label.icon.codicon {
	vertical-align: middle;
	line-height: 22px;
}

.extensions-viewlet > .extensions .extension-view-header .monaco-action-bar .action-item.disabled  {
	display: none;
}

.extensions-viewlet > .extensions .panel-header {
	padding-right: 12px;
}

.extensions-viewlet > .extensions .panel-header > .title {
	flex: 1;
}

.extensions-viewlet > .extensions .panel-header > .actions.show {
	flex: inherit;
}

.extensions-viewlet > .extensions .message-container {
	padding: 5px 9px 5px 20px;
	cursor: default;
	display: flex;
}

.extensions-viewlet > .extensions .message-container .message {
	padding-left: 5px;
}

.extensions-viewlet > .extensions .message-container .severity-icon {
	flex-shrink: 0;
}

.extensions-viewlet > .extensions .extension-list-item {
	position: absolute;
}

.extensions-viewlet > .extensions .extension-list-item.loading {
	background: url('loading.svg') center center no-repeat;
}

.monaco-workbench.vs-dark .extensions-viewlet > .extensions .extension-list-item.loading {
	background-image: url('loading-dark.svg');
}

.monaco-workbench.hc-black .extensions-viewlet > .extensions .extension-list-item.loading {
	background-image: url('loading-hc.svg');
}

.extensions-viewlet > .extensions .extension-list-item.loading > .icon-container {
	display: none;
}
.extensions-viewlet.narrow > .extensions .extension-list-item > .icon-container {
	display: flex;
	align-items: flex-start;
	padding-top: 10px;
}
.extensions-viewlet.narrow > .extensions .extension-list-item > .icon-container > .extension-icon .icon {
	width: 24px;
	height: 24px;
	padding-right: 8px;
}
.extensions-viewlet.narrow > .extensions .extension-list-item > .icon-container > .extension-icon .codicon {
	font-size: 24px !important;
	padding-right: 8px;
}

.extensions-viewlet.narrow > .extensions .extension-list-item > .icon-container .extension-badge,
.extensions-viewlet.mini > .extensions .extension-list-item > .icon-container,
.extensions-viewlet.mini > .extensions .extension-list-item > .details > .header-container > .header > .ratings,
.extensions-viewlet.mini > .extensions .extension-bookmark-container {
	display: none;
}

.extensions-viewlet.narrow > .extensions .extension-list-item > .details > .footer > .monaco-action-bar > .actions-container .extension-action {
	max-width: 100px;
}

.monaco-workbench.vs .extensions-viewlet > .extensions .monaco-list-row.disabled:not(.selected) > .extension-list-item > .icon-container > .extension-icon .icon,
.monaco-workbench.vs-dark .extensions-viewlet > .extensions .monaco-list-row.disabled:not(.selected) > .extension-list-item > .icon-container > .extension-icon .icon,
.monaco-workbench.vs .extensions-viewlet > .extensions .monaco-list-row.disabled:not(.selected) > .extension-list-item > .details > .header-container .codicon,
.monaco-workbench.vs-dark .extensions-viewlet > .extensions .monaco-list-row.disabled:not(.selected) > .extension-list-item > .details > .header-container .codicon {
	opacity: 0.5;
}

.extensions-viewlet .codicon-error::before {
	color: var(--vscode-editorError-foreground);
}

.extensions-viewlet .codicon-warning::before {
	color: var(--vscode-editorWarning-foreground);
}

.extensions-viewlet .codicon-info::before {
	color: var(--vscode-editorInfo-foreground);
}
