{"original": {"content": "\tcontextKeyService.onDidChangeContext(this.onDidChangeContext, this, this.disposables);\n\t\tthis.disposables.add(Event.filter(viewsRegistry.onDidChangeViewWelcomeContent, id => id === this.id)(this.onDidChangeViewWelcomeContent, this, this.disposables));\n\t\tthis.onDidChangeViewWelcomeContent();\n\t}\n\n\tprivate onDidChangeViewWelcomeContent(): void {\n\t\tconst descriptors = viewsRegistry.getViewWelcomeContent(this.id);\n\n\t\tthis.items = [];\n\n\t\tfor (const descriptor of descriptors) {\n\t\t\tif (descriptor.when === 'default') {\n\t\t\t\tthis.defaultItem = { descriptor, visible: true };\n\t\t\t} else {\n\t\t\t\tconst visible = descriptor.when ? this.contextKeyService.contextMatchesRules(descriptor.when) : true;\n\t\t\t\tthis.items.push({ descriptor, visible });\n\t\t\t}\n\t\t}\n\n\t\tthis._onDidChange.fire();\n\t}\n\n\tprivate onDidChangeContext(): void {\n\t\tlet didChange = false;\n\n\t\tfor (const item of this.items) {\n\t\t\tif (!item.descriptor.when || item.descriptor.when === 'default') {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tconst visible = this.contextKeyService.contextMatchesRules(item.descriptor.when);\n\n\t\t\tif (item.visible === visible) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\titem.visible = visible;\n\t\t\tdidChange = true;\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis._onDidChange.fire();\n\t\t}\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t}\n}\n\nexport abstract class ViewPane extends Pane implements IView {\n\n\tprivate static readonly AlwaysShowActionsConfig = 'workbench.view.alwaysShowHeaderActions';\n\n\tprivate _onDidFocus = this._register(new Emitter<void>());\n\treadonly onDidFocus: Event<void> = this._onDidFocus.event;\n\n\tprivate _onDidBlur = this._register(new Emitter<void>());\n\treadonly onDidBlur: Event<void> = this._onDidBlur.event;\n\n\tprivate _onDidChangeBodyVisibility = this._register(new Emitter<boolean>());\n\treadonly onDidChangeBodyVisibility: Event<boolean> = this._onDidChangeBodyVisibility.event;\n\n\tprotected _onDidChangeTitleArea = this._register(new Emitter<void>());\n\treadonly onDidChangeTitleArea: Event<void> = this._onDidChangeTitleArea.event;\n\n\tprotected _onDidChangeViewWelcomeState = this._register(new Emitter<void>());\n\treadonly onDidChangeViewWelcomeState: Event<void> = this._onDidChangeViewWelcomeState.event;\n\n\tprivate _isVisible: boolean = false;\n\treadonly id: string;\n\n\tprivate _title: string;\n\tpublic get title(): string {\n\t\treturn this._title;\n\t}\n\n\tprivate _titleDescription: string | undefined;\n\tpublic get titleDescription(): string | undefined {\n\t\treturn this._titleDescription;\n\t}\n\n\treadonly menuActions: CompositeMenuActions;\n\n\tprivate progressBar!: ProgressBar;\n\tprivate progressIndicator!: IProgressIndicator;\n\n\tprivate toolbar?: WorkbenchToolBar;\n\tprivate readonly showActions: ViewPaneShowActions;\n\tprivate headerContainer?: HTMLElement;\n\tprivate titleContainer?: HTMLElement;\n\tprivate titleDescriptionContainer?: HTMLElement;\n\tprivate iconContainer?: HTMLElement;\n\tprotected twistiesContainer?: HTMLElement;\n\n\tprivate bodyContainer!: HTMLElement;\n\tprivate viewWelcomeContainer!: HTMLElement;\n\tprivate viewWelcomeDisposable: IDisposable = Disposable.None;\n\tprivate viewWelcomeController: ViewWelcomeController;\n\n\tprotected readonly scopedContextKeyService: IContextKeyService;\n\n\tconstructor(\n\t\toptions: IViewPaneOptions,\n\t\t@IKeybindingService protected keybindingService: IKeybindingService,\n\t\t@IContextMenuService protected contextMenuService: IContextMenuService,\n\t\t@IConfigurationService protected readonly configurationService: IConfigurationService,\n\t\t@IContextKeyService protected contextKeyService: IContextKeyService,\n\t\t@IViewDescriptorService protected viewDescriptorService: IViewDescriptorService,\n\t\t@IInstantiationService protected instantiationService: IInstantiationService,\n\t\t@IOpenerService protected openerService: IOpenerService,\n\t\t@IThemeService protected themeService: IThemeService,\n\t\t@ITelemetryService protected telemetryService: ITelemetryService,\n\t) {\n\t\tsuper({ ...options, ...{ orientation: viewDescriptorService.getViewLocationById(options.id) === ViewContainerLocation.Panel ? Orientation.HORIZONTAL : Orientation.VERTICAL } });\n\n\t\tthis.id = options.id;\n\t\tthis._title = options.title;\n\t\tthis._titleDescription = options.titleDescription;\n\t\tthis.showActions = options.showActions ?? ViewPaneShowActions.Default;\n\n\t\tthis.scopedContextKeyService = this._register(contextKeyService.createScoped(this.element));\n\t\tthis.scopedContextKeyService.createKey('view', this.id);\n\t\tconst viewLocationKey = this.scopedContextKeyService.createKey('viewLocation', ViewContainerLocationToString(viewDescriptorService.getViewLocationById(this.id)!));\n\t\tthis._register(Event.filter(viewDescriptorService.onDidChangeLocation, e => e.views.some(view => view.id === this.id))(() => viewLocationKey.set(ViewContainerLocationToString(viewDescriptorService.getViewLocationById(this.id)!))));\n\n\t\tthis.menuActions = this._register(this.instantiationService.createChild(new ServiceCollection([IContextKeyService, this.scopedContextKeyService])).createInstance(CompositeMenuActions, options.titleMenuId ?? MenuId.ViewTitle, MenuId.ViewTitleContext, { shouldForwardArgs: !options.donotForwardArgs }));\n\t\tthis._register(this.menuActions.onDidChange(() => this.updateActions()));\n\n\t\tthis.viewWelcomeController = this._register(new ViewWelcomeController(this.id, contextKeyService));\n\t}\n\n\toverride get headerVisible(): boolean {\n\t\treturn super.headerVisible;\n\t}\n\n\toverride set headerVisible(visible: boolean) {\n\t\tsuper.headerVisible = visible;\n\t\tthis.element.classList.toggle('merged-header', !visible);\n\t}\n\n\tsetVisible(visible: boolean): void {\n\t\tif (this._isVisible !== visible) {\n\t\t\tthis._isVisible = visible;\n\n\t\t\tif (this.isExpanded()) {\n\t\t\t\tthis._onDidChangeBodyVisibility.fire(visible);\n\t\t\t}\n\t\t}\n\t}\n\n\tisVisible(): boolean {\n\t\treturn this._isVisible;\n\t}\n\n\tisBodyVisible(): boolean {\n\t\treturn this._isVisible && this.isExpanded();\n\t}\n\n\toverride setExpanded(expanded: boolean): boolean {\n\t\tconst changed = super.setExpanded(expanded);\n\t\tif (changed) {\n\t\t\tthis._onDidChangeBodyVisibility.fire(expanded);\n\t\t}\n\t\tif (this.twistiesContainer) {\n\t\t\tthis.twistiesContainer.classList.remove(...ThemeIcon.asClassNameArray(this.getTwistyIcon(!expanded)));\n\t\t\tthis.twistiesContainer.classList.add(...ThemeIcon.asClassNameArray(this.getTwistyIcon(expanded)));\n\t\t}\n\t\treturn changed;\n\t}\n\n\toverride render(): void {\n\t\tsuper.render();\n\n\t\tconst focusTracker = trackFocus(this.element);\n\t\tthis._register(focusTracker);\n\t\tthis._register(focusTracker.onDidFocus(() => this._onDidFocus.fire()));\n\t\tthis._register(focusTracker.onDidBlur(() => this._onDidBlur.fire()));\n\t}\n\n\tprotected renderHeader(container: HTMLElement): void {\n\t\tthis.headerContainer = container;\n\n\t\tthis.twistiesContainer = append(container, $(ThemeIcon.asCSSSelector(this.getTwistyIcon(this.isExpanded()))));\n\n\t\tthis.renderHeaderTitle(container, this.title);\n\n\t\tconst actions = append(container, $('.actions'));\n\t\tactions.classList.toggle('show-always', this.showActions === ViewPaneShowActions.Always);\n\t\tactions.classList.toggle('show-expanded', this.showActions === ViewPaneShowActions.WhenExpanded);\n\t\tthis.toolbar = this.instantiationService.createInstance(WorkbenchToolBar, actions, {\n\t\t\torientation: ActionsOrientation.HORIZONTAL,\n\t\t\tactionViewItemProvider: action => this.getActionViewItem(action),\n\t\t\tariaLabel: nls.localize('viewToolbarAriaLabel', \"{0} actions\", this.title),\n\t\t\tgetKeyBinding: action => this.keybindingService.lookupKeybinding(action.id),\n\t\t\trenderDropdownAsChildElement: true,\n\t\t\tactionRunner: this.getActionRunner(),\n\t\t\tresetMenu: this.menuActions.menuId\n\t\t});\n\n\t\tthis._register(this.toolbar);\n\t\tthis.setActions();\n\n\t\tthis._register(addDisposableListener(actions, EventType.CLICK, e => e.preventDefault()));\n\n\t\tconst viewContainerModel = this.viewDescriptorService.getViewContainerByViewId(this.id);\n\t\tif (viewContainerModel) {\n\t\t\tthis._register(this.viewDescriptorService.getViewContainerModel(viewContainerModel).onDidChangeContainerInfo(({ title }) => this.updateTitle(this.title)));\n\t\t} else {\n\t\t\tconsole.error(`View container model not found for view ${this.id}`);\n\t\t}\n\n\t\tconst onDidRelevantConfigurationChange = Event.filter(this.configurationService.onDidChangeConfiguration, e => e.affectsConfiguration(ViewPane.AlwaysShowActionsConfig));\n\t\tthis._register(onDidRelevantConfigurationChange(this.updateActionsVisibility, this));\n\t\tthis.updateActionsVisibility();\n\t}\n\n\tprotected getTwistyIcon(expanded: boolean): ThemeIcon {\n\t\treturn expanded ? viewPaneContainerExpandedIcon : viewPaneContainerCollapsedIcon;\n\t}\n\n\toverride style(styles: IPaneStyles): void {\n\t\tsuper.style(styles);\n\n\t\tconst icon = this.getIcon();\n\t\tif (this.iconContainer) {\n\t\t\tconst fgColor = asCssValueWithDefault(styles.headerForeground, asCssVariable(foreground));\n\t\t\tif (URI.isUri(icon)) {\n\t\t\t\t// Apply background color to activity bar item provided with iconUrls\n\t\t\t\tthis.iconContainer.style.backgroundColor = fgColor;\n\t\t\t\tthis.iconContainer.style.color = '';\n\t\t\t} else {\n\t\t\t\t// Apply foreground color to activity bar items provided with codicons\n\t\t\t\tthis.iconContainer.style.color = fgColor;\n\t\t\t\tthis.iconContainer.style.backgroundColor = '';\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate getIcon(): ThemeIcon | URI {\n\t\treturn this.viewDescriptorService.getViewDescriptorById(this.id)?.containerIcon || defaultViewIcon;\n\t}\n\n\tprotected renderHeaderTitle(container: HTMLElement, title: string): void {\n\t\tthis.iconContainer = append(container, $('.icon', undefined));\n\t\tconst icon = this.getIcon();\n\n\t\tlet cssClass: string | undefined = undefined;\n\t\tif (URI.isUri(icon)) {\n\t\t\tcssClass = `view-${this.id.replace(/[\\.\\:]/g, '-')}`;\n\t\t\tconst iconClass = `.pane-header .icon.${cssClass}`;\n\n\t\t\tcreateCSSRule(iconClass, `\n\t\t\t\tmask: ${asCSSUrl(icon)} no-repeat 50% 50%;\n\t\t\t\tmask-size: 24px;\n\t\t\t\t-webkit-mask: ${asCSSUrl(icon)} no-repeat 50% 50%;\n\t\t\t\t-webkit-mask-size: 16px;\n\t\t\t`);\n\t\t} else if (ThemeIcon.isThemeIcon(icon)) {\n\t\t\tcssClass = ThemeIcon.asClassName(icon);\n\t\t}\n\n\t\tif (cssClass) {\n\t\t\tthis.iconContainer.classList.add(...cssClass.split(' '));\n\t\t}\n\n\t\tconst calculatedTitle = this.calculateTitle(title);\n\t\tthis.titleContainer = append(container, $('h3.title', { title: calculatedTitle }, calculatedTitle));\n\n\t\tif (this._titleDescription) {\n\t\t\tthis.setTitleDescription(this._titleDescription);\n\t\t}\n\n\t\tthis.iconContainer.title = calculatedTitle;\n\t\tthis.iconContainer.setAttribute('aria-label', calculatedTitle);\n\t}\n\n\tprotected updateTitle(title: string): void {\n\t\tconst calculatedTitle = this.calculateTitle(title);\n\t\tif (this.titleContainer) {\n\t\t\tthis.titleContainer.textContent = calculatedTitle;\n\t\t\tthis.titleContainer.setAttribute('title', calculatedTitle);\n\t\t}\n\n\t\tif (this.iconContainer) {\n\t\t\tthis.iconContainer.title = calculatedTitle;\n\t\t\tthis.iconContainer.setAttribute('aria-label', calculatedTitle);\n\t\t}\n\n\t\tthis._title = title;\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tprivate setTitleDescription(description: string | undefined) {\n\t\tif (this.titleDescriptionContainer) {\n\t\t\tthis.titleDescriptionContainer.textContent = description ?? '';\n\t\t\tthis.titleDescriptionContainer.setAttribute('title', description ?? '');\n\t\t}\n\t\telse if (description && this.titleContainer) {\n\t\t\tthis.titleDescriptionContainer = after(this.titleContainer, $('span.description', { title: description }, description));\n\t\t}\n\t}\n\n\tprotected updateTitleDescription(description?: string | undefined): void {\n\t\tthis.setTitleDescription(description);\n\n\t\tthis._titleDescription = description;\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tprivate calculateTitle(title: string): string {\n\t\tconst viewContainer = this.viewDescriptorService.getViewContainerByViewId(this.id)!;\n\t\tconst model = this.viewDescriptorService.getViewContainerModel(viewContainer);\n\t\tconst viewDescriptor = this.viewDescriptorService.getViewDescriptorById(this.id);\n\t\tconst isDefault = this.viewDescriptorService.getDefaultContainerById(this.id) === viewContainer;\n\n\t\tif (!isDefault && viewDescriptor?.containerTitle && model.title !== viewDescriptor.containerTitle) {\n\t\t\treturn `${viewDescriptor.containerTitle}: ${title}`;\n\t\t}\n\n\t\treturn title;\n\t}\n\n\tprivate scrollableElement!: DomScrollableElement;\n\n\tprotected renderBody(container: HTMLElement): void {\n\t\tthis.bodyContainer = container;\n\n\t\tconst viewWelcomeContainer = append(container, $('.welcome-view'));\n\t\tthis.viewWelcomeContainer = $('.welcome-view-content', { tabIndex: 0 });\n\t\tthis.scrollableElement = this._register(new DomScrollableElement(this.viewWelcomeContainer, {\n\t\t\talwaysConsumeMouseWheel: true,\n\t\t\thorizontal: ScrollbarVisibility.Hidden,\n\t\t\tvertical: ScrollbarVisibility.Visible,\n\t\t}));\n\n\t\tappend(viewWelcomeContainer, this.scrollableElement.getDomNode());\n\n\t\tconst onViewWelcomeChange = Event.any(this.viewWelcomeController.onDidChange, this.onDidChangeViewWelcomeState);\n\t\tthis._register(onViewWelcomeChange(this.updateViewWelcome, this));\n\t\tthis.updateViewWelcome();\n\t}\n\n\tprotected layoutBody(height: number, width: number): void {\n\t\tif (this.shouldShowWelcome()) {\n\t\t\tthis.viewWelcomeContainer.style.height = `${height}px`;\n\t\t\tthis.viewWelcomeContainer.style.width = `${width}px`;\n\t\t\tthis.viewWelcomeContainer.classList.toggle('wide', width > 640);\n\t\t\tthis.scrollableElement.scanDomNode();\n\t\t}\n\t}\n\n\tonDidScrollRoot() {\n\t\t// noop\n\t}\n\n\tgetProgressIndicator() {\n\t\tif (this.progressBar === undefined) {\n\t\t\t// Progress bar\n\t\t\tthis.progressBar = this._register(new ProgressBar(this.element, defaultProgressBarStyles));\n\t\t\tthis.progressBar.hide();\n\t\t}\n\n\t\tif (this.progressIndicator === undefined) {\n\t\t\tconst that = this;\n\t\t\tthis.progressIndicator = new ScopedProgressIndicator(assertIsDefined(this.progressBar), new class extends AbstractProgressScope {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper(that.id, that.isBodyVisible());\n\t\t\t\t\tthis._register(that.onDidChangeBodyVisibility(isVisible => isVisible ? this.onScopeOpened(that.id) : this.onScopeClosed(that.id)));\n\t\t\t\t}\n\t\t\t}());\n\t\t}\n\t\treturn this.progressIndicator;\n\t}\n\n\tprotected getProgressLocation(): string {\n\t\treturn this.viewDescriptorService.getViewContainerByViewId(this.id)!.id;\n\t}\n\n\tprotected getBackgroundColor(): string {\n\t\tswitch (this.viewDescriptorService.getViewLocationById(this.id)) {\n\t\t\tcase ViewContainerLocation.Panel:\n\t\t\t\treturn PANEL_BACKGROUND;\n\t\t\tcase ViewContainerLocation.Sidebar:\n\t\t\tcase ViewContainerLocation.AuxiliaryBar:\n\t\t\t\treturn SIDE_BAR_BACKGROUND;\n\t\t}\n\n\t\treturn SIDE_BAR_BACKGROUND;\n\t}\n\n\tfocus(): void {\n\t\tif (this.shouldShowWelcome()) {\n\t\t\tthis.viewWelcomeContainer.focus();\n\t\t} else if (this.element) {\n\t\t\tthis.element.focus();\n\t\t\tthis._onDidFocus.fire();\n\t\t}\n\t}\n\n\tprivate setActions(): void {\n\t\tif (this.toolbar) {\n\t\t\tconst primaryActions = [...this.menuActions.getPrimaryActions()];\n\t\t\tif (this.shouldShowFilterInHeader()) {\n\t\t\t\tprimaryActions.unshift(VIEWPANE_FILTER_ACTION);\n\t\t\t}\n\t\t\tthis.toolbar.setActions(prepareActions(primaryActions), prepareActions(this.menuActions.getSecondaryActions()));\n\t\t\tthis.toolbar.context = this.getActionsContext();\n\t\t}\n\t}\n\n\tprivate updateActionsVisibility(): void {\n\t\tif (!this.headerContainer) {\n\t\t\treturn;\n\t\t}\n\t\tconst shouldAlwaysShowActions = this.configurationService.getValue<boolean>('workbench.view.alwaysShowHeaderActions');\n\t\tthis.headerContainer.classList.toggle('actions-always-visible', shouldAlwaysShowActions);\n\t}\n\n\tprotected updateActions(): void {\n\t\tthis.setActions();\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tgetActionViewItem(action: IAction, options?: IDropdownMenuActionViewItemOptions): IActionViewItem | undefined {\n\t\tif (action.id === VIEWPANE_FILTER_ACTION.id) {\n\t\t\tconst that = this;\n\t\t\treturn new class extends BaseActionViewItem {\n\t\t\t\tconstructor() { super(null, action); }\n\t\t\t\toverride setFocusable(): void { /* noop input elements are focusable by default */ }\n\t\t\t\toverride get trapsArrowNavigation(): boolean { return true; }\n\t\t\t\toverride render(container: HTMLElement): void {\n\t\t\t\t\tcontainer.classList.add('viewpane-filter-container');\n\t\t\t\t\tappend(container, that.getFilterWidget()!.element);\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t\treturn createActionViewItem(this.instantiationService, action, { ...options, ...{ menuAsChild: action instanceof SubmenuItemAction } });\n\t}\n\n\tgetActionsContext(): unknown {\n\t\treturn undefined;\n\t}\n\n\tgetActionRunner(): IActionRunner | undefined {\n\t\treturn undefined;\n\t}\n\n\tgetOptimalWidth(): number {\n\t\treturn 0;\n\t}\n\n\tsaveState(): void {\n\t\t// Subclasses to implement for saving state\n\t}\n\n\tprivate updateViewWelcome(): void {\n\t\tthis.viewWelcomeDisposable.dispose();\n\n\t\tif (!this.shouldShowWelcome()) {\n\t\t\tthis.bodyContainer.classList.remove('welcome');\n\t\t\tthis.viewWelcomeContainer.innerText = '';\n\t\t\tthis.scrollableElement.scanDomNode();\n\t\t\treturn;\n\t\t}\n\n\t\tconst contents = this.viewWelcomeController.contents;\n\n\t\tif (contents.length === 0) {\n\t\t\tthis.bodyContainer.classList.remove('welcome');\n\t\t\tthis.viewWelcomeContainer.innerText = '';\n\t\t\tthis.scrollableElement.scanDomNode();\n\t\t\treturn;\n\t\t}\n\n\t\tconst disposables = new DisposableStore();\n\t\tthis.bodyContainer.classList.add('welcome');\n\t\tthis.viewWelcomeContainer.innerText = '';\n\n\t\tfor (const { content, precondition } of contents) {\n\t\t\tconst lines = content.split('\\n');\n\n\t\t\tfor (let line of lines) {\n\t\t\t\tline = line.trim();\n\n\t\t\t\tif (!line) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst linkedText = parseLinkedText(line);\n\n\t\t\t\tif (linkedText.nodes.length === 1 && typeof linkedText.nodes[0] !== 'string') {\n\t\t\t\t\tconst node = linkedText.nodes[0];\n\t\t\t\t\tconst buttonContainer = append(this.viewWelcomeContainer, $('.button-container'));\n\t\t\t\t\tconst button = new Button(buttonContainer, { title: node.title, supportIcons: true, ...defaultButtonStyles });\n\t\t\t\t\tbutton.label = node.label;\n\t\t\t\t\tbutton.onDidClick(_ => {\n\t\t\t\t\t\tthis.telemetryService.publicLog2<{ viewId: string; uri: string }, WelcomeActionClassification>('views.welcomeAction', { viewId: this.id, uri: node.href });\n\t\t\t\t\t\tthis.openerService.open(node.href, { allowCommands: true });\n\t\t\t\t\t}, null, disposables);\n\t\t\t\t\tdisposables.add(button);\n\n\t\t\t\t\tif (precondition) {\n\t\t\t\t\t\tconst updateEnablement = () => button.enabled = this.contextKeyService.contextMatchesRules(precondition);\n\t\t\t\t\t\tupdateEnablement();\n\n\t\t\t\t\t\tconst keys = new Set();\n\t\t\t\t\t\tprecondition.keys().forEach(key => keys.add(key));\n\t\t\t\t\t\tconst onDidChangeContext = Event.filter(this.contextKeyService.onDidChangeContext, e => e.affectsSome(keys));\n\t\t\t\t\t\tonDidChangeContext(updateEnablement, null, disposables);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconst p = append(this.viewWelcomeContainer, $('p'));\n\n\t\t\t\t\tfor (const node of linkedText.nodes) {\n\t\t\t\t\t\tif (typeof node === 'string') {\n\t\t\t\t\t\t\tappend(p, document.createTextNode(node));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst link = disposables.add(this.instantiationService.createInstance(Link, p, node, {}));\n\n\t\t\t\t\t\t\tif (precondition && node.href.startsWith('command:')) {\n\t\t\t\t\t\t\t\tconst updateEnablement = () => link.enabled = this.contextKeyService.contextMatchesRules(precondition);\n\t\t\t\t\t\t\t\tupdateEnablement();\n\n\t\t\t\t\t\t\t\tconst keys = new Set();\n\t\t\t\t\t\t\t\tprecondition.keys().forEach(key => keys.add(key));\n\t\t\t\t\t\t\t\tconst onDidChangeContext = Event.filter(this.contextKeyService.onDidChangeContext, e => e.affectsSome(keys));\n\t\t\t\t\t\t\t\tonDidChangeContext(updateEnablement, null, disposables);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.scrollableElement.scanDomNode();\n\t\tthis.viewWelcomeDisposable = disposables;\n\t}\n\n\tshouldShowWelcome(): boolean {\n\t\treturn false;\n\t}\n\n\tgetFilterWidget()", "fileName": "./1.tst"}, "modified": {"content": "\n\tlayout(height: number, width: number) {\n\t\tif (!this.enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.element!.style.height = `${height}px`;\n\t\tthis.element!.style.width = `${width}px`;\n\t\tthis.element!.classList.toggle('wide', width > 640);\n\t\tthis.scrollableElement!.scanDomNode();\n\t}\n\n\tfocus() {\n\t\tif (!this.enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.element!.focus();\n\t}\n\n\tprivate onDidChangeViewWelcomeState(): void {\n\t\tconst enabled = this.delegate.shouldShowWelcome();\n\n\t\tif (this.enabled === enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.enabled = enabled;\n\n\t\tif (!enabled) {\n\t\t\tthis.enabledDisposables.clear();\n\t\t\treturn;\n\t\t}\n\n\t\tthis.container.classList.add('welcome');\n\t\tconst viewWelcomeContainer = append(this.container, $('.welcome-view'));\n\t\tthis.element = $('.welcome-view-content', { tabIndex: 0 });\n\t\tthis.scrollableElement = new DomScrollableElement(this.element, { alwaysConsumeMouseWheel: true, horizontal: ScrollbarVisibility.Hidden, vertical: ScrollbarVisibility.Visible, });\n\t\tappend(viewWelcomeContainer, this.scrollableElement.getDomNode());\n\n\t\tthis.enabledDisposables.add(toDisposable(() => {\n\t\t\tthis.container.classList.remove('welcome');\n\t\t\tthis.scrollableElement!.dispose();\n\t\t\tviewWelcomeContainer.remove();\n\t\t\tthis.scrollableElement = undefined;\n\t\t\tthis.element = undefined;\n\t\t}));\n\n\t\tthis.contextKeyService.onDidChangeContext(this.onDidChangeContext, this, this.enabledDisposables);\n\t\tEvent.chain(viewsRegistry.onDidChangeViewWelcomeContent, $ => $.filter(id => id === this.delegate.id))\n\t\t\t(this.onDidChangeViewWelcomeContent, this, this.enabledDisposables);\n\t\tthis.onDidChangeViewWelcomeContent();\n\t}\n\n\tprivate onDidChangeViewWelcomeContent(): void {\n\t\tconst descriptors = viewsRegistry.getViewWelcomeContent(this.delegate.id);\n\n\t\tthis.items = [];\n\n\t\tfor (const descriptor of descriptors) {\n\t\t\tif (descriptor.when === 'default') {\n\t\t\t\tthis.defaultItem = { descriptor, visible: true };\n\t\t\t} else {\n\t\t\t\tconst visible = descriptor.when ? this.contextKeyService.contextMatchesRules(descriptor.when) : true;\n\t\t\t\tthis.items.push({ descriptor, visible });\n\t\t\t}\n\t\t}\n\n\t\tthis.render();\n\t}\n\n\tprivate onDidChangeContext(): void {\n\t\tlet didChange = false;\n\n\t\tfor (const item of this.items) {\n\t\t\tif (!item.descriptor.when || item.descriptor.when === 'default') {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tconst visible = this.contextKeyService.contextMatchesRules(item.descriptor.when);\n\n\t\t\tif (item.visible === visible) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\titem.visible = visible;\n\t\t\tdidChange = true;\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.render();\n\t\t}\n\t}\n\n\tprivate render(): void {\n\t\tthis.renderDisposables.clear();\n\n\t\tconst contents = this.getContentDescriptors();\n\n\t\tif (contents.length === 0) {\n\t\t\tthis.container.classList.remove('welcome');\n\t\t\tthis.element!.innerText = '';\n\t\t\tthis.scrollableElement!.scanDomNode();\n\t\t\treturn;\n\t\t}\n\n\t\tthis.container.classList.add('welcome');\n\t\tthis.element!.innerText = '';\n\n\t\tfor (const { content, precondition } of contents) {\n\t\t\tconst lines = content.split('\\n');\n\n\t\t\tfor (let line of lines) {\n\t\t\t\tline = line.trim();\n\n\t\t\t\tif (!line) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst linkedText = parseLinkedText(line);\n\n\t\t\t\tif (linkedText.nodes.length === 1 && typeof linkedText.nodes[0] !== 'string') {\n\t\t\t\t\tconst node = linkedText.nodes[0];\n\t\t\t\t\tconst buttonContainer = append(this.element!, $('.button-container'));\n\t\t\t\t\tconst button = new Button(buttonContainer, { title: node.title, supportIcons: true, ...defaultButtonStyles });\n\t\t\t\t\tbutton.label = node.label;\n\t\t\t\t\tbutton.onDidClick(_ => {\n\t\t\t\t\t\tthis.telemetryService.publicLog2<{ viewId: string; uri: string }, WelcomeActionClassification>('views.welcomeAction', { viewId: this.delegate.id, uri: node.href });\n\t\t\t\t\t\tthis.openerService.open(node.href, { allowCommands: true });\n\t\t\t\t\t}, null, this.renderDisposables);\n\t\t\t\t\tthis.renderDisposables.add(button);\n\n\t\t\t\t\tif (precondition) {\n\t\t\t\t\t\tconst updateEnablement = () => button.enabled = this.contextKeyService.contextMatchesRules(precondition);\n\t\t\t\t\t\tupdateEnablement();\n\n\t\t\t\t\t\tconst keys = new Set(precondition.keys());\n\t\t\t\t\t\tconst onDidChangeContext = Event.filter(this.contextKeyService.onDidChangeContext, e => e.affectsSome(keys));\n\t\t\t\t\t\tonDidChangeContext(updateEnablement, null, this.renderDisposables);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconst p = append(this.element!, $('p'));\n\n\t\t\t\t\tfor (const node of linkedText.nodes) {\n\t\t\t\t\t\tif (typeof node === 'string') {\n\t\t\t\t\t\t\tappend(p, document.createTextNode(node));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst link = this.renderDisposables.add(this.instantiationService.createInstance(Link, p, node, {}));\n\n\t\t\t\t\t\t\tif (precondition && node.href.startsWith('command:')) {\n\t\t\t\t\t\t\t\tconst updateEnablement = () => link.enabled = this.contextKeyService.contextMatchesRules(precondition);\n\t\t\t\t\t\t\t\tupdateEnablement();\n\n\t\t\t\t\t\t\t\tconst keys = new Set(precondition.keys());\n\t\t\t\t\t\t\t\tconst onDidChangeContext = Event.filter(this.contextKeyService.onDidChangeContext, e => e.affectsSome(keys));\n\t\t\t\t\t\t\t\tonDidChangeContext(updateEnablement, null, this.renderDisposables);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.scrollableElement!.scanDomNode();\n\t}\n\n\tprivate getContentDescriptors(): IViewContentDescriptor[] {\n\t\tconst visibleItems = this.items.filter(v => v.visible);\n\n\t\tif (visibleItems.length === 0 && this.defaultItem) {\n\t\t\treturn [this.defaultItem.descriptor];\n\t\t}\n\n\t\treturn visibleItems.map(v => v.descriptor);\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t}\n\t}\n\n\texport abstract class ViewPane extends Pane implements IView {\n\n\tprivate static readonly AlwaysShowActionsConfig = 'workbench.view.alwaysShowHeaderActions';\n\n\tprivate _onDidFocus = this._register(new Emitter<void>());\n\treadonly onDidFocus: Event<void> = this._onDidFocus.event;\n\n\tprivate _onDidBlur = this._register(new Emitter<void>());\n\treadonly onDidBlur: Event<void> = this._onDidBlur.event;\n\n\tprivate _onDidChangeBodyVisibility = this._register(new Emitter<boolean>());\n\treadonly onDidChangeBodyVisibility: Event<boolean> = this._onDidChangeBodyVisibility.event;\n\n\tprotected _onDidChangeTitleArea = this._register(new Emitter<void>());\n\treadonly onDidChangeTitleArea: Event<void> = this._onDidChangeTitleArea.event;\n\n\tprotected _onDidChangeViewWelcomeState = this._register(new Emitter<void>());\n\treadonly onDidChangeViewWelcomeState: Event<void> = this._onDidChangeViewWelcomeState.event;\n\n\tprivate _isVisible: boolean = false;\n\treadonly id: string;\n\n\tprivate _title: string;\n\tpublic get title(): string {\n\t\treturn this._title;\n\t}\n\n\tprivate _titleDescription: string | undefined;\n\tpublic get titleDescription(): string | undefined {\n\t\treturn this._titleDescription;\n\t}\n\n\treadonly menuActions: CompositeMenuActions;\n\n\tprivate progressBar!: ProgressBar;\n\tprivate progressIndicator!: IProgressIndicator;\n\n\tprivate toolbar?: WorkbenchToolBar;\n\tprivate readonly showActions: ViewPaneShowActions;\n\tprivate headerContainer?: HTMLElement;\n\tprivate titleContainer?: HTMLElement;\n\tprivate titleDescriptionContainer?: HTMLElement;\n\tprivate iconContainer?: HTMLElement;\n\tprotected twistiesContainer?: HTMLElement;\n\tprivate viewWelcomeController!: ViewWelcomeController;\n\n\tprotected readonly scopedContextKeyService: IContextKeyService;\n\n\tconstructor(\n\t\toptions: IViewPaneOptions,\n\t\t@IKeybindingService protected keybindingService: IKeybindingService,\n\t\t@IContextMenuService protected contextMenuService: IContextMenuService,\n\t\t@IConfigurationService protected readonly configurationService: IConfigurationService,\n\t\t@IContextKeyService protected contextKeyService: IContextKeyService,\n\t\t@IViewDescriptorService protected viewDescriptorService: IViewDescriptorService,\n\t\t@IInstantiationService protected instantiationService: IInstantiationService,\n\t\t@IOpenerService protected openerService: IOpenerService,\n\t\t@IThemeService protected themeService: IThemeService,\n\t\t@ITelemetryService protected telemetryService: ITelemetryService,\n\t) {\n\t\tsuper({ ...options, ...{ orientation: viewDescriptorService.getViewLocationById(options.id) === ViewContainerLocation.Panel ? Orientation.HORIZONTAL : Orientation.VERTICAL } });\n\n\t\tthis.id = options.id;\n\t\tthis._title = options.title;\n\t\tthis._titleDescription = options.titleDescription;\n\t\tthis.showActions = options.showActions ?? ViewPaneShowActions.Default;\n\n\t\tthis.scopedContextKeyService = this._register(contextKeyService.createScoped(this.element));\n\t\tthis.scopedContextKeyService.createKey('view', this.id);\n\t\tconst viewLocationKey = this.scopedContextKeyService.createKey('viewLocation', ViewContainerLocationToString(viewDescriptorService.getViewLocationById(this.id)!));\n\t\tthis._register(Event.filter(viewDescriptorService.onDidChangeLocation, e => e.views.some(view => view.id === this.id))(() => viewLocationKey.set(ViewContainerLocationToString(viewDescriptorService.getViewLocationById(this.id)!))));\n\n\t\tthis.menuActions = this._register(this.instantiationService.createChild(new ServiceCollection([IContextKeyService, this.scopedContextKeyService])).createInstance(CompositeMenuActions, options.titleMenuId ?? MenuId.ViewTitle, MenuId.ViewTitleContext, { shouldForwardArgs: !options.donotForwardArgs }));\n\t\tthis._register(this.menuActions.onDidChange(() => this.updateActions()));\n\t}\n\n\toverride get headerVisible(): boolean {\n\t\treturn super.headerVisible;\n\t}\n\n\toverride set headerVisible(visible: boolean) {\n\t\tsuper.headerVisible = visible;\n\t\tthis.element.classList.toggle('merged-header', !visible);\n\t}\n\n\tsetVisible(visible: boolean): void {\n\t\tif (this._isVisible !== visible) {\n\t\t\tthis._isVisible = visible;\n\n\t\t\tif (this.isExpanded()) {\n\t\t\t\tthis._onDidChangeBodyVisibility.fire(visible);\n\t\t\t}\n\t\t}\n\t}\n\n\tisVisible(): boolean {\n\t\treturn this._isVisible;\n\t}\n\n\tisBodyVisible(): boolean {\n\t\treturn this._isVisible && this.isExpanded();\n\t}\n\n\toverride setExpanded(expanded: boolean): boolean {\n\t\tconst changed = super.setExpanded(expanded);\n\t\tif (changed) {\n\t\t\tthis._onDidChangeBodyVisibility.fire(expanded);\n\t\t}\n\t\tif (this.twistiesContainer) {\n\t\t\tthis.twistiesContainer.classList.remove(...ThemeIcon.asClassNameArray(this.getTwistyIcon(!expanded)));\n\t\t\tthis.twistiesContainer.classList.add(...ThemeIcon.asClassNameArray(this.getTwistyIcon(expanded)));\n\t\t}\n\t\treturn changed;\n\t}\n\n\toverride render(): void {\n\t\tsuper.render();\n\n\t\tconst focusTracker = trackFocus(this.element);\n\t\tthis._register(focusTracker);\n\t\tthis._register(focusTracker.onDidFocus(() => this._onDidFocus.fire()));\n\t\tthis._register(focusTracker.onDidBlur(() => this._onDidBlur.fire()));\n\t}\n\n\tprotected renderHeader(container: HTMLElement): void {\n\t\tthis.headerContainer = container;\n\n\t\tthis.twistiesContainer = append(container, $(ThemeIcon.asCSSSelector(this.getTwistyIcon(this.isExpanded()))));\n\n\t\tthis.renderHeaderTitle(container, this.title);\n\n\t\tconst actions = append(container, $('.actions'));\n\t\tactions.classList.toggle('show-always', this.showActions === ViewPaneShowActions.Always);\n\t\tactions.classList.toggle('show-expanded', this.showActions === ViewPaneShowActions.WhenExpanded);\n\t\tthis.toolbar = this.instantiationService.createInstance(WorkbenchToolBar, actions, {\n\t\t\torientation: ActionsOrientation.HORIZONTAL,\n\t\t\tactionViewItemProvider: action => this.getActionViewItem(action),\n\t\t\tariaLabel: nls.localize('viewToolbarAriaLabel', \"{0} actions\", this.title),\n\t\t\tgetKeyBinding: action => this.keybindingService.lookupKeybinding(action.id),\n\t\t\trenderDropdownAsChildElement: true,\n\t\t\tactionRunner: this.getActionRunner(),\n\t\t\tresetMenu: this.menuActions.menuId\n\t\t});\n\n\t\tthis._register(this.toolbar);\n\t\tthis.setActions();\n\n\t\tthis._register(addDisposableListener(actions, EventType.CLICK, e => e.preventDefault()));\n\n\t\tconst viewContainerModel = this.viewDescriptorService.getViewContainerByViewId(this.id);\n\t\tif (viewContainerModel) {\n\t\t\tthis._register(this.viewDescriptorService.getViewContainerModel(viewContainerModel).onDidChangeContainerInfo(({ title }) => this.updateTitle(this.title)));\n\t\t} else {\n\t\t\tconsole.error(`View container model not found for view ${this.id}`);\n\t\t}\n\n\t\tconst onDidRelevantConfigurationChange = Event.filter(this.configurationService.onDidChangeConfiguration, e => e.affectsConfiguration(ViewPane.AlwaysShowActionsConfig));\n\t\tthis._register(onDidRelevantConfigurationChange(this.updateActionsVisibility, this));\n\t\tthis.updateActionsVisibility();\n\t}\n\n\tprotected getTwistyIcon(expanded: boolean): ThemeIcon {\n\t\treturn expanded ? viewPaneContainerExpandedIcon : viewPaneContainerCollapsedIcon;\n\t}\n\n\toverride style(styles: IPaneStyles): void {\n\t\tsuper.style(styles);\n\n\t\tconst icon = this.getIcon();\n\t\tif (this.iconContainer) {\n\t\t\tconst fgColor = asCssValueWithDefault(styles.headerForeground, asCssVariable(foreground));\n\t\t\tif (URI.isUri(icon)) {\n\t\t\t\t// Apply background color to activity bar item provided with iconUrls\n\t\t\t\tthis.iconContainer.style.backgroundColor = fgColor;\n\t\t\t\tthis.iconContainer.style.color = '';\n\t\t\t} else {\n\t\t\t\t// Apply foreground color to activity bar items provided with codicons\n\t\t\t\tthis.iconContainer.style.color = fgColor;\n\t\t\t\tthis.iconContainer.style.backgroundColor = '';\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate getIcon(): ThemeIcon | URI {\n\t\treturn this.viewDescriptorService.getViewDescriptorById(this.id)?.containerIcon || defaultViewIcon;\n\t}\n\n\tprotected renderHeaderTitle(container: HTMLElement, title: string): void {\n\t\tthis.iconContainer = append(container, $('.icon', undefined));\n\t\tconst icon = this.getIcon();\n\n\t\tlet cssClass: string | undefined = undefined;\n\t\tif (URI.isUri(icon)) {\n\t\t\tcssClass = `view-${this.id.replace(/[\\.\\:]/g, '-')}`;\n\t\t\tconst iconClass = `.pane-header .icon.${cssClass}`;\n\n\t\t\tcreateCSSRule(iconClass, `\n\t\t\t\tmask: ${asCSSUrl(icon)} no-repeat 50% 50%;\n\t\t\t\tmask-size: 24px;\n\t\t\t\t-webkit-mask: ${asCSSUrl(icon)} no-repeat 50% 50%;\n\t\t\t\t-webkit-mask-size: 16px;\n\t\t\t`);\n\t\t} else if (ThemeIcon.isThemeIcon(icon)) {\n\t\t\tcssClass = ThemeIcon.asClassName(icon);\n\t\t}\n\n\t\tif (cssClass) {\n\t\t\tthis.iconContainer.classList.add(...cssClass.split(' '));\n\t\t}\n\n\t\tconst calculatedTitle = this.calculateTitle(title);\n\t\tthis.titleContainer = append(container, $('h3.title', { title: calculatedTitle }, calculatedTitle));\n\n\t\tif (this._titleDescription) {\n\t\t\tthis.setTitleDescription(this._titleDescription);\n\t\t}\n\n\t\tthis.iconContainer.title = calculatedTitle;\n\t\tthis.iconContainer.setAttribute('aria-label', calculatedTitle);\n\t}\n\n\tprotected updateTitle(title: string): void {\n\t\tconst calculatedTitle = this.calculateTitle(title);\n\t\tif (this.titleContainer) {\n\t\t\tthis.titleContainer.textContent = calculatedTitle;\n\t\t\tthis.titleContainer.setAttribute('title', calculatedTitle);\n\t\t}\n\n\t\tif (this.iconContainer) {\n\t\t\tthis.iconContainer.title = calculatedTitle;\n\t\t\tthis.iconContainer.setAttribute('aria-label', calculatedTitle);\n\t\t}\n\n\t\tthis._title = title;\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tprivate setTitleDescription(description: string | undefined) {\n\t\tif (this.titleDescriptionContainer) {\n\t\t\tthis.titleDescriptionContainer.textContent = description ?? '';\n\t\t\tthis.titleDescriptionContainer.setAttribute('title', description ?? '');\n\t\t}\n\t\telse if (description && this.titleContainer) {\n\t\t\tthis.titleDescriptionContainer = after(this.titleContainer, $('span.description', { title: description }, description));\n\t\t}\n\t}\n\n\tprotected updateTitleDescription(description?: string | undefined): void {\n\t\tthis.setTitleDescription(description);\n\n\t\tthis._titleDescription = description;\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tprivate calculateTitle(title: string): string {\n\t\tconst viewContainer = this.viewDescriptorService.getViewContainerByViewId(this.id)!;\n\t\tconst model = this.viewDescriptorService.getViewContainerModel(viewContainer);\n\t\tconst viewDescriptor = this.viewDescriptorService.getViewDescriptorById(this.id);\n\t\tconst isDefault = this.viewDescriptorService.getDefaultContainerById(this.id) === viewContainer;\n\n\t\tif (!isDefault && viewDescriptor?.containerTitle && model.title !== viewDescriptor.containerTitle) {\n\t\t\treturn `${viewDescriptor.containerTitle}: ${title}`;\n\t\t}\n\n\t\treturn title;\n\t}\n\n\tprotected renderBody(container: HTMLElement): void {\n\t\tthis.viewWelcomeController = this._register(new ViewWelcomeController(container, this, this.instantiationService, this.openerService, this.telemetryService, this.contextKeyService));\n\t}\n\n\tprotected layoutBody(height: number, width: number): void {\n\t\tthis.viewWelcomeController.layout(height, width);\n\t}\n\n\tonDidScrollRoot() {\n\t\t// noop\n\t}\n\n\tgetProgressIndicator() {\n\t\tif (this.progressBar === undefined) {\n\t\t\t// Progress bar\n\t\t\tthis.progressBar = this._register(new ProgressBar(this.element, defaultProgressBarStyles));\n\t\t\tthis.progressBar.hide();\n\t\t}\n\n\t\tif (this.progressIndicator === undefined) {\n\t\t\tconst that = this;\n\t\t\tthis.progressIndicator = new ScopedProgressIndicator(assertIsDefined(this.progressBar), new class extends AbstractProgressScope {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper(that.id, that.isBodyVisible());\n\t\t\t\t\tthis._register(that.onDidChangeBodyVisibility(isVisible => isVisible ? this.onScopeOpened(that.id) : this.onScopeClosed(that.id)));\n\t\t\t\t}\n\t\t\t}());\n\t\t}\n\t\treturn this.progressIndicator;\n\t}\n\n\tprotected getProgressLocation(): string {\n\t\treturn this.viewDescriptorService.getViewContainerByViewId(this.id)!.id;\n\t}\n\n\tprotected getBackgroundColor(): string {\n\t\tswitch (this.viewDescriptorService.getViewLocationById(this.id)) {\n\t\t\tcase ViewContainerLocation.Panel:\n\t\t\t\treturn PANEL_BACKGROUND;\n\t\t\tcase ViewContainerLocation.Sidebar:\n\t\t\tcase ViewContainerLocation.AuxiliaryBar:\n\t\t\t\treturn SIDE_BAR_BACKGROUND;\n\t\t}\n\n\t\treturn SIDE_BAR_BACKGROUND;\n\t}\n\n\tfocus(): void {\n\t\tif (this.shouldShowWelcome()) {\n\t\t\tthis.viewWelcomeController.focus();\n\t\t} else if (this.element) {\n\t\t\tthis.element.focus();\n\t\t\tthis._onDidFocus.fire();\n\t\t}\n\t}\n\n\tprivate setActions(): void {\n\t\tif (this.toolbar) {\n\t\t\tconst primaryActions = [...this.menuActions.getPrimaryActions()];\n\t\t\tif (this.shouldShowFilterInHeader()) {\n\t\t\t\tprimaryActions.unshift(VIEWPANE_FILTER_ACTION);\n\t\t\t}\n\t\t\tthis.toolbar.setActions(prepareActions(primaryActions), prepareActions(this.menuActions.getSecondaryActions()));\n\t\t\tthis.toolbar.context = this.getActionsContext();\n\t\t}\n\t}\n\n\tprivate updateActionsVisibility(): void {\n\t\tif (!this.headerContainer) {\n\t\t\treturn;\n\t\t}\n\t\tconst shouldAlwaysShowActions = this.configurationService.getValue<boolean>('workbench.view.alwaysShowHeaderActions');\n\t\tthis.headerContainer.classList.toggle('actions-always-visible', shouldAlwaysShowActions);\n\t}\n\n\tprotected updateActions(): void {\n\t\tthis.setActions();\n\t\tthis._onDidChangeTitleArea.fire();\n\t}\n\n\tgetActionViewItem(action: IAction, options?: IDropdownMenuActionViewItemOptions): IActionViewItem | undefined {\n\t\tif (action.id === VIEWPANE_FILTER_ACTION.id) {\n\t\t\tconst that = this;\n\t\t\treturn new class extends BaseActionViewItem {\n\t\t\t\tconstructor() { super(null, action); }\n\t\t\t\toverride setFocusable(): void { /* noop input elements are focusable by default */ }\n\t\t\t\toverride get trapsArrowNavigation(): boolean { return true; }\n\t\t\t\toverride render(container: HTMLElement): void {\n\t\t\t\t\tcontainer.classList.add('viewpane-filter-container');\n\t\t\t\t\tappend(container, that.getFilterWidget()!.element);\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t\treturn createActionViewItem(this.instantiationService, action, { ...options, ...{ menuAsChild: action instanceof SubmenuItemAction } });\n\t}\n\n\tgetActionsContext(): unknown {\n\t\treturn undefined;\n\t}\n\n\tgetActionRunner(): IActionRunner | undefined {\n\t\treturn undefined;\n\t}\n\n\tgetOptimalWidth(): number {\n\t\treturn 0;\n\t}\n\n\tsaveState(): void {\n\t\t// Subclasses to implement for saving state\n\t}\n\n\tshouldShowWelcome(): boolean {\n\t\treturn false;\n\t}\n\n\tgetFilterWidget()", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[1,3)", "modifiedRange": "[1,52)", "innerChanges": null}, {"originalRange": "[7,8)", "modifiedRange": "[56,57)", "innerChanges": [{"originalRange": "[7,64 -> 7,64]", "modifiedRange": "[56,64 -> 56,73]"}]}, {"originalRange": "[20,21)", "modifiedRange": "[69,70)", "innerChanges": [{"originalRange": "[20,8 -> 20,25]", "modifiedRange": "[69,8 -> 69,14]"}]}, {"originalRange": "[42,43)", "modifiedRange": "[91,172)", "innerChanges": null}, {"originalRange": "[44,44)", "modifiedRange": "[173,175)", "innerChanges": null}, {"originalRange": "[49,50)", "modifiedRange": "[180,181)", "innerChanges": [{"originalRange": "[49,1 -> 49,1]", "modifiedRange": "[180,1 -> 180,2]"}]}, {"originalRange": "[51,52)", "modifiedRange": "[182,183)", "innerChanges": [{"originalRange": "[51,1 -> 51,1]", "modifiedRange": "[182,1 -> 182,2]"}]}, {"originalRange": "[95,100)", "modifiedRange": "[226,227)", "innerChanges": [{"originalRange": "[95,1 -> 99,1]", "modifiedRange": "[226,1 -> 226,1]"}, {"originalRange": "[99,31 -> 99,31]", "modifiedRange": "[226,31 -> 226,32]"}]}, {"originalRange": "[129,131)", "modifiedRange": "[256,256)", "innerChanges": null}, {"originalRange": "[324,326)", "modifiedRange": "[449,449)", "innerChanges": null}, {"originalRange": "[327,342)", "modifiedRange": "[450,451)", "innerChanges": [{"originalRange": "[327,8 -> 329,9]", "modifiedRange": "[450,8 -> 450,8]"}, {"originalRange": "[329,24 -> 330,26]", "modifiedRange": "[450,23 -> 450,27]"}, {"originalRange": "[330,31 -> 331,28]", "modifiedRange": "[450,32 -> 450,32]"}, {"originalRange": "[331,47 -> 331,74]", "modifiedRange": "[450,51 -> 450,52]"}, {"originalRange": "[331,88 -> 333,9]", "modifiedRange": "[450,66 -> 450,74]"}, {"originalRange": "[333,13 -> 337,30]", "modifiedRange": "[450,78 -> 450,88]"}, {"originalRange": "[337,37 -> 339,5]", "modifiedRange": "[450,95 -> 450,96]"}, {"originalRange": "[339,8 -> 339,78]", "modifiedRange": "[450,99 -> 450,114]"}, {"originalRange": "[339,87 -> 340,3]", "modifiedRange": "[450,123 -> 450,137]"}, {"originalRange": "[340,8 -> 340,59]", "modifiedRange": "[450,142 -> 450,157]"}, {"originalRange": "[340,66 -> 341,26]", "modifiedRange": "[450,164 -> 450,183]"}]}, {"originalRange": "[345,351)", "modifiedRange": "[454,455)", "innerChanges": [{"originalRange": "[345,1 -> 346,2]", "modifiedRange": "[454,1 -> 454,1]"}, {"originalRange": "[346,24 -> 347,27]", "modifiedRange": "[454,23 -> 454,27]"}, {"originalRange": "[347,30 -> 348,53]", "modifiedRange": "[454,30 -> 454,43]"}, {"originalRange": "[348,60 -> 350,4 EOL]", "modifiedRange": "[454,50 -> 454,52 EOL]"}]}, {"originalRange": "[394,395)", "modifiedRange": "[498,499)", "innerChanges": [{"originalRange": "[394,24 -> 394,27]", "modifiedRange": "[498,24 -> 498,28]"}]}, {"originalRange": "[457,540)", "modifiedRange": "[561,561)", "innerChanges": null}]}