"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MemoryManager {
    constructor(context) {
        this.chatHistory = [];
        this.codeContexts = [];
        this.context = context;
        this.snapshotsPath = path.join(context.extensionPath, 'data', 'snapshots');
        // Ensure snapshots directory exists
        if (!fs.existsSync(this.snapshotsPath)) {
            fs.mkdirSync(this.snapshotsPath, { recursive: true });
        }
        // Load existing chat history from global state
        this.loadChatHistory();
    }
    addChatMessage(role, content, mode) {
        const message = {
            id: this.generateId(),
            role,
            content,
            timestamp: Date.now(),
            mode
        };
        this.chatHistory.push(message);
        // Keep only last 100 messages to prevent memory issues
        if (this.chatHistory.length > 100) {
            this.chatHistory = this.chatHistory.slice(-100);
        }
        this.saveChatHistory();
    }
    getChatHistory(mode) {
        if (mode) {
            return this.chatHistory.filter(msg => msg.mode === mode);
        }
        return this.chatHistory;
    }
    clearChatHistory(mode) {
        if (mode) {
            this.chatHistory = this.chatHistory.filter(msg => msg.mode !== mode);
        }
        else {
            this.chatHistory = [];
        }
        this.saveChatHistory();
    }
    addCodeContext(filePath, content, language) {
        const context = {
            filePath,
            content,
            language,
            timestamp: Date.now()
        };
        // Remove existing context for the same file
        this.codeContexts = this.codeContexts.filter(ctx => ctx.filePath !== filePath);
        this.codeContexts.push(context);
        // Keep only last 50 code contexts
        if (this.codeContexts.length > 50) {
            this.codeContexts = this.codeContexts.slice(-50);
        }
    }
    getCodeContexts() {
        return this.codeContexts;
    }
    async createSnapshot(name) {
        const snapshot = {
            id: this.generateId(),
            name,
            timestamp: Date.now(),
            workspaceState: this.context.workspaceState.keys().reduce((state, key) => {
                state[key] = this.context.workspaceState.get(key);
                return state;
            }, {}),
            chatHistory: [...this.chatHistory],
            codeContext: [...this.codeContexts]
        };
        const snapshotPath = path.join(this.snapshotsPath, `${snapshot.id}.json`);
        try {
            fs.writeFileSync(snapshotPath, JSON.stringify(snapshot, null, 2));
            return snapshot.id;
        }
        catch (error) {
            throw new Error(`Failed to create snapshot: ${error}`);
        }
    }
    async loadSnapshot(snapshotId) {
        const snapshotPath = path.join(this.snapshotsPath, `${snapshotId}.json`);
        try {
            if (!fs.existsSync(snapshotPath)) {
                throw new Error(`Snapshot not found: ${snapshotId}`);
            }
            const snapshotData = fs.readFileSync(snapshotPath, 'utf8');
            const snapshot = JSON.parse(snapshotData);
            // Restore chat history
            this.chatHistory = snapshot.chatHistory;
            this.saveChatHistory();
            // Restore code contexts
            this.codeContexts = snapshot.codeContext;
            // Restore workspace state
            for (const [key, value] of Object.entries(snapshot.workspaceState)) {
                await this.context.workspaceState.update(key, value);
            }
        }
        catch (error) {
            throw new Error(`Failed to load snapshot: ${error}`);
        }
    }
    listSnapshots() {
        try {
            const files = fs.readdirSync(this.snapshotsPath);
            const snapshots = [];
            for (const file of files) {
                if (file.endsWith('.json')) {
                    try {
                        const snapshotPath = path.join(this.snapshotsPath, file);
                        const snapshotData = fs.readFileSync(snapshotPath, 'utf8');
                        const snapshot = JSON.parse(snapshotData);
                        snapshots.push({
                            id: snapshot.id,
                            name: snapshot.name,
                            timestamp: snapshot.timestamp
                        });
                    }
                    catch (error) {
                        console.error(`Error reading snapshot ${file}:`, error);
                    }
                }
            }
            return snapshots.sort((a, b) => b.timestamp - a.timestamp);
        }
        catch (error) {
            console.error('Error listing snapshots:', error);
            return [];
        }
    }
    async deleteSnapshot(snapshotId) {
        const snapshotPath = path.join(this.snapshotsPath, `${snapshotId}.json`);
        try {
            if (fs.existsSync(snapshotPath)) {
                fs.unlinkSync(snapshotPath);
            }
        }
        catch (error) {
            throw new Error(`Failed to delete snapshot: ${error}`);
        }
    }
    loadChatHistory() {
        const history = this.context.globalState.get('chatHistory', []);
        this.chatHistory = history;
    }
    saveChatHistory() {
        this.context.globalState.update('chatHistory', this.chatHistory);
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    dispose() {
        this.saveChatHistory();
    }
}
exports.MemoryManager = MemoryManager;
//# sourceMappingURL=MemoryManager.js.map