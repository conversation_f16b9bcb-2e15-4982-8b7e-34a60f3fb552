{"version": 3, "file": "RAGManager.js", "sourceRoot": "", "sources": ["../../src/rag/RAGManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAyB;AACzB,2CAA6B;AAkB7B,MAAa,UAAU;IAMnB,YAAY,OAAgC;QAJpC,cAAS,GAAe,EAAE,CAAC;QAE3B,kBAAa,GAAY,KAAK,CAAC;QAGnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QAE/E,+BAA+B;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACzB,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI;YACA,0BAA0B;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,4CAA4C;YAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACzC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;SACjF;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAa,EAAE,OAAe,EAAE,MAAc,EAAE,QAAgB;QAC9E,MAAM,QAAQ,GAAa;YACvB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,KAAK;YACL,OAAO;YACP,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,6CAA6C;QAC7C,kDAAkD;QAClD,QAAQ,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,OAAO,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,QAAgB,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,OAAO,EAAE,CAAC;SACb;QAED,IAAI;YACA,kDAAkD;YAClD,oCAAoC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,KAAa,EAAE,YAAoB,IAAI;QAC5D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3D,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO,EAAE,CAAC;SACb;QAED,IAAI,OAAO,GAAG,6BAA6B,CAAC;QAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;YAChC,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;YACnC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YAE5C,IAAI,UAAU,GAAG,WAAW,GAAG,SAAS,EAAE;gBACtC,MAAM;aACT;YAED,OAAO,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC;YACvD,UAAU,IAAI,WAAW,CAAC;SAC7B;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAa,EAAE,KAAa;QACjD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,gCAAgC;YAChC,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACjC,KAAK,IAAI,EAAE,CAAC;aACf;YAED,gBAAgB;YAChB,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;YAC/E,KAAK,IAAI,OAAO,CAAC;YAEjB,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,sBAAsB;gBACtB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAEpE,OAAO,CAAC,IAAI,CAAC;oBACT,QAAQ,EAAE,GAAG;oBACb,KAAK;oBACL,aAAa;iBAChB,CAAC,CAAC;aACN;SACJ;QAED,OAAO,OAAO;aACT,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACzB,CAAC;IAEO,oBAAoB,CAAC,OAAe,EAAE,KAAa,EAAE,YAAoB,GAAG;QAChF,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;SAC1C;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,SAAS,CAAC,CAAC;QAExD,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,IAAY;QAC5C,iFAAiF;QACjF,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC7C,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;SAC3C;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,2CAA2C;QAC3C,MAAM,WAAW,GAAG;YAChB;gBACI,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE;;;;;;;;;;kCAUS;gBAClB,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;aACzB;YACD;gBACI,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE;;;;;;;;;;;;;;yBAcA;gBACT,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,QAAQ;aACrB;SACJ,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;YAC3B,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC5E;IACL,CAAC;IAEO,aAAa;QACjB,IAAI;YACA,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBAClC,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACrC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;SACvB;IACL,CAAC;IAEO,aAAa;QACjB,IAAI;YACA,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SAChF;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;SACnD;IACL,CAAC;IAEO,UAAU;QACd,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,OAAO;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;CACJ;AA7OD,gCA6OC"}